package com.lld.im.service.liveroom.sharding;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 直播间分片配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "liveroom.sharding")
public class LiveRoomShardingConfig {
    
    /**
     * 分片数量
     */
    private Integer shardCount = 4;
    
    /**
     * 分片策略：
     * - roomId: 基于直播间ID的哈希分片
     * - heat: 基于直播间热度的动态分片
     * - range: 基于直播间ID范围的分片
     */
    private String strategy = "roomId";
    
    /**
     * 是否启用分片
     */
    private Boolean enabled = true;
    
    /**
     * 获取分片策略类型
     */
    public ShardingStrategy getShardingStrategyEnum() {
        if ("heat".equalsIgnoreCase(strategy)) {
            return ShardingStrategy.HEAT;
        } else if ("range".equalsIgnoreCase(strategy)) {
            return ShardingStrategy.RANGE;
        } else {
            return ShardingStrategy.ROOM_ID;
        }
    }
    
    /**
     * 分片策略枚举
     */
    public enum ShardingStrategy {
        /**
         * 基于直播间ID的哈希分片
         */
        ROOM_ID,
        
        /**
         * 基于直播间热度的动态分片
         */
        HEAT,
        
        /**
         * 基于直播间ID范围的分片
         */
        RANGE
    }
} 