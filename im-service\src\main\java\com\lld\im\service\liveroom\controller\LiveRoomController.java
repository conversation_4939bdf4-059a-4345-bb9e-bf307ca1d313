package com.lld.im.service.liveroom.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.interceptor.UserPermissionCheck;
import com.lld.im.service.liveroom.model.req.CreateLiveRoomReq;
import com.lld.im.service.liveroom.model.req.JoinLiveRoomReq;
import com.lld.im.service.liveroom.model.req.SendLiveRoomMsgReq;
import com.lld.im.service.liveroom.model.req.UpdateAnnouncementReq;
import com.lld.im.service.liveroom.model.req.KickUserReq;
import com.lld.im.service.liveroom.model.req.CloseLiveRoomReq;
import com.lld.im.service.liveroom.model.req.MuteUserReq;
import com.lld.im.service.liveroom.model.req.MuteAllReq;
import com.lld.im.service.liveroom.model.req.LeaveLiveRoomReq;
import com.lld.im.service.liveroom.model.req.CheckLiveRoomExistsReq;
import com.lld.im.service.liveroom.model.resp.LiveRoomMsgResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomUserResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomExistsResp;
import com.lld.im.service.liveroom.service.LiveRoomService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 直播间控制器
 */
@Api(tags = "直播间管理", description = "直播间创建、加入、消息发送和管理相关接口")
@RestController
@RequestMapping("/v1/api/liveroom")
public class LiveRoomController extends BaseController {

    @Autowired
    private LiveRoomService liveRoomService;

    @ApiOperation(value = "创建直播间", notes = "创建一个新的直播间，返回直播间ID")
    @PostMapping("/create")
    @UserPermissionCheck("anchorId")
    public ResponseVO<String> createLiveRoom(
            @ApiParam(value = "创建直播间请求参数", required = true) @RequestBody @Validated CreateLiveRoomReq req) {
        fillCommonParams(req);
        return liveRoomService.createLiveRoom(req);
    }

    @ApiOperation(value = "加入直播间", notes = "用户加入指定的直播间，返回直播间详细信息")
    @PostMapping("/join")
    public ResponseVO<LiveRoomResp> joinLiveRoom(
            @ApiParam(value = "加入直播间请求参数", required = true) @RequestBody @Validated JoinLiveRoomReq req) {
        fillCommonParams(req);
        return liveRoomService.joinLiveRoom(req);
    }

    @ApiOperation(value = "离开直播间", notes = "用户离开指定的直播间")
    @PostMapping("/leave")
    public ResponseVO<Void> leaveLiveRoom(
            @ApiParam(value = "离开直播间请求参数", required = true) @RequestBody @Validated LeaveLiveRoomReq req) {
        fillCommonParams(req);
        return liveRoomService.leaveLiveRoom(req);
    }

    @ApiOperation(value = "发送直播间消息", notes = "在直播间内发送消息，支持文本、表情、礼物等多种类型")
    @PostMapping("/message/send")
    public ResponseVO<String> sendMessage(
            @ApiParam(value = "发送消息请求参数", required = true) @RequestBody @Validated SendLiveRoomMsgReq req) {
        fillCommonParams(req);
        return liveRoomService.sendMessage(req);
    }

    @ApiOperation(value = "获取直播间信息", notes = "获取指定直播间的详细信息，包括基本信息和在线用户列表")
    @GetMapping("/info")
    public ResponseVO<LiveRoomResp> getLiveRoomInfo(
            @ApiParam(value = "直播间ID", required = true, example = "room123") @RequestParam("roomId") String roomId) {
        Integer appId = getCurrentAppId();
        return liveRoomService.getLiveRoomInfo(roomId, appId);
    }

    @ApiOperation(value = "获取直播间最近消息", notes = "获取直播间的最近聊天消息记录")
    @GetMapping("/message/recent")
    public ResponseVO<List<LiveRoomMsgResp>> getRecentMessages(
            @ApiParam(value = "直播间ID", required = true, example = "room123") @RequestParam("roomId") String roomId,
            @ApiParam(value = "消息数量限制", example = "50") @RequestParam(value = "limit", required = false) Integer limit) {
        Integer appId = getCurrentAppId();
        return liveRoomService.getRecentMessages(roomId, appId, limit);
    }

    @ApiOperation(value = "禁言/解禁用户", notes = "对直播间内的指定用户进行禁言或解禁操作")
    @PostMapping("/mute/user")
    public ResponseVO<Void> muteUser(
            @ApiParam(value = "禁言用户请求参数", required = true) @RequestBody @Validated MuteUserReq req) {
        fillCommonParams(req);
        return liveRoomService.muteUser(req);
    }

    @ApiOperation(value = "全员禁言/解禁", notes = "对直播间进行全员禁言或解禁操作")
    @PostMapping("/mute/all")
    public ResponseVO<Void> muteAll(
            @ApiParam(value = "全员禁言请求参数", required = true) @RequestBody @Validated MuteAllReq req) {
        fillCommonParams(req);
        return liveRoomService.muteAll(req);
    }

    /**
     * 踢出用户
     */
    @ApiOperation(value = "踢出用户", notes = "将指定用户踢出直播间")
    @PostMapping("/kick")
    public ResponseVO<Void> kickUser(
            @ApiParam(value = "踢出用户请求参数", required = true) @RequestBody @Validated KickUserReq req) {
        fillCommonParams(req);
        return liveRoomService.kickUser(req);
    }

    /**
     * 更新直播间公告
     */
    @ApiOperation(value = "更新直播间公告", notes = "更新指定直播间的公告信息")
    @PostMapping("/announcement")
    public ResponseVO<Void> updateAnnouncement(
            @ApiParam(value = "更新公告请求参数", required = true) @RequestBody @Validated UpdateAnnouncementReq req
    ) {
        fillCommonParams(req);
        return liveRoomService.updateAnnouncement(req);
    }

    /**
     * 关闭直播间
     */
    @ApiOperation(value = "关闭直播间", notes = "关闭指定的直播间")
    @PostMapping("/close")
    public ResponseVO<Void> closeLiveRoom(
            @ApiParam(value = "关闭直播间请求参数", required = true) @RequestBody @Validated CloseLiveRoomReq req) {
        fillCommonParams(req);
        return liveRoomService.closeLiveRoom(req);
    }

    /**
     * 获取单个直播间的在线用户列表
     * 注意：此接口已加入鉴权白名单，需要在请求头中提供 X-App-Id 参数
     */
    @ApiOperation(value = "获取直播间在线用户列表", notes = "获取指定直播间的在线用户列表，直接从Redis缓存读取")
    @GetMapping("/users/online")
    public ResponseVO<List<LiveRoomUserResp>> getLiveRoomOnlineUsers(
            @ApiParam(value = "直播间ID", required = true, example = "room123") @RequestParam("roomId") String roomId) {
        Integer appId = getCurrentAppId();
        return liveRoomService.getLiveRoomOnlineUsers(roomId, appId);
    }

    /**
     * 批量获取多个直播间的用户列表信息
     * 注意：此接口已加入鉴权白名单，需要在请求头中提供 X-App-Id 参数
     */
    @ApiOperation(value = "批量获取直播间用户列表", notes = "批量获取多个直播间的用户列表信息，需要在请求头中提供 X-App-Id 参数")
    @PostMapping("/batch/users")
    public ResponseVO<Map<String, List<LiveRoomUserResp>>> batchGetLiveRoomUsers(
            @ApiParam(value = "直播间ID列表", required = true) @RequestBody List<String> roomIds) {
        Integer appId = getCurrentAppId();
        return liveRoomService.batchGetLiveRoomUsers(roomIds, appId);
    }

    /**
     * 批量检查直播间是否存在
     */
    @ApiOperation(value = "批量检查直播间是否存在",
                 notes = "批量检查一个或多个直播间是否存在于系统中，返回每个直播间的存在状态和基本信息")
    @PostMapping("/check-exists")
    public ResponseVO<List<LiveRoomExistsResp>> checkLiveRoomExists(
            @ApiParam(value = "批量检查直播间存在状态请求参数", required = true)
            @RequestBody @Validated CheckLiveRoomExistsReq req) {
        fillCommonParams(req);
        return liveRoomService.checkLiveRoomExists(req);
    }
}
