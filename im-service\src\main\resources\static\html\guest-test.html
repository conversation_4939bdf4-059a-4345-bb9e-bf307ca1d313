<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游客WebSocket测试页面</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-bar {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #dc3545;
            margin-right: 10px;
            transition: background-color 0.3s;
        }
        
        .status-indicator.connected {
            background-color: #28a745;
        }
        
        .buttons {
            display: flex;
            margin-bottom: 20px;
        }
        
        button {
            margin-right: 10px;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
        }
        
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        button.disconnect {
            background-color: #dc3545;
        }
        
        .log-container {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            border-bottom: 1px solid #e9ecef;
            padding-bottom: 5px;
        }
        
        .log-time {
            color: #6c757d;
            margin-right: 5px;
        }
        
        .log-info {
            color: #17a2b8;
        }
        
        .log-success {
            color: #28a745;
        }
        
        .log-error {
            color: #dc3545;
        }
        
        .notifications-container {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            background-color: white;
        }
        
        .notification {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
            background-color: #e2f3fd;
            border-left: 4px solid #007bff;
            animation: fadeIn 0.5s;
        }
        
        .notification-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .notification-content {
            margin-bottom: 5px;
        }
        
        .notification-time {
            font-size: 12px;
            color: #6c757d;
            text-align: right;
        }
        
        .empty-notice {
            text-align: center;
            color: #6c757d;
            padding: 20px;
        }
        
        .connection-options {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        
        .connection-options a {
            margin-right: 10px;
            color: #007bff;
            text-decoration: none;
        }
        
        .connection-options a:hover {
            text-decoration: underline;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
        }
        
        .config-panel {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .config-title {
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 5px;
        }
        
        .config-note {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>游客WebSocket测试页面</h1>
        
        <div class="status-bar">
            <div class="status-indicator" id="status-indicator"></div>
            <div id="status-text">未连接</div>
        </div>
        
        <div class="config-panel">
            <div class="config-title">连接配置</div>
            <div class="form-group">
                <label for="appId">应用ID (appId)</label>
                <input type="number" id="appId" value="10000" readonly>
            </div>
            <div class="form-group">
                <label for="clientType">客户端类型 (clientType)</label>
                <input type="number" id="clientType" value="1" readonly>
            </div>
            <small class="config-note">注意: 已配置正确的固定参数，appId=10000, clientType=1</small>
        </div>
        
        <div class="connection-options">
            <span>连接选项: </span>
            <a href="#" id="connect-ip">*********:19000</a>
            <a href="#" id="connect-custom">自定义连接...</a>
        </div>
        
        <div class="buttons">
            <button id="connect-btn">连接WebSocket</button>
            <button id="disconnect-btn" class="disconnect" disabled>断开连接</button>
            <button id="clear-log-btn">清空日志</button>
            <button id="test-notification-btn" disabled>测试系统通知</button>
        </div>
        
        <h3>连接日志</h3>
        <div class="log-container" id="log-container"></div>
        
        <h3>系统公告 (完整二进制帧格式)</h3>
        <div class="notifications-container" id="notifications-container">
            <div class="empty-notice">暂无系统公告，请等待消息推送或点击"测试系统通知"按钮...<br>
            <small>现在支持完整的WebSocket报文格式，包含MessageHeader和完整的消息体</small></div>
        </div>
    </div>

    <script src="../js/guest-connection-example.js"></script>
    <script>
        // DOM元素
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');
        const clearLogBtn = document.getElementById('clear-log-btn');
        const testNotificationBtn = document.getElementById('test-notification-btn');
        const logContainer = document.getElementById('log-container');
        const notificationsContainer = document.getElementById('notifications-container');
        const appIdInput = document.getElementById('appId');
        const clientTypeInput = document.getElementById('clientType');
        
        // 连接选项
        const connectLocalhost = document.getElementById('connect-localhost');
        const connect127 = document.getElementById('connect-127');
        const connectIp = document.getElementById('connect-ip');
        const connectCustom = document.getElementById('connect-custom');
        
        // WebSocket客户端
        let wsClient = null;
        let currentUrl = 'ws://*********:19000/ws';
        
        // 事件监听
        connectBtn.addEventListener('click', () => connectWs(currentUrl));
        disconnectBtn.addEventListener('click', disconnectWs);
        clearLogBtn.addEventListener('click', clearLog);
        testNotificationBtn.addEventListener('click', testSystemNotification);
        
        // 检查DOM元素是否存在再添加事件监听
        if (connectLocalhost) {
            connectLocalhost.addEventListener('click', (e) => {
                e.preventDefault();
                currentUrl = 'ws://localhost:19000/ws';
                addLog('info', `设置连接URL: ${currentUrl}`);
            });
        }
        
        if (connect127) {
            connect127.addEventListener('click', (e) => {
                e.preventDefault();
                currentUrl = 'ws://127.0.0.1:19000/ws';
                addLog('info', `设置连接URL: ${currentUrl}`);
            });
        }
        
        if (connectIp) {
            connectIp.addEventListener('click', (e) => {
                e.preventDefault();
                currentUrl = 'ws://*********:19000/ws';
                addLog('info', `设置连接URL: ${currentUrl}`);
            });
        }
        
        if (connectCustom) {
            connectCustom.addEventListener('click', (e) => {
                e.preventDefault();
                const customUrl = prompt('请输入WebSocket连接地址:', 'ws://localhost:9000/ws');
                if (customUrl) {
                    currentUrl = customUrl;
                    addLog('info', `设置自定义连接URL: ${currentUrl}`);
                }
            });
        }
        
        // 连接WebSocket
        function connectWs(url) {
            if (wsClient) {
                wsClient.close();
                wsClient = null;
            }
            
            // 使用固定的appId和clientType参数
            const appId = 10000;
            const clientType = 1;
            
            addLog('info', `正在连接到 ${url}...`);
            addLog('info', `配置: appId=${appId}, clientType=${clientType}`);
            statusText.textContent = '正在连接...';
            
            // 添加更详细的连接日志
            console.log('正在创建WebSocket客户端:', {
                isGuest: true,
                appId: appId,
                clientType: clientType,
                url: url
            });
            
            wsClient = new ImWebSocketClient({
                isGuest: true,
                appId: appId,                 // 固定使用appId=10000
                clientType: clientType,       // 固定使用clientType=1
                imei: 'web',                  // 设置imei为web
                reconnectInterval: 3000,
                maxReconnectAttempts: 5,
                heartbeatInterval: 30000,
                onConnectSuccess: () => {
                    statusIndicator.classList.add('connected');
                    statusText.textContent = '已连接';
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    testNotificationBtn.disabled = false;
                    addLog('success', '连接成功!');

                    // 显示客户端信息
                    if (wsClient && wsClient.guestId) {
                        addLog('info', `游客ID: ${wsClient.guestId}`);
                    }
                },
                onMessage: (data) => {
                    try {
                        // 检查是否为二进制数据或Blob
                        if (data instanceof Blob) {
                            addLog('info', `收到Blob数据，长度: ${data.size} 字节`);
                        } else if (data instanceof ArrayBuffer) {
                            addLog('info', `收到二进制数据，长度: ${data.byteLength} 字节`);
                        } else {
                            // JSON对象或文本数据
                            if (typeof data === 'string') {
                                addLog('info', `收到文本消息: ${data}`);
                            } else if (data.command) {
                                // 已解析的二进制帧消息
                                addLog('info', `收到二进制帧消息: command=${data.command}`);
                                if (data.command === 9004) {
                                    addLog('success', '✅ 系统通知使用二进制帧格式');
                                    addLog('info', `📋 完整消息结构: ${Object.keys(data).join(', ')}`);
                                }
                            } else {
                                addLog('info', `收到消息: ${JSON.stringify(data)}`);
                            }
                        }
                    } catch (error) {
                        addLog('error', `处理消息异常: ${error.message}`);
                        console.error('处理消息异常:', error, data);
                    }
                },
                onClose: () => {
                    updateDisconnectedStatus();
                    addLog('info', '连接已关闭');
                },
                onError: (error) => {
                    updateDisconnectedStatus();
                    addLog('error', `连接错误: ${error}`);
                }
            });
            
            // 注册系统通知处理器
            wsClient.registerMessageHandler(9004, (data) => {
                addLog('success', `收到系统通知 (二进制帧): command=${data.command}`);
                addLog('info', `消息头部信息: appId=${data.appId}, clientType=${data.clientType}, imei=${data.imei}`);
                addLog('info', `时间戳: ${data.timestamp ? new Date(data.timestamp).toLocaleString() : '未提供'}`);
                addLog('info', `通知数据: ${JSON.stringify(data.data)}`);
                addNotification(data.data);
            });
            
            wsClient.connect(url);
        }
        
        // 断开WebSocket连接
        function disconnectWs() {
            if (!wsClient) return;
            
            wsClient.close();
            wsClient = null;
            updateDisconnectedStatus();
            addLog('info', '已断开连接');
        }
        
        // 更新断开连接状态
        function updateDisconnectedStatus() {
            statusIndicator.classList.remove('connected');
            statusText.textContent = '未连接';
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            testNotificationBtn.disabled = true;
        }
        
        // 添加日志条目
        function addLog(type, message) {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            
            const now = new Date();
            const timeStr = now.toLocaleTimeString() + '.' + now.getMilliseconds().toString().padStart(3, '0');
            
            logEntry.innerHTML = `<span class="log-time">[${timeStr}]</span><span class="log-${type}">${message}</span>`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            logContainer.innerHTML = '';
        }
        
        // 添加通知
        function addNotification(notification) {
            // 移除空通知提示
            const emptyNotice = notificationsContainer.querySelector('.empty-notice');
            if (emptyNotice) {
                notificationsContainer.removeChild(emptyNotice);
            }
            
            const type = notification.notificationType || 1;
            const title = notification.title || '系统通知';
            const content = notification.content || '';
            
            const notificationDiv = document.createElement('div');
            notificationDiv.className = 'notification';
            notificationDiv.innerHTML = `
                <div class="notification-title">${title}</div>
                <div class="notification-content">${content}</div>
                <div class="notification-time">${new Date().toLocaleTimeString()}</div>
            `;
            
            notificationsContainer.appendChild(notificationDiv);
            notificationsContainer.scrollTop = notificationsContainer.scrollHeight;
        }
        
        // 测试系统通知
        function testSystemNotification() {
            if (!wsClient) {
                addLog('error', '请先连接WebSocket');
                return;
            }

            addLog('info', '发送测试系统通知请求到服务器...');

            // 模拟完整的系统通知消息格式（与服务端发送的格式一致）
            const testNotificationData = {
                title: '测试系统通知',
                content: '这是一条测试的系统通知消息，用于验证完整的二进制帧格式是否正常工作。',
                notificationType: 1,
                timestamp: Date.now()
            };

            // 模拟完整的WebSocket消息结构
            const fullTestMessage = {
                command: 9004,
                appId: 10000,
                clientType: 1,
                imei: 'test-client',
                timestamp: Date.now(),
                data: testNotificationData
            };

            // 模拟收到系统通知
            addLog('info', '模拟收到完整格式的系统通知 (用于测试显示效果)');
            addLog('info', `📋 模拟消息结构: ${Object.keys(fullTestMessage).join(', ')}`);
            addLog('success', `收到系统通知 (二进制帧): command=${fullTestMessage.command}`);
            addLog('info', `消息头部信息: appId=${fullTestMessage.appId}, clientType=${fullTestMessage.clientType}, imei=${fullTestMessage.imei}`);
            addLog('info', `时间戳: ${new Date(fullTestMessage.timestamp).toLocaleString()}`);
            addLog('info', `通知数据: ${JSON.stringify(fullTestMessage.data)}`);

            addNotification(fullTestMessage.data);

            // 提示用户如何触发真实的系统通知
            addLog('info', '💡 要测试真实的系统通知，请通过管理后台或API发送系统通知');
        }

        // 初始化日志
        addLog('info', '页面加载完成，请设置正确的appId并选择连接方式');
        addLog('info', '🔧 已更新为支持完整的WebSocket报文格式');
        addLog('info', '📋 现在支持MessageHeader + MessagePack完整结构');
    </script>
</body>
</html> 