lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  # Redis集群模式配置 - 基于Docker集群部署
  redis:
    mode: cluster # 使用集群模式
    database: 0
    password: 123456
    timeout: 3000
    poolMinIdle: 8
    poolConnTimeout: 3000
    poolSize: 10

    cluster:
      nodes:
        - localhost:7000    # redis-node-1
        - localhost:7001    # redis-node-2
        - localhost:7002    # redis-node-3
      maxRedirects: 3      # 保留字段，当前Redisson版本使用默认值
      scanInterval: 1000   # 集群节点扫描间隔（毫秒）

  # RabbitMQ集群模式配置 - 基于Docker集群部署
  rabbitmq:
    # 集群模式配置
    addresses:
      - host: localhost
        port: 5672      # rabbitmq-node-1
      - host: localhost
        port: 5673      # rabbitmq-node-2
      - host: localhost
        port: 5674      # rabbitmq-node-3
    virtualHost: /
    userName: admin
    password: admin123
    connectionTimeout: 5000
    requestedHeartbeat: 30
    networkRecoveryInterval: 5000
    automaticRecoveryEnabled: true

  # Nacos集群配置示例
  nacosConfig:
    serverAddr: nacos1:8848,nacos2:8848,nacos3:8848
    namespace: im-system
    group: DEFAULT_GROUP
    username: nacos
    password: nacos
    connectTimeout: 3000
    readTimeout: 5000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId
