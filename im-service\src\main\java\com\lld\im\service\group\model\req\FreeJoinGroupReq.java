package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 自由加入群组请求
 */
@ApiModel(description = "自由加入群组请求模型")
@Data
public class FreeJoinGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要加入的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "群内昵称", example = "小明", notes = "用户在群内的显示昵称")
    private String alias;

    @ApiModelProperty(value = "扩展字段", example = "{\"source\": \"qr_code\", \"inviter\": \"user456\"}", notes = "扩展信息，JSON格式")
    private String extra;
}
