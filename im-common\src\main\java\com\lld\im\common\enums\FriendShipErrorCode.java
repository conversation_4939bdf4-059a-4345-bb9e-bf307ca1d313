package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

public enum FriendShipErrorCode implements ApplicationExceptionEnum {


    IMPORT_SIZE_BEYOND(30000,"friendship.import.size.beyond"),

    ADD_FRIEND_ERROR(30001,"friendship.add.failed"),

    TO_IS_YOUR_FRIEND(30002,"friendship.already.friend"),

    TO_IS_NOT_YOUR_FRIEND(30003,"friendship.not.friend"),

    FRIEND_IS_DELETED(30004,"friendship.deleted"),

    FRIEND_IS_BLACK(30006,"friendship.blacklisted"),

    TARGET_IS_BLACK_YOU(30007,"friendship.target.blacklisted.you"),

    REPEATSHIP_IS_NOT_EXIST(30008,"friendship.relation.not.exist"),

    ADD_BLACK_ERROR(30009,"friendship.add.black.failed"),

    FRIEND_IS_NOT_YOUR_BLACK(30010,"friendship.not.in.blacklist"),

    NOT_APPROVER_OTHER_MAN_REQUEST(30011,"friendship.cannot.approve.others"),

    FRIEND_REQUEST_IS_NOT_EXIST(30012,"friendship.request.not.exist"),

    FRIEND_SHIP_GROUP_CREATE_ERROR(30014,"friendship.group.create.failed"),

    FRIEND_SHIP_GROUP_IS_EXIST(30015,"friendship.group.already.exist"),

    FRIEND_SHIP_GROUP_IS_NOT_EXIST(30016,"friendship.group.not.exist"),

    NON_FRIEND_MSG_LIMIT_EXCEEDED(30017,"friendship.non.friend.msg.limit.exceeded"),

    ;

    private int code;
    private String messageKey;

    FriendShipErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case IMPORT_SIZE_BEYOND:
                return "导入數量超出上限";
            case ADD_FRIEND_ERROR:
                return "添加好友失败";
            case TO_IS_YOUR_FRIEND:
                return "对方已经是你的好友";
            case TO_IS_NOT_YOUR_FRIEND:
                return "对方不是你的好友";
            case FRIEND_IS_DELETED:
                return "好友已被删除";
            case FRIEND_IS_BLACK:
                return "好友已被拉黑";
            case TARGET_IS_BLACK_YOU:
                return "对方把你拉黑";
            case REPEATSHIP_IS_NOT_EXIST:
                return "关系链记录不存在";
            case ADD_BLACK_ERROR:
                return "添加黑名單失败";
            case FRIEND_IS_NOT_YOUR_BLACK:
                return "好友已經不在你的黑名單内";
            case NOT_APPROVER_OTHER_MAN_REQUEST:
                return "无法审批其他人的好友请求";
            case FRIEND_REQUEST_IS_NOT_EXIST:
                return "好友申请不存在";
            case FRIEND_SHIP_GROUP_CREATE_ERROR:
                return "好友分组创建失败";
            case FRIEND_SHIP_GROUP_IS_EXIST:
                return "好友分组已存在";
            case FRIEND_SHIP_GROUP_IS_NOT_EXIST:
                return "好友分组不存在";
            case NON_FRIEND_MSG_LIMIT_EXCEEDED:
                return "非好友私信发送次数已达上限，请等待对方回复后再发送";
            default:
                return this.messageKey;
        }
    }

}
