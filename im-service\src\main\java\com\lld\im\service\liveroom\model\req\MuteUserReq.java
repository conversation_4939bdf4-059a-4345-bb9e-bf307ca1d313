package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 禁言用户请求模型
 * 
 * @description: 直播间用户禁言操作的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "禁言用户请求模型")
@Data
public class MuteUserReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要执行禁言操作的直播间唯一标识")
    @NotBlank(message = "{validation.room.id.not.blank}")
    private String roomId;

    @ApiModelProperty(value = "被禁言用户ID", required = true, example = "user123", notes = "要被禁言的用户唯一标识")
    @NotBlank(message = "{validation.user.id.not.blank}")
    private String userId;

    @ApiModelProperty(value = "禁言状态", required = true, example = "1", notes = "1-禁言 0-解禁")
    @NotNull(message = "{validation.mute.status.not.null}")
    private Integer mute;

}
