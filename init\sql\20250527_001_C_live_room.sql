-- liquibase formatted sql
-- changeset im-core:20250527_001_C_live_room
-- 创建直播间表
-- 对应实体类：LiveRoom.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `room_name` varchar(100) NOT NULL COMMENT '直播间名称',
    `room_cover` varchar(255) DEFAULT NULL COMMENT '直播间封面',
    `status` int(1) DEFAULT '0' COMMENT '直播间状态 0-未开播 1-直播中 2-已结束',
    `anchor_id` varchar(50) NOT NULL COMMENT '主播ID',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `announcement` varchar(500) DEFAULT NULL COMMENT '直播间公告',
    `mute_all` int(1) DEFAULT '0' COMMENT '是否开启全员禁言 0-否 1-是',
    `need_review` int(1) DEFAULT '0' COMMENT '是否需要审核 0-否 1-是',
    `max_online_count` int(11) DEFAULT '0' COMMENT '最大在线人数',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_app_id` (`room_id`, `app_id`),
    KEY `idx_anchor_id` (`anchor_id`),
    KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间表';

-- 创建直播间成员表
-- 对应实体类：LiveRoomMember.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_member` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `role` int(1) DEFAULT '4' COMMENT '用户角色 1-主播 2-管理员 3-VIP用户 4-普通用户 5-游客',
    `mute` int(1) DEFAULT '0' COMMENT '是否被禁言 0-否 1-是',
    `mute_end_time` datetime DEFAULT NULL COMMENT '禁言到期时间',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '进入时间',
    `last_active_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后活跃时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_user_id_app_id` (`room_id`, `user_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_role` (`role`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间成员表';

-- 创建直播间消息表
-- 对应实体类：LiveRoomMessage.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_message` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `message_id` varchar(50) NOT NULL COMMENT '消息ID',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `from_id` varchar(50) NOT NULL COMMENT '发送者ID',
    `from_nickname` varchar(100) DEFAULT NULL COMMENT '发送者昵称',
    `from_avatar` varchar(255) DEFAULT NULL COMMENT '发送者头像',
    `message_type` int(1) NOT NULL COMMENT '消息类型 1-文本 2-图片 3-语音 4-视频 5-表情 6-礼物 7-点赞 8-系统消息 9-加入消息 10-离开消息',
    `content` text COMMENT '消息内容',
    `sequence` bigint(20) NOT NULL COMMENT '消息序列号',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `send_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发送时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `extra` varchar(1000) DEFAULT NULL COMMENT '扩展字段，用于存储额外信息（JSON格式）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_message_id_app_id` (`message_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_from_id` (`from_id`),
    KEY `idx_sequence` (`sequence`),
    KEY `idx_send_time` (`send_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间消息表';

-- 创建直播间礼物表
-- 对应实体类：LiveRoomGift.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_gift` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `gift_id` varchar(50) NOT NULL COMMENT '礼物ID',
    `gift_name` varchar(100) NOT NULL COMMENT '礼物名称',
    `gift_img` varchar(255) NOT NULL COMMENT '礼物图片',
    `gift_animation` varchar(255) DEFAULT NULL COMMENT '礼物动画',
    `gift_price` int(11) NOT NULL COMMENT '礼物价格(金币)',
    `gift_type` int(1) DEFAULT '1' COMMENT '礼物类型 1-普通 2-特效 3-专属',
    `status` int(1) DEFAULT '1' COMMENT '状态 0-下架 1-上架',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_gift_id_app_id` (`gift_id`, `app_id`),
    KEY `idx_status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间礼物表';

-- 创建直播间礼物记录表
-- 对应实体类：LiveRoomGiftRecord.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_gift_record` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `record_id` varchar(50) NOT NULL COMMENT '记录ID',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `gift_id` varchar(50) NOT NULL COMMENT '礼物ID',
    `sender_id` varchar(50) NOT NULL COMMENT '赠送者ID',
    `receiver_id` varchar(50) NOT NULL COMMENT '接收者ID',
    `gift_count` int(11) NOT NULL DEFAULT '1' COMMENT '礼物数量',
    `gift_price` int(11) NOT NULL COMMENT '礼物单价(金币)',
    `total_price` int(11) NOT NULL COMMENT '总价(金币)',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `send_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '赠送时间',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_record_id_app_id` (`record_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_sender_id` (`sender_id`),
    KEY `idx_receiver_id` (`receiver_id`),
    KEY `idx_send_time` (`send_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间礼物记录表';

-- 创建直播间禁言用户表
-- 对应实体类：LiveRoomMuteUser.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_mute_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `operator_id` varchar(50) NOT NULL COMMENT '操作者ID',
    `mute_end_time` datetime DEFAULT NULL COMMENT '禁言到期时间',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_user_id_app_id` (`room_id`, `user_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_mute_end_time` (`mute_end_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间禁言用户表';

-- 创建直播间管理员表
-- 对应实体类：LiveRoomAdmin.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_admin` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `operator_id` varchar(50) NOT NULL COMMENT '操作者ID',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_user_id_app_id` (`room_id`, `user_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间管理员表';

-- 创建直播间封禁用户表
-- 对应实体类：LiveRoomBlockedUser.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_blocked_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `user_id` varchar(50) NOT NULL COMMENT '用户ID',
    `operator_id` varchar(50) NOT NULL COMMENT '操作者ID',
    `block_end_time` datetime DEFAULT NULL COMMENT '封禁到期时间',
    `reason` varchar(255) DEFAULT NULL COMMENT '封禁原因',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_user_id_app_id` (`room_id`, `user_id`, `app_id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_block_end_time` (`block_end_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间封禁用户表';

-- 创建直播间统计表
-- 对应实体类：LiveRoomStats.java（已创建）
CREATE TABLE IF NOT EXISTS `im_live_room_stats` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `room_id` varchar(50) NOT NULL COMMENT '直播间ID',
    `online_count` int(11) DEFAULT '0' COMMENT '当前在线人数',
    `peak_online_count` int(11) DEFAULT '0' COMMENT '峰值在线人数',
    `total_viewer_count` int(11) DEFAULT '0' COMMENT '累计观看人数',
    `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
    `gift_count` int(11) DEFAULT '0' COMMENT '礼物数',
    `gift_value` bigint(20) DEFAULT '0' COMMENT '礼物总价值(金币)',
    `start_time` datetime DEFAULT NULL COMMENT '直播开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '直播结束时间',
    `duration` int(11) DEFAULT '0' COMMENT '直播时长(秒)',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_room_id_app_id` (`room_id`, `app_id`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_end_time` (`end_time`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播间统计表';
