package com.lld.im.common;

import com.lld.im.common.exception.ApplicationExceptionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ResponseVO<T> {

    private int code;

    private String msg;

    private T data;

    public static ResponseVO successResponse(Object data) {
        return new ResponseVO(200, getSuccessMessage(), data);
    }

    public static ResponseVO successResponse() {
        return new ResponseVO(200, getSuccessMessage());
    }

    public static ResponseVO errorResponse() {
        return new ResponseVO(500, getSystemErrorMessage());
    }

    /**
     * 获取成功消息（支持国际化）
     */
    private static String getSuccessMessage() {
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "common.success", new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认值
        }
        return "success";
    }

    /**
     * 获取系统错误消息（支持国际化）
     */
    private static String getSystemErrorMessage() {
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "common.system.error", new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回默认值
        }
        return "系统内部异常";
    }

    public static ResponseVO errorResponse(int code, String msg) {
        return new ResponseVO(code, msg);
    }

    public static ResponseVO errorResponse(ApplicationExceptionEnum enums) {
        return new ResponseVO(enums.getCode(), enums.getError());
    }

    public boolean isOk(){
        return this.code == 200;
    }


    public ResponseVO(int code, String msg) {
        this.code = code;
        this.msg = msg;
//		this.data = null;
    }

    public ResponseVO success(){
        this.code = 200;
        this.msg = "success";
        return this;
    }

    public ResponseVO success(T data){
        this.code = 200;
        this.msg = "success";
        this.data = data;
        return this;
    }
    
}
