package com.lld.im.service.liveroom.model.resp;

import lombok.Data;

import java.util.Date;

/**
 * 直播间用户信息响应
 */
@Data
public class LiveRoomUserResp {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户角色 1-主播 2-管理员 3-普通用户
     */
    private Integer role;

    /**
     * 是否被禁言 0-否 1-是
     */
    private Integer muted;

    /**
     * 进入直播间时间
     */
    private Date joinTime;
    
    /**
     * 用户等级
     */
    private Integer level;
    
    /**
     * 用户自定义标识
     */
    private String customTag;
} 