package com.lld.im.service.conversation.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Repository
public interface ImConversationSetMapper extends BaseMapper<ImConversationSetEntity> {

    @Update(" update im_conversation_set set readed_sequence = #{readedSequence}, sequence = #{sequence}, " +
            " last_message_sequence = CASE WHEN #{lastMessageSequence} > last_message_sequence OR last_message_sequence IS NULL " +
            " THEN #{lastMessageSequence} ELSE last_message_sequence END " +
            " where conversation_id = #{conversationId} and app_id = #{appId} AND readed_sequence < #{readedSequence}")
    public void readMark(ImConversationSetEntity imConversationSetEntity);

    @Select(" select COALESCE(max(sequence), 0) from im_conversation_set where app_id = #{appId} AND from_id = #{userId} ")
    Long geConversationSetMaxSeq(Integer appId, String userId);

    /**
     * 插入或更新会话记录，如果存在则更新sequence和lastMessageSequence字段
     */
    @Insert("<script>" +
            "INSERT INTO im_conversation_set " +
            "(conversation_id, conversation_type, from_id, to_id, is_mute, is_top, sequence, last_message_sequence, readed_sequence, app_id) " +
            "VALUES " +
            "(#{conversationId}, #{conversationType}, #{fromId}, #{toId}, #{isMute}, #{isTop}, #{sequence}, #{lastMessageSequence}, #{readedSequence}, #{appId}) " +
            "ON DUPLICATE KEY UPDATE " +
            "sequence = #{sequence}, last_message_sequence = #{lastMessageSequence}" +
            "</script>")
    void insertOrUpdateSequence(ImConversationSetEntity entity);

    /**
     * 批量插入或更新会话记录
     */
    @Insert("<script>" +
            "INSERT INTO im_conversation_set " +
            "(conversation_id, conversation_type, from_id, to_id, is_mute, is_top, sequence, last_message_sequence, readed_sequence, app_id) " +
            "VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.conversationId}, #{item.conversationType}, #{item.fromId}, #{item.toId}, " +
            "#{item.isMute}, #{item.isTop}, #{item.sequence}, #{item.lastMessageSequence}, #{item.readedSequence}, #{item.appId})" +
            "</foreach>" +
            "ON DUPLICATE KEY UPDATE " +
            "sequence = VALUES(sequence), last_message_sequence = VALUES(last_message_sequence)" +
            "</script>")
    void batchInsertOrUpdateSequence(@Param("list") List<ImConversationSetEntity> entities);

    /**
     * 更新会话的sequence和lastMessageSequence字段
     */
    @Update("UPDATE im_conversation_set SET sequence = #{conversationSequence}, last_message_sequence = #{messageSequence} " +
            "WHERE conversation_id = #{conversationId} AND app_id = #{appId}")
    void updateSequence(@Param("conversationId") String conversationId,
                       @Param("appId") Integer appId,
                       @Param("conversationSequence") Long conversationSequence,
                       @Param("messageSequence") Long messageSequence);

    /**
     * 只更新会话的lastMessageSequence字段，不更新sequence
     */
    @Update("UPDATE im_conversation_set SET last_message_sequence = #{messageSequence} " +
            "WHERE conversation_id = #{conversationId} AND app_id = #{appId}")
    void updateLastMessageSequence(@Param("conversationId") String conversationId,
                                  @Param("appId") Integer appId,
                                  @Param("messageSequence") Long messageSequence);

    /**
     * 只更新已读状态，不更新sequence字段
     */
    @Update("UPDATE im_conversation_set SET readed_sequence = #{readedSequence}, " +
            "last_message_sequence = CASE WHEN #{lastMessageSequence} > last_message_sequence OR last_message_sequence IS NULL " +
            "THEN #{lastMessageSequence} ELSE last_message_sequence END " +
            "WHERE conversation_id = #{conversationId} AND app_id = #{appId} AND readed_sequence < #{readedSequence}")
    void updateReadStatus(ImConversationSetEntity imConversationSetEntity);
}
