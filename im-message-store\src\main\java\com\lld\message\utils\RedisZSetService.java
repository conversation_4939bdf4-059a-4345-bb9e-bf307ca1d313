package com.lld.message.utils;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis ZSet操作服务
 * 使用Redisson原生API避免Spring Data Redis Template的兼容性问题
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class RedisZSetService {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 添加元素到有序集合
     * @param key 键
     * @param value 值
     * @param score 分数
     * @return 是否成功添加
     */
    public boolean add(String key, String value, double score) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.add(score, value);
        } catch (Exception e) {
            log.error("Redis ZSet add操作失败: key={}, value={}, score={}", key, value, score, e);
            return false;
        }
    }

    /**
     * 批量添加元素到有序集合
     * @param key 键
     * @param values 值和分数的映射
     * @return 添加的元素数量
     */
    public int addAll(String key, java.util.Map<String, Double> values) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            int count = 0;
            for (java.util.Map.Entry<String, Double> entry : values.entrySet()) {
                if (sortedSet.add(entry.getValue(), entry.getKey())) {
                    count++;
                }
            }
            return count;
        } catch (Exception e) {
            log.error("Redis ZSet addAll操作失败: key={}, size={}", key, values.size(), e);
            return 0;
        }
    }

    /**
     * 获取有序集合的大小
     * @param key 键
     * @return 集合大小
     */
    public long size(String key) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.size();
        } catch (Exception e) {
            log.error("Redis ZSet size操作失败: key={}", key, e);
            return 0;
        }
    }

    /**
     * 移除指定范围的元素（按排名）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置
     * @return 移除的元素数量
     */
    public long removeRange(String key, int start, int end) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.removeRangeByRank(start, end);
        } catch (Exception e) {
            log.error("Redis ZSet removeRange操作失败: key={}, start={}, end={}", key, start, end, e);
            return 0;
        }
    }

    /**
     * 获取指定范围的元素（按排名，正序）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置
     * @return 元素集合
     */
    public Collection<String> range(String key, int start, int end) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.valueRange(start, end);
        } catch (Exception e) {
            log.error("Redis ZSet range操作失败: key={}, start={}, end={}", key, start, end, e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取指定范围的元素（按排名，倒序）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置
     * @return 元素集合
     */
    public Collection<String> reverseRange(String key, int start, int end) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.valueRangeReversed(start, end);
        } catch (Exception e) {
            log.error("Redis ZSet reverseRange操作失败: key={}, start={}, end={}", key, start, end, e);
            return java.util.Collections.emptyList();
        }
    }

    /**
     * 获取指定范围的元素及分数（按排名，倒序）
     * @param key 键
     * @param start 开始位置
     * @param end 结束位置
     * @return 元素和分数的映射
     */
    public java.util.Map<String, Double> reverseRangeWithScores(String key, int start, int end) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.entryRangeReversed(start, end).stream()
                    .collect(java.util.stream.Collectors.toMap(
                            org.redisson.client.protocol.ScoredEntry::getValue,
                            org.redisson.client.protocol.ScoredEntry::getScore
                    ));
        } catch (Exception e) {
            log.error("Redis ZSet reverseRangeWithScores操作失败: key={}, start={}, end={}", key, start, end, e);
            return java.util.Collections.emptyMap();
        }
    }

    /**
     * 设置键的过期时间
     * @param key 键
     * @param timeout 超时时间
     * @param timeUnit 时间单位
     * @return 是否设置成功
     */
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.expire(timeout, timeUnit);
        } catch (Exception e) {
            log.error("Redis ZSet expire操作失败: key={}, timeout={}, unit={}", key, timeout, timeUnit, e);
            return false;
        }
    }

    /**
     * 删除键
     * @param key 键
     * @return 是否删除成功
     */
    public boolean delete(String key) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.delete();
        } catch (Exception e) {
            log.error("Redis ZSet delete操作失败: key={}", key, e);
            return false;
        }
    }

    /**
     * 按分数范围获取元素及分数
     * @param key 键
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @param offset 偏移量
     * @param count 数量限制
     * @return 元素和分数的映射
     */
    public java.util.Map<String, Double> rangeByScoreWithScores(String key, double minScore, double maxScore, int offset, int count) {
        try {
            RScoredSortedSet<String> sortedSet = redissonClient.getScoredSortedSet(key);
            return sortedSet.entryRange(minScore, true, maxScore, true, offset, count).stream()
                    .collect(java.util.stream.Collectors.toMap(
                            org.redisson.client.protocol.ScoredEntry::getValue,
                            org.redisson.client.protocol.ScoredEntry::getScore
                    ));
        } catch (Exception e) {
            log.error("Redis ZSet rangeByScoreWithScores操作失败: key={}, minScore={}, maxScore={}, offset={}, count={}",
                    key, minScore, maxScore, offset, count, e);
            return java.util.Collections.emptyMap();
        }
    }

    /**
     * 批量操作（事务性）
     * @param operations 批量操作
     */
    public void batchOperations(java.util.function.Consumer<RBatch> operations) {
        try {
            RBatch batch = redissonClient.createBatch();
            operations.accept(batch);
            batch.execute();
            log.debug("Redis ZSet批量操作执行成功");
        } catch (Exception e) {
            log.error("Redis ZSet批量操作失败", e);
        }
    }
}
