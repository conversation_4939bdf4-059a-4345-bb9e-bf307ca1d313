package com.lld.im.common;


import com.lld.im.common.exception.ApplicationExceptionEnum;


public enum BaseErrorCode implements ApplicationExceptionEnum {

    SUCCESS(200,"success"),
    SYSTEM_ERROR(90000,"system.error"),
    PARAMETER_ERROR(90001,"parameter.error"),
    METHOD_NOT_SUPPORTED(90002,"method.not.supported"),


            ;

    private int code;
    private String messageKey;

    BaseErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case SUCCESS:
                return "success";
            case SYSTEM_ERROR:
                return "服务器内部错误,请联系管理员";
            case PARAMETER_ERROR:
                return "参数校验错误";
            case METHOD_NOT_SUPPORTED:
                return "请求方法不支持";
            default:
                return this.messageKey;
        }
    }

}
