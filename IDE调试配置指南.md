# IM系统IDE调试配置指南

## 📋 调试环境概述

IM系统提供了完整的调试环境启动脚本，支持在IDE中进行远程调试。

### 🎯 调试端口分配
- **im-tcp**: 5005 (TCP连接服务)
- **im-service**: 5006 (核心业务服务)  
- **im-message-store**: 5007 (消息存储服务)

### 🚀 快速启动脚本
- `start-debug.ps1` - 完整调试环境启动脚本
- `debug-quick.ps1` - 快速调试启动脚本
- `start-debug.bat` - 图形菜单界面

## 🛠️ IDE配置指南

### IntelliJ IDEA配置

#### 1. 创建Remote JVM Debug配置
1. 打开 **Run/Debug Configurations**
2. 点击 **+** 添加新配置
3. 选择 **Remote JVM Debug**

#### 2. 配置调试参数
```
Name: IM-TCP Debug
Host: localhost
Port: 5005
Use module classpath: im-tcp
```

```
Name: IM-Service Debug  
Host: localhost
Port: 5006
Use module classpath: im-service
```

```
Name: IM-Message-Store Debug
Host: localhost
Port: 5007
Use module classpath: im-message-store
```

#### 3. 启动调试会话
1. 先运行调试启动脚本
2. 在IDE中启动对应的Debug配置
3. 设置断点开始调试

### Eclipse配置

#### 1. 创建Remote Java Application
1. 右键项目 → **Debug As** → **Debug Configurations**
2. 双击 **Remote Java Application** 创建新配置

#### 2. 配置连接参数
```
Name: IM-TCP Debug
Project: im-tcp
Connection Type: Standard (Socket Attach)
Host: localhost
Port: 5005
```

#### 3. 重复为其他服务创建配置

### VS Code配置

#### 1. 安装Java扩展包
- Extension Pack for Java
- Debugger for Java

#### 2. 配置launch.json
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Attach to IM-TCP",
            "request": "attach",
            "hostName": "localhost",
            "port": 5005
        },
        {
            "type": "java", 
            "name": "Attach to IM-Service",
            "request": "attach",
            "hostName": "localhost",
            "port": 5006
        },
        {
            "type": "java",
            "name": "Attach to IM-Message-Store", 
            "request": "attach",
            "hostName": "localhost",
            "port": 5007
        }
    ]
}
```

## 🎮 使用方法

### 方法1: 完整调试环境
```powershell
# 启动所有服务（暂停等待调试器）
.\start-debug.ps1

# 启动所有服务（不暂停）
.\start-debug.ps1 -NoSuspend

# 启动指定服务
.\start-debug.ps1 im-tcp im-service

# 顺序启动（避免端口冲突）
.\start-debug.ps1 -Sequential
```

### 方法2: 快速调试
```powershell
# 启动单个服务
.\debug-quick.ps1 im-tcp
.\debug-quick.ps1 im-service  
.\debug-quick.ps1 im-message-store

# 启动但不等待调试器
.\debug-quick.ps1 im-service -NoSuspend
```

### 方法3: 图形界面
```cmd
# 双击运行批处理文件
start-debug.bat
```

## 🔧 调试流程

### 标准调试流程
1. **构建项目**
   ```powershell
   .\build-simple.ps1 -SkipTests
   ```

2. **启动调试环境**
   ```powershell
   .\start-debug.ps1
   ```

3. **连接IDE调试器**
   - 在IDE中启动Remote Debug配置
   - 连接到对应端口

4. **设置断点并调试**
   - 在代码中设置断点
   - 触发相关功能进行调试

5. **停止调试**
   ```powershell
   .\stop-im-en.ps1 -Force
   ```

### 单服务调试流程
1. **启动单个服务**
   ```powershell
   .\debug-quick.ps1 im-service
   ```

2. **IDE连接调试**
   - 启动对应的Remote Debug配置

3. **开始调试**

## 📊 调试配置对照表

| 服务 | 调试端口 | JAR文件 | 配置文件 |
|------|---------|---------|----------|
| im-tcp | 5005 | im-tcp/target/im-tcp.jar | config-docker-cluster.yml |
| im-service | 5006 | im-service/target/im-service.jar | application.yml |
| im-message-store | 5007 | im-message-store/target/im-message-store.jar | application.yml |

## 🐛 常见问题解决

### 1. 端口被占用
```
错误: 端口 5005 已被占用
```
**解决方案**:
```powershell
# 停止现有服务
.\stop-im-en.ps1 -Force

# 或查看端口占用
netstat -ano | findstr :5005
```

### 2. JAR文件不存在
```
错误: JAR文件不存在
```
**解决方案**:
```powershell
# 重新构建项目
.\build-simple.ps1
```

### 3. IDE无法连接
**检查项**:
- 确认服务已启动且端口正确
- 检查防火墙设置
- 确认IDE配置的Host和Port正确

### 4. 调试器连接后服务立即退出
**原因**: 可能是suspend=y模式下，调试器断开连接导致
**解决方案**:
```powershell
# 使用非暂停模式启动
.\start-debug.ps1 -NoSuspend
```

## 📝 调试技巧

### 1. 热重载调试
- 使用IDE的热重载功能
- 修改代码后无需重启服务

### 2. 条件断点
- 在IDE中设置条件断点
- 只在特定条件下暂停执行

### 3. 表达式求值
- 在调试过程中求值表达式
- 查看变量状态和方法返回值

### 4. 多线程调试
- 注意IM系统的多线程特性
- 使用线程视图查看不同线程状态

## 🔄 调试环境管理

### 启动前检查
```powershell
# 检查构建状态
.\build-simple.ps1 -Help

# 检查端口状态
netstat -ano | findstr ":500"

# 检查现有进程
Get-Process java
```

### 日志查看
调试模式下的日志文件位置：
- `logs/im-tcp-debug.log`
- `logs/im-service-debug.log`  
- `logs/im-message-store-debug.log`

### 清理环境
```powershell
# 停止所有服务
.\stop-im-en.ps1 -Force

# 清理日志文件
Remove-Item logs/*-debug.log -ErrorAction SilentlyContinue
```

## 📚 相关文档

- `README-构建脚本.md` - 构建脚本使用指南
- `README-停止IM服务脚本.md` - 服务停止脚本说明
- `构建脚本总结.md` - 构建脚本功能总结

## 🎯 最佳实践

1. **开发调试**: 使用 `debug-quick.ps1` 启动单个服务
2. **集成调试**: 使用 `start-debug.ps1` 启动完整环境
3. **生产调试**: 谨慎使用调试模式，注意性能影响
4. **团队协作**: 统一调试端口配置，避免冲突
