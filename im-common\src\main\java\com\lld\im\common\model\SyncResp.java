package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 数据同步响应
 * @author: Chackylee
 * @description: 客户端数据同步响应模型
 **/
@ApiModel(description = "数据同步响应模型")
@Data
public class SyncResp<T> {

    @ApiModelProperty(value = "最大序列号", example = "1500", notes = "服务端当前最大的数据序列号")
    private Long maxSequence;

    @ApiModelProperty(value = "是否同步完成", example = "true", notes = "true-本次已同步完所有数据 false-还有更多数据需要继续同步")
    private boolean isCompleted;

    @ApiModelProperty(value = "同步数据列表", notes = "本次同步返回的数据列表")
    private List<T> dataList;

}
