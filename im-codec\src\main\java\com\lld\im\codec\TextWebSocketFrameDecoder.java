package com.lld.im.codec;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessageHeader;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageDecoder;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 文本WebSocket帧解码器
 * 处理客户端发送的JSON格式的文本消息
 */
public class TextWebSocketFrameDecoder extends MessageToMessageDecoder<TextWebSocketFrame> {
    
    private static final Logger logger = LoggerFactory.getLogger(TextWebSocketFrameDecoder.class);
    
    @Override
    protected void decode(ChannelHandlerContext ctx, TextWebSocketFrame frame, List<Object> out) throws Exception {
        String text = frame.text();
        logger.info("收到TextWebSocketFrame消息: {}", text);
        
        try {
            // 将JSON转换为对象
            JSONObject jsonObject = JSON.parseObject(text);
            
            // 创建消息头
            MessageHeader header = new MessageHeader();
            header.setCommand(jsonObject.getInteger("command"));
            header.setVersion(jsonObject.getInteger("version"));
            header.setClientType(jsonObject.getInteger("clientType"));
            header.setAppId(jsonObject.getInteger("appId"));
            header.setImei(jsonObject.getString("imei"));
            header.setMessageType(jsonObject.getInteger("messageType"));
            
            // 创建消息对象
            Message message = new Message();
            message.setMessageHeader(header);
            message.setMessagePack(jsonObject.get("data"));
            
            logger.info("解析TextWebSocketFrame成功: command={}, appId={}", header.getCommand(), header.getAppId());
            out.add(message);
        } catch (Exception e) {
            logger.error("解析TextWebSocketFrame失败: {}", e.getMessage());
        }
    }
} 