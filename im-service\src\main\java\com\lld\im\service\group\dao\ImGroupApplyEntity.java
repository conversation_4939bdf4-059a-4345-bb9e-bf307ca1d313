package com.lld.im.service.group.dao;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @description: 群申请记录实体类
 * @author: lld
 * @version: 1.0
 */
@Data
@TableName("im_group_apply_request")
public class ImGroupApplyEntity {

    @TableId(type = IdType.AUTO)
    private Long applyId;

    private Integer appId;

    private String groupId;

    /**
     * 申请人用户ID
     */
    private String applicantId;

    /**
     * 申请状态：0-待审批 1-已同意 2-已拒绝 3-已撤销
     */
    private Integer applyStatus;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 申请时间戳
     */
    private Long applyTime;

    /**
     * 审批人用户ID（群主或管理员）
     */
    private String approverId;

    /**
     * 审批时间戳
     */
    private Long approveTime;

    /**
     * 拒绝理由
     */
    private String rejectReason;

    /**
     * 序列号，用于数据同步
     */
    private Long sequence;

    /**
     * 记录创建时间戳
     */
    private Long createTime;

    /**
     * 记录更新时间戳
     */
    private Long updateTime;

    /**
     * 扩展字段，JSON格式
     */
    private String extra;
}
