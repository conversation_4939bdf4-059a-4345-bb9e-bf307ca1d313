package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 更新直播间公告请求
 */
@ApiModel(description = "更新直播间公告请求模型")
@Data
public class UpdateAnnouncementReq extends RequestBase {
    @ApiModelProperty(value = "直播间ID", required = true, example = "room123")
    @NotBlank(message = "{validation.room.id.not.blank}")
    private String roomId;

    @ApiModelProperty(value = "公告内容", required = true, example = "欢迎来到直播间！")
    @NotBlank(message = "{validation.announcement.not.blank}")
    private String announcement;
} 