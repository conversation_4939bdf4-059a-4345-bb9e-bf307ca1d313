package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: Chackylee
 * @description: 删除分组，同时删除分组下的成员
 **/
@ApiModel(description = "删除好友分组请求模型")
@Data
public class DeleteFriendShipGroupReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起删除分组操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "分组名称列表", required = true, example = "[\"同事\", \"同学\"]", notes = "要删除的好友分组名称列表")
    @NotEmpty(message = "分组名称不能为空")
    private List<String> groupName;

}
