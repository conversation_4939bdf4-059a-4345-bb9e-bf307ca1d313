package com.lld.im.service.friendship.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.friendship.model.req.AddFriendShipGroupMemberReq;
import com.lld.im.service.friendship.model.req.AddFriendShipGroupReq;
import com.lld.im.service.friendship.model.req.DeleteFriendShipGroupMemberReq;
import com.lld.im.service.friendship.model.req.DeleteFriendShipGroupReq;
import com.lld.im.service.friendship.service.ImFriendShipGroupMemberService;
import com.lld.im.service.friendship.service.ImFriendShipGroupService;
import com.lld.im.service.interceptor.UserPermissionCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @author: Chackylee
 * @description: 好友分组管理控制器
 **/
@Api(tags = "好友分组管理", description = "好友分组的创建、删除和成员管理相关接口")
@RestController
@RequestMapping("v1/friendship/group")
public class ImFriendShipGroupController extends BaseController {

    @Autowired
    ImFriendShipGroupService imFriendShipGroupService;

    @Autowired
    ImFriendShipGroupMemberService imFriendShipGroupMemberService;

    @ApiOperation(value = "创建好友分组", notes = "为用户创建新的好友分组")
    @PostMapping("/add")
    @UserPermissionCheck("fromId")
    public ResponseVO add(
            @ApiParam(value = "创建好友分组请求参数", required = true) @RequestBody @Validated AddFriendShipGroupReq req)  {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return imFriendShipGroupService.addGroup(req);
    }

    @ApiOperation(value = "删除好友分组", notes = "删除指定的好友分组")
    @DeleteMapping("/del")
    @UserPermissionCheck("fromId")
    public ResponseVO del(
            @ApiParam(value = "删除好友分组请求参数", required = true) @RequestBody @Validated DeleteFriendShipGroupReq req)  {
        fillCommonParams(req);
        return imFriendShipGroupService.deleteGroup(req);
    }

    @ApiOperation(value = "添加分组成员", notes = "将好友添加到指定分组中")
    @PostMapping("/member/add")
    @UserPermissionCheck("fromId")
    public ResponseVO memberAdd(
            @ApiParam(value = "添加分组成员请求参数", required = true) @RequestBody @Validated AddFriendShipGroupMemberReq req)  {
        fillCommonParams(req);
        return imFriendShipGroupMemberService.addGroupMember(req);
    }

    @ApiOperation(value = "移除分组成员", notes = "将好友从指定分组中移除")
    @DeleteMapping("/member/del")
    @UserPermissionCheck("fromId")
    public ResponseVO memberDel(
            @ApiParam(value = "移除分组成员请求参数", required = true) @RequestBody @Validated DeleteFriendShipGroupMemberReq req)  {
        fillCommonParams(req);
        return imFriendShipGroupMemberService.delGroupMember(req);
    }


}
