# 原始单机模式配置备份
# 此文件保留了修改前的原始配置，可用于回退到单机模式

spring:
  profiles:
    active: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: root
    url: ******************************************************************************************
    username: root

  redis:
    host: 127.0.0.1
    port: 6379
    jedis:
      pool:
        max-active: 100
        max-idle: 100
        max-wait: 1000
        min-idle: 10
    password: 123456
  rabbitmq:
    host: localhost
    port: 5672
    addresses: localhost
    username: guest
    password: guest
    #    virtual-host:
    listener:
      simple:
        concurrency: 5
        max-concurrency: 10
        acknowledge-mode: MANUAL
        prefetch: 1
        retry:
          enabled: true
          max-attempts: 3
    publisher-confirms: true
    publisher-returns: true
    template:
      mandatory: true
    cache:
      connection:
        mode: channel
      channel:
        size: 36
        checkout-timeout: 0
  application:
    name: im-core


# logger 配置
logging:
  config: classpath:logback-spring.xml

server:
  port: 8000

appConfig:
  appId: 10000
  privateKey: 123456
  # Nacos配置
  nacosServerAddr: 127.0.0.1:8848 # Nacos服务器地址
  nacosNamespace: im-system # Nacos命名空间
  nacosGroup: DEFAULT_GROUP # Nacos分组
  nacosUsername: nacos # Nacos用户名
  nacosPassword: nacos # Nacos密码
  # 路由策略配置保持不变
  imRouteWay: 1 # 路由策略1轮训 2随机 3hash
  consistentHashWay: 1 # 如果选用一致性hash的话具体hash算法 1 TreeMap 2 自定义Map
  tcpPort: 9000 # tcp端口
  webSocketPort: 19000 # webSocket端口
  needWebSocket: true #是否需要开启webSocket
  loginModel: 1
  messageRecallTimeOut : 1200000000 #消息可撤回时间，单位毫秒
  #  *                多端同步模式：1 只允许一端在线，手机/电脑/web 踢掉除了本client+imel的设备
  #  *                            2 允许手机/电脑的一台设备 + web在线 踢掉除了本client+imel的非web端设备
  #  *                            3 允许手机和电脑单设备 + web 同时在线 踢掉非本client+imel的同端设备
  #  *                            4 允许所有端多设备登录 不踢任何设备
  groupMaxMemberCount: 500
  sendMessageCheckFriend: false # 发送消息是否校验关系链
  sendMessageCheckBlack: false # 发送消息是否校验黑名单
  callbackUrl: http://127.0.0.1:8000/callback
  modifyUserAfterCallback: false # 用户资料变更之后回调开关
  addFriendAfterCallback: false # 添加好友之后回调开关
  addFriendBeforeCallback: false # 添加好友之前回调开关
  modifyFriendAfterCallback: false # 修改好友之后回调开关
  deleteFriendAfterCallback: false # 删除好友之后回调开关
  addFriendShipBlackAfterCallback: false #添加黑名单之后回调开关
  deleteFriendShipBlackAfterCallback: false #删除黑名单之后回调开关
  createGroupAfterCallback: false # 创建群聊之后回调开关
  modifyGroupAfterCallback: false # 修改群聊之后回调开关
  destroyGroupAfterCallback: false # 解散群聊之后回调开关
  deleteGroupMemberAfterCallback: false # 删除群成员之后回调
  addGroupMemberAfterCallback: false # 拉人入群之后回调
  addGroupMemberBeforeCallback: false # 拉人入群之前回调
  sendMessageAfterCallback: false # 发送单聊消息之后
  sendMessageBeforeCallback: false # 发送单聊消息之前
  sendGroupMessageAfterCallback: false # 发送群聊消息之后
  sendGroupMessageBeforeCallback: false # 发送群聊消息之前
  offlineMessageCount: 1000 #离线消息存储条数
  deleteConversationSyncMode: 1 #1多段同步
  enableNonFriendMsgLimit: false #非好友私信限制功能开关
  nonFriendMsgMaxCountBeforeReply: 1 #对方未回复时的最大消息数
  nonFriendMsgMaxCountAfterReply: 3 #对方回复后的最大消息数
  nonFriendMsgLimitExpireDays: 30 #非好友私信限制过期时间（天）


mqQueueName: 123

mybatis-plus:

  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_EMPTY

#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

httpclient:
  maxTotal: 100
  defaultMaxPerRoute: 50
  connectTimeout: 2000
  connectionRequestTimeout: 2000
  socketTimeout: 5000
  staleConnectionCheckEnabled: true

mpp:
  entityBasePath: com.lld.im.service.friendship.dao

# 添加直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId

im:
  mq:
    rabbitmq:
      notification:
        exchange: im.notification.exchange
        queue: im.notification.queue
        routing-key: im.notification
      liveroom:
        exchange: im.liveroom.exchange
        queue: im.liveroom.queue
        routing-key: im.liveroom
