package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取用户序列号请求
 * @description: 获取用户数据同步序列号
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "获取用户序列号请求模型")
@Data
public class GetUserSequenceReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要获取序列号的用户ID")
    private String userId;

}
