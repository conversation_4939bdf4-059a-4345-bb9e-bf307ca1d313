package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设置群组进群门槛请求
 * @author: lld
 * @description: 设置群组进群门槛的请求模型
 */
@ApiModel(description = "设置群组进群门槛请求模型")
@Data
public class SetGroupJoinThresholdReq extends RequestBase {
    
    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要设置门槛的群组ID")
    @NotBlank(message = "群组ID不能为空")
    private String groupId;
    
    @ApiModelProperty(value = "门槛类型", required = true, example = "1", 
                     notes = "1-无要求 2-关注我 3-关注我超过7天 4-关注我超过30天")
    @NotNull(message = "门槛类型不能为空")
    private Integer thresholdType;
}
