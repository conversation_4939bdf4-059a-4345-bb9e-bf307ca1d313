package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 发送直播间消息请求
 */
@ApiModel(description = "发送直播间消息请求模型")
@Data
public class SendLiveRoomMsgReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123")
    @NotBlank(message = "{validation.room.id.not.blank}")
    private String roomId;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123")
    @NotBlank(message = "{validation.from.id.not.blank}")
    private String fromId;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000")
    @NotNull(message = "{validation.app.id.not.null}")
    private Integer appId;

    @ApiModelProperty(value = "发送者昵称", example = "张三")
    private String fromNickname;

    @ApiModelProperty(value = "发送者头像URL", example = "https://example.com/avatar.jpg")
    private String fromAvatar;

    @ApiModelProperty(value = "消息类型 (1:文本 2:图片 3:语音 4:视频 5:表情 6:礼物 7:点赞 8:系统)", required = true, example = "1", notes = "直播间消息的类型标识")
    @NotNull(message = "{validation.message.type.not.null}")
    private Integer messageType;

    @ApiModelProperty(value = "消息内容", required = true, example = "大家好！")
    @NotBlank(message = "{validation.content.not.blank}")
    private String content;

    @ApiModelProperty(value = "扩展信息", example = "{\"giftId\": \"gift001\", \"giftCount\": 1}")
    private Map<String, Object> extra;

    @ApiModelProperty(value = "客户端类型", example = "1")
    private Integer clientType;

    @ApiModelProperty(value = "设备标识", example = "device123")
    private String imei;
}