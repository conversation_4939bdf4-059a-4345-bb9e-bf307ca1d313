package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户端信息
 * @author: <PERSON><PERSON>lee
 * @description: 客户端基本信息模型
 **/
@ApiModel(description = "客户端信息模型")
@Data
@NoArgsConstructor
public class ClientInfo {

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "客户端类型 (0:webApi 1:web 2:ios 3:android 4:windows 5:mac)", required = true, example = "1", notes = "客户端设备类型标识")
    private Integer clientType;

    @ApiModelProperty(value = "设备标识", required = true, example = "device123", notes = "设备的唯一标识符")
    private String imei;

    public ClientInfo(Integer appId, Integer clientType, String imei) {
        this.appId = appId;
        this.clientType = clientType;
        this.imei = imei;
    }
}
