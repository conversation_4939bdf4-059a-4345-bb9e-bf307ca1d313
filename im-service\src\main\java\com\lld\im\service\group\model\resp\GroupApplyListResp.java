package com.lld.im.service.group.model.resp;

import com.lld.im.service.group.model.req.GroupApplyDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 群申请列表响应
 */
@ApiModel(description = "群申请列表响应模型")
@Data
public class GroupApplyListResp {

    @ApiModelProperty(value = "申请列表", notes = "群申请记录列表")
    private List<GroupApplyDto> applyList;

    @ApiModelProperty(value = "总记录数", example = "100", notes = "符合条件的总记录数")
    private Long totalCount;

    @ApiModelProperty(value = "当前页码", example = "1", notes = "当前查询的页码")
    private Long pageNum;

    @ApiModelProperty(value = "每页大小", example = "20", notes = "每页记录数")
    private Long pageSize;

    @ApiModelProperty(value = "总页数", example = "5", notes = "总页数")
    private Long totalPages;

    @ApiModelProperty(value = "是否有下一页", example = "true", notes = "是否还有下一页数据")
    private Boolean hasNext;

    @ApiModelProperty(value = "是否有上一页", example = "false", notes = "是否还有上一页数据")
    private Boolean hasPrevious;
}
