package com.lld.im.service.group.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.enums.GroupSpeakPermissionEnum;
import com.lld.im.service.group.dao.ImGroupEntity;
import com.lld.im.service.group.model.GroupSpeakPermissionConfig;
import com.lld.im.service.group.model.resp.GetRoleInGroupResp;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 群组发言权限检查工具类
 * @author: lld
 * @description: 提供群组发言权限的检查和配置解析功能
 */
@Component
public class GroupSpeakPermissionChecker {
    
    private static final Logger logger = LoggerFactory.getLogger(GroupSpeakPermissionChecker.class);
    
    private static final String SPEAK_PERMISSION_KEY = "speakPermission";
    
    /**
     * 检查用户是否有群组发言权限
     * @param userId 用户ID
     * @param group 群组信息
     * @param memberInfo 用户在群组中的信息
     * @return 是否有发言权限
     */
    public boolean hasPermission(String userId, ImGroupEntity group, GetRoleInGroupResp memberInfo) {
        if (group == null || memberInfo == null || StringUtils.isBlank(userId)) {
            logger.warn("权限检查参数无效: userId={}, group={}, memberInfo={}", userId, group, memberInfo);
            return false;
        }
        
        try {
            // 解析群组发言权限配置
            GroupSpeakPermissionConfig config = parsePermissionConfig(group.getExtra());
            
            // 检查权限
            boolean hasPermission = config.hasPermission(userId, memberInfo.getRole());
            
            logger.debug("用户发言权限检查: userId={}, groupId={}, permissionType={}, hasPermission={}", 
                        userId, group.getGroupId(), config.getType(), hasPermission);
            
            return hasPermission;
        } catch (Exception e) {
            logger.error("检查用户发言权限时发生异常: userId={}, groupId={}", userId, group.getGroupId(), e);
            // 异常情况下默认允许发言，避免影响正常功能
            return true;
        }
    }
    
    /**
     * 从群组extra字段解析发言权限配置
     * @param extra 群组的extra字段
     * @return 发言权限配置
     */
    public GroupSpeakPermissionConfig parsePermissionConfig(String extra) {
        if (StringUtils.isBlank(extra)) {
            return GroupSpeakPermissionConfig.createDefault();
        }
        
        try {
            JSONObject extraJson = JSON.parseObject(extra);
            JSONObject speakPermissionJson = extraJson.getJSONObject(SPEAK_PERMISSION_KEY);
            
            if (speakPermissionJson == null) {
                return GroupSpeakPermissionConfig.createDefault();
            }
            
            GroupSpeakPermissionConfig config = new GroupSpeakPermissionConfig();
            
            // 解析权限类型
            Integer type = speakPermissionJson.getInteger("type");
            if (type != null && GroupSpeakPermissionEnum.isValidCode(type)) {
                config.setType(GroupSpeakPermissionEnum.getByCode(type));
            }
            
            // 解析允许发言的成员列表
            if (speakPermissionJson.containsKey("allowedMembers")) {
                config.setAllowedMembers(
                    speakPermissionJson.getJSONArray("allowedMembers").toJavaList(String.class)
                );
            }
            
            // 解析更新时间和更新者
            config.setUpdateTime(speakPermissionJson.getLong("updateTime"));
            config.setUpdatedBy(speakPermissionJson.getString("updatedBy"));
            
            // 验证配置有效性
            if (!config.isValid()) {
                logger.warn("解析到无效的发言权限配置，使用默认配置: {}", speakPermissionJson);
                return GroupSpeakPermissionConfig.createDefault();
            }
            
            return config;
        } catch (Exception e) {
            logger.error("解析群组发言权限配置失败，使用默认配置: extra={}", extra, e);
            return GroupSpeakPermissionConfig.createDefault();
        }
    }

    /**
     * 检查extra字段中是否存在发言权限配置
     * @param extra 群组的extra字段
     * @return 是否存在发言权限配置
     */
    public boolean hasPermissionConfig(String extra) {
        if (StringUtils.isBlank(extra)) {
            return false;
        }

        try {
            JSONObject extraJson = JSON.parseObject(extra);
            return extraJson.containsKey(SPEAK_PERMISSION_KEY) && extraJson.getJSONObject(SPEAK_PERMISSION_KEY) != null;
        } catch (Exception e) {
            logger.error("检查发言权限配置存在性失败: extra={}", extra, e);
            return false;
        }
    }

    /**
     * 构建包含发言权限配置的extra字段JSON字符串
     * @param originalExtra 原始的extra字段
     * @param config 发言权限配置
     * @return 更新后的extra字段JSON字符串
     */
    public String buildPermissionExtra(String originalExtra, GroupSpeakPermissionConfig config) {
        if (config == null || !config.isValid()) {
            logger.warn("发言权限配置无效，不更新extra字段: {}", config);
            return originalExtra;
        }
        
        try {
            JSONObject extraJson;
            
            if (StringUtils.isBlank(originalExtra)) {
                extraJson = new JSONObject();
            } else {
                extraJson = JSON.parseObject(originalExtra);
            }
            
            // 构建发言权限配置
            JSONObject speakPermissionJson = new JSONObject();
            speakPermissionJson.put("type", config.getType().getCode());
            
            if (config.getAllowedMembers() != null && !config.getAllowedMembers().isEmpty()) {
                speakPermissionJson.put("allowedMembers", config.getAllowedMembers());
            }
            
            speakPermissionJson.put("updateTime", 
                config.getUpdateTime() != null ? config.getUpdateTime() : System.currentTimeMillis());
            
            if (StringUtils.isNotBlank(config.getUpdatedBy())) {
                speakPermissionJson.put("updatedBy", config.getUpdatedBy());
            }
            
            extraJson.put(SPEAK_PERMISSION_KEY, speakPermissionJson);
            
            return extraJson.toJSONString();
        } catch (Exception e) {
            logger.error("构建发言权限extra字段失败: originalExtra={}, config={}", originalExtra, config, e);
            return originalExtra;
        }
    }
    
    /**
     * 从extra字段中移除发言权限配置
     * @param originalExtra 原始的extra字段
     * @return 移除权限配置后的extra字段
     */
    public String removePermissionFromExtra(String originalExtra) {
        if (StringUtils.isBlank(originalExtra)) {
            return originalExtra;
        }
        
        try {
            JSONObject extraJson = JSON.parseObject(originalExtra);
            extraJson.remove(SPEAK_PERMISSION_KEY);
            
            // 如果移除后为空对象，返回null
            if (extraJson.isEmpty()) {
                return null;
            }
            
            return extraJson.toJSONString();
        } catch (Exception e) {
            logger.error("从extra字段移除发言权限配置失败: originalExtra={}", originalExtra, e);
            return originalExtra;
        }
    }
}
