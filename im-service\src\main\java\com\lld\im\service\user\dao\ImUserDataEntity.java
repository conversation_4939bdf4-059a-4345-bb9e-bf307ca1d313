package com.lld.im.service.user.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 数据库用户数据实体类
 **/
@ApiModel(description = "用户数据实体")
@Data
@TableName("im_user_data")
public class ImUserDataEntity {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "用户的唯一标识")
    private String userId;

    @ApiModelProperty(value = "用户昵称", example = "张三", notes = "用户的显示昵称")
    private String nickName;

    @ApiModelProperty(value = "用户位置", example = "北京市朝阳区", notes = "用户所在地理位置")
    private String location;

    @ApiModelProperty(value = "生日", example = "1990-01-01", notes = "用户生日，格式：YYYY-MM-DD")
    private String birthDay;

    @ApiModelProperty(value = "密码", example = "123456", notes = "用户登录密码")
    private String password;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg", notes = "用户头像图片地址")
    private String photo;

    @ApiModelProperty(value = "性别 (0:未知 1:男性 2:女性)", example = "1", notes = "用户性别标识")
    private Integer userSex;

    @ApiModelProperty(value = "个性签名", example = "这个人很懒，什么都没留下", notes = "用户的个性签名")
    private String selfSignature;

    @ApiModelProperty(value = "加好友验证类型 (1:无需验证 2:需要验证)", example = "1", notes = "添加好友时的验证设置")
    private Integer friendAllowType;

    @ApiModelProperty(value = "禁止添加好友标识 (0:未禁用 1:已禁用)", example = "0", notes = "管理员禁止用户添加好友的设置")
    private Integer disableAddFriend;

    @ApiModelProperty(value = "禁用标识 (0:未禁用 1:已禁用)", example = "0", notes = "用户账号禁用状态")
    private Integer forbiddenFlag;

    @ApiModelProperty(value = "禁言标识 (0:未禁言 1:已禁言)", example = "0", notes = "用户禁言状态")
    private Integer silentFlag;

    @ApiModelProperty(value = "用户类型 (1:普通用户 2:客服 3:机器人)", example = "1", notes = "用户账号类型")
    private Integer userType;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "所属应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "删除标识 (0:未删除 1:已删除)", example = "0", notes = "用户数据删除状态标识")
    private Integer delFlag;

    @ApiModelProperty(value = "扩展字段", example = "{\"hobby\": \"reading\"}", notes = "用户的扩展信息")
    private String extra;


}
