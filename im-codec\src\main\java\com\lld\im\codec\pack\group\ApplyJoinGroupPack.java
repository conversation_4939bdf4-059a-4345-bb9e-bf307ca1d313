package com.lld.im.codec.pack.group;

import lombok.Data;

/**
 * @description: 申请加群通知报文
 * @author: lld
 * @version: 1.0
 */
@Data
public class ApplyJoinGroupPack {

    /**
     * 申请ID
     */
    private Long applyId;

    /**
     * 群组ID
     */
    private String groupId;

    /**
     * 申请人用户ID
     */
    private String applicantId;

    /**
     * 申请人昵称
     */
    private String applicantNickname;

    /**
     * 申请人头像
     */
    private String applicantAvatar;

    /**
     * 申请理由
     */
    private String applyReason;

    /**
     * 申请时间戳
     */
    private Long applyTime;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 序列号
     */
    private Long sequence;
}
