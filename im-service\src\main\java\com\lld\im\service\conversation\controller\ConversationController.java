package com.lld.im.service.conversation.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.conversation.model.DeleteConversationReq;
import com.lld.im.service.conversation.model.UpdateConversationReq;
import com.lld.im.service.conversation.service.ConversationService;
import com.lld.im.service.group.model.req.ImportGroupReq;
import com.lld.im.service.interceptor.UserPermissionCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 会话管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "会话管理", description = "聊天会话的创建、更新、删除和同步相关接口")
@RestController
@RequestMapping("v1/conversation")
public class ConversationController extends BaseController {

    @Autowired
    ConversationService conversationService;

    @ApiOperation(value = "删除会话", notes = "删除指定的聊天会话记录")
    @DeleteMapping("/deleteConversation")
    @UserPermissionCheck("fromId")
    public ResponseVO deleteConversation(
            @ApiParam(value = "删除会话请求参数", required = true) @RequestBody @Validated DeleteConversationReq req)  {
        fillCommonParams(req);
        return conversationService.deleteConversation(req);
    }

    @ApiOperation(value = "更新会话", notes = "更新会话状态，如置顶、免打扰等设置")
    @PutMapping("/updateConversation")
    @UserPermissionCheck("fromId")
    public ResponseVO updateConversation(
            @ApiParam(value = "更新会话请求参数", required = true) @RequestBody @Validated UpdateConversationReq req)  {
        fillCommonParams(req);
        return conversationService.updateConversation(req);
    }

    @ApiOperation(value = "同步会话列表", notes = "增量同步用户的会话列表数据")
    @PostMapping("/syncConversationList")
    public ResponseVO syncConversationList(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req)  {
        fillCommonParams(req);
        return conversationService.syncConversationSet(req);
    }

}
