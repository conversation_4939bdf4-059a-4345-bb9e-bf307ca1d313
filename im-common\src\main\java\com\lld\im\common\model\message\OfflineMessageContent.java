package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 离线消息内容模型
 * @description: 离线消息数据模型，用于存储用户离线时的消息
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "离线消息内容模型")
@Data
public class OfflineMessageContent {

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "消息体ID", required = true, example = "123456789", notes = "消息体的唯一标识")
    private Long messageKey;

    @ApiModelProperty(value = "消息内容", required = true, example = "Hello, this is an offline message", notes = "消息的具体内容")
    private String messageBody;

    @ApiModelProperty(value = "消息时间", required = true, example = "1640995200000", notes = "消息发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\"}", notes = "消息的扩展信息")
    private String extra;

    @ApiModelProperty(value = "删除标识 (0:未删除 1:已删除)", example = "0", notes = "消息删除状态标识")
    private Integer delFlag;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者用户ID", required = true, example = "user456", notes = "消息接收者的用户ID")
    private String toId;

    @ApiModelProperty(value = "消息序列号", required = true, example = "1001", notes = "消息的序列号，用于排序和同步")
    private Long messageSequence;

    @ApiModelProperty(value = "消息随机数", example = "random123", notes = "消息的随机标识，用于去重")
    private String messageRandom;

    @ApiModelProperty(value = "会话类型 (0 单聊 1群聊 2机器人 3公众号)", required = true, example = "1", notes = "离线消息所属的会话类型")
    private Integer conversationType;

    @ApiModelProperty(value = "会话ID", required = true, example = "conv_123456", notes = "会话的唯一标识")
    private String conversationId;

}
