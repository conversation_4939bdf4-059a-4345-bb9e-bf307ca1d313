package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取群组信息请求
 */
@ApiModel(description = "获取群组信息请求模型")
@Data
public class GetGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要查询的群组唯一标识")
    private String groupId;

}
