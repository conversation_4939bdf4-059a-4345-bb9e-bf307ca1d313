-- 优化通知类型查询的数据库索引
-- 用于支持按通知类型获取最新通知的功能

-- 为系统通知表添加复合索引，优化按类型和时间查询
CREATE INDEX IF NOT EXISTS idx_notification_app_type_time 
ON im_system_notification(app_id, notification_type, create_time DESC, del_flag);

-- 为用户通知关联表添加复合索引，优化用户通知查询
CREATE INDEX IF NOT EXISTS idx_user_notification_app_user_read 
ON im_user_system_notification(app_id, user_id, read_status);

-- 为用户通知关联表添加通知ID索引，优化关联查询
CREATE INDEX IF NOT EXISTS idx_user_notification_id 
ON im_user_system_notification(notification_id);

-- 为系统通知表添加通知类型索引，优化类型过滤
CREATE INDEX IF NOT EXISTS idx_notification_type 
ON im_system_notification(notification_type);
