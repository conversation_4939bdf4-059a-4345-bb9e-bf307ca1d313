package com.lld.im.mq.starter.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * IM系统通知消息模型
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImSystemNotificationMessage extends ImBaseMessage {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 通知标题
     */
    @NotBlank(message = "通知标题不能为空")
    private String title;
    
    /**
     * 通知内容
     */
    @NotBlank(message = "通知内容不能为空")
    private String content;
    
    /**
     * 通知类型
     */
    @NotNull(message = "通知类型不能为空")
    private Integer notificationType;
    
    /**
     * 接收者ID列表，为空表示广播给所有用户
     */
    private List<String> receiverIds;
    
    /**
     * 目标类型：1-所有用户，2-普通用户，3-游客用户
     * 默认为1（所有用户）
     */
    private Integer targetType = 1;

    
    /**
     * 目标类型常量
     */
    public static class TargetType {
        public static final int ALL_USERS = 1;      // 所有用户
        public static final int NORMAL_USERS = 2;   // 普通用户
        public static final int GUEST_USERS = 3;    // 游客用户
    }
}
