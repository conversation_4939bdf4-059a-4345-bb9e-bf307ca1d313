package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户ID请求
 * @author: <PERSON><PERSON><PERSON>
 * @description: 通用的用户ID请求模型
 **/
@ApiModel(description = "用户ID请求模型")
@Data
public class UserId extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "用户的唯一标识")
    private String userId;

}
