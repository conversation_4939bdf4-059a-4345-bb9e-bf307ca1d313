package com.lld.im.service.conversation.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lld.im.codec.pack.conversation.CreateConversationPack;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.command.ConversationEventCommand;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import com.lld.im.service.conversation.dao.mapper.ImConversationSetMapper;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.utils.MessageProducer;
import com.lld.im.service.utils.WriteUserSeq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 会话预创建服务
 * 在好友关系建立和群组加入时自动创建会话记录，提升消息发送响应速度
 * 
 * @author: lld
 * @description: 会话预创建服务，用于在好友关系建立和群组加入时预先创建会话记录
 */
@Service
public class ConversationPreCreateService {

    private static final Logger logger = LoggerFactory.getLogger(ConversationPreCreateService.class);

    @Autowired
    private ImConversationSetMapper imConversationSetMapper;

    @Autowired
    private RedisSeq redisSeq;

    @Autowired
    private WriteUserSeq writeUserSeq;

    @Autowired
    private MessageProducer messageProducer;

    /**
     * 好友关系建立时预创建单聊会话记录
     * 为双方用户都创建会话记录，会话ID格式：0_userId1_userId2（按用户ID升序排列）
     * 
     * @param appId 应用ID
     * @param fromId 发起方用户ID
     * @param toId 目标方用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void preCreateP2PConversation(Integer appId, String fromId, String toId) {
        logger.info("开始预创建单聊会话记录，appId: {}, fromId: {}, toId: {}", appId, fromId, toId);
        
        try {
            // 为发起方创建会话记录
            createP2PConversationIfNotExists(appId, fromId, toId);
            
            // 为目标方创建会话记录
            createP2PConversationIfNotExists(appId, toId, fromId);
            
            logger.info("单聊会话记录预创建成功，appId: {}, fromId: {}, toId: {}", appId, fromId, toId);
        } catch (Exception e) {
            logger.error("单聊会话记录预创建失败，appId: {}, fromId: {}, toId: {}, 错误: {}", 
                appId, fromId, toId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 群组加入时预创建群聊会话记录
     * 只为新加入的用户创建会话记录，会话ID格式：1_userId_groupId
     * 
     * @param appId 应用ID
     * @param userId 用户ID
     * @param groupId 群组ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void preCreateGroupConversation(Integer appId, String userId, String groupId) {
        logger.info("开始预创建群聊会话记录，appId: {}, userId: {}, groupId: {}", appId, userId, groupId);
        
        try {
            createGroupConversationIfNotExists(appId, userId, groupId);
            
            logger.info("群聊会话记录预创建成功，appId: {}, userId: {}, groupId: {}", appId, userId, groupId);
        } catch (Exception e) {
            logger.error("群聊会话记录预创建失败，appId: {}, userId: {}, groupId: {}, 错误: {}", 
                appId, userId, groupId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 批量预创建群聊会话记录
     * 用于群组成员批量加入时的会话预创建
     * 
     * @param appId 应用ID
     * @param userIds 用户ID列表
     * @param groupId 群组ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchPreCreateGroupConversation(Integer appId, List<String> userIds, String groupId) {
        logger.info("开始批量预创建群聊会话记录，appId: {}, userIds: {}, groupId: {}", appId, userIds, groupId);
        
        try {
            for (String userId : userIds) {
                createGroupConversationIfNotExists(appId, userId, groupId);
            }
            
            logger.info("批量群聊会话记录预创建成功，appId: {}, userIds: {}, groupId: {}", appId, userIds, groupId);
        } catch (Exception e) {
            logger.error("批量群聊会话记录预创建失败，appId: {}, userIds: {}, groupId: {}, 错误: {}", 
                appId, userIds, groupId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建单聊会话记录（如果不存在）
     */
    private void createP2PConversationIfNotExists(Integer appId, String fromId, String toId) {
        String conversationId = convertConversationId(ConversationTypeEnum.P2P.getCode(), fromId, toId);
        
        // 检查会话记录是否已存在
        QueryWrapper<ImConversationSetEntity> query = new QueryWrapper<>();
        query.eq("conversation_id", conversationId);
        query.eq("app_id", appId);
        
        ImConversationSetEntity existingEntity = imConversationSetMapper.selectOne(query);
        if (existingEntity != null) {
            logger.debug("单聊会话记录已存在，跳过创建，conversationId: {}", conversationId);
            return;
        }
        
        // 生成会话序列号用于增量同步
        long conversationSeq = redisSeq.doGetSeq(appId + ":" + Constants.SeqConstants.Conversation);
        
        // 创建新的会话记录
        ImConversationSetEntity newEntity = createP2PConversationEntity(
            appId, fromId, toId, ConversationTypeEnum.P2P.getCode(), conversationSeq);
        
        imConversationSetMapper.insert(newEntity);
        writeUserSeq.writeUserSeq(appId, fromId, Constants.SeqConstants.Conversation, conversationSeq);

        // 实时推送新会话创建通知
        sendConversationCreateNotification(newEntity);

        logger.debug("单聊会话记录创建成功并推送通知，conversationId: {}", conversationId);
    }

    /**
     * 创建群聊会话记录（如果不存在）
     */
    private void createGroupConversationIfNotExists(Integer appId, String userId, String groupId) {
        String conversationId = convertConversationId(ConversationTypeEnum.GROUP.getCode(), userId, groupId);
        
        // 检查会话记录是否已存在
        QueryWrapper<ImConversationSetEntity> query = new QueryWrapper<>();
        query.eq("conversation_id", conversationId);
        query.eq("app_id", appId);
        
        ImConversationSetEntity existingEntity = imConversationSetMapper.selectOne(query);
        if (existingEntity != null) {
            logger.debug("群聊会话记录已存在，跳过创建，conversationId: {}", conversationId);
            return;
        }
        
        // 生成会话序列号用于增量同步
        long conversationSeq = redisSeq.doGetSeq(appId + ":" + Constants.SeqConstants.Conversation);
        
        // 创建新的会话记录
        ImConversationSetEntity newEntity = createGroupConversationEntity(
            appId, userId, groupId, ConversationTypeEnum.GROUP.getCode(), conversationSeq);
        
        imConversationSetMapper.insert(newEntity);
        writeUserSeq.writeUserSeq(appId, userId, Constants.SeqConstants.Conversation, conversationSeq);

        // 实时推送新会话创建通知
        sendConversationCreateNotification(newEntity);

        logger.debug("群聊会话记录创建成功并推送通知，conversationId: {}", conversationId);
    }

    /**
     * 生成会话ID
     * 单聊格式：0_fromId_toId（不排序，确保每个用户有独立的会话ID）
     * 群聊格式：1_userId_groupId
     */
    private String convertConversationId(Integer type, String fromId, String toId) {
        // 统一使用直接拼接的方式，确保每个用户都有独立的会话ID
        return type + "_" + fromId + "_" + toId;
    }

    /**
     * 创建单聊会话记录实体
     */
    private ImConversationSetEntity createP2PConversationEntity(
            Integer appId, String fromId, String toId, Integer conversationType, Long conversationSequence) {
        
        ImConversationSetEntity entity = new ImConversationSetEntity();
        entity.setConversationId(convertConversationId(conversationType, fromId, toId));
        entity.setConversationType(conversationType);
        entity.setFromId(fromId);
        entity.setToId(toId);
        entity.setAppId(appId);
        entity.setIsMute(0);
        entity.setIsTop(0);
        entity.setReadedSequence(0L);
        entity.setSequence(conversationSequence);
        // 预创建时不设置消息序列号，等待首次消息时更新
        entity.setLastMessageSequence(0L);
        
        return entity;
    }

    /**
     * 创建群聊会话记录实体
     */
    private ImConversationSetEntity createGroupConversationEntity(
            Integer appId, String userId, String groupId, Integer conversationType, Long conversationSequence) {
        
        ImConversationSetEntity entity = new ImConversationSetEntity();
        entity.setConversationId(convertConversationId(conversationType, userId, groupId));
        entity.setConversationType(conversationType);
        entity.setFromId(userId);
        entity.setToId(groupId);
        entity.setAppId(appId);
        entity.setIsMute(0);
        entity.setIsTop(0);
        entity.setReadedSequence(0L);
        entity.setSequence(conversationSequence);
        // 预创建时不设置消息序列号，等待首次消息时更新
        entity.setLastMessageSequence(0L);
        
        return entity;
    }

    /**
     * 发送新会话创建通知
     */
    private void sendConversationCreateNotification(ImConversationSetEntity entity) {
        try {
            CreateConversationPack pack = new CreateConversationPack();
            pack.setConversationId(entity.getConversationId());
            pack.setConversationType(entity.getConversationType());
            pack.setFromId(entity.getFromId());
            pack.setToId(entity.getToId());
            pack.setSequence(entity.getSequence());
            pack.setLastMessageSequence(entity.getLastMessageSequence());
            pack.setIsTop(entity.getIsTop());
            pack.setIsMute(entity.getIsMute());
            pack.setReadedSequence(entity.getReadedSequence());

            // 推送给会话的拥有者（fromId）的所有在线端
            // 预创建场景下没有特定的客户端信息，推送给所有端
            messageProducer.sendToUser(entity.getFromId(),
                    ConversationEventCommand.CONVERSATION_CREATE,
                    pack, entity.getAppId());

            logger.debug("新会话创建通知发送成功: conversationId={}, fromId={}",
                entity.getConversationId(), entity.getFromId());
        } catch (Exception e) {
            logger.error("发送新会话创建通知失败: conversationId={}, error={}",
                entity.getConversationId(), e.getMessage(), e);
        }
    }
}
