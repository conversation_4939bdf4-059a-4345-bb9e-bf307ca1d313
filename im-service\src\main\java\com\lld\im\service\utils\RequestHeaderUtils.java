package com.lld.im.service.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 请求头工具类
 * 用于从HTTP请求头中获取公共参数
 * 
 * @description: 提供统一的请求头参数获取方法，支持向后兼容
 * @author: IM System
 * @version: 1.0
 */
public class RequestHeaderUtils {
    
    /**
     * 应用ID请求头名称
     */
    public static final String HEADER_APP_ID = "X-App-Id";
    
    /**
     * 用户标识请求头名称
     */
    public static final String HEADER_IDENTIFIER = "X-Identifier";
    
    /**
     * 用户签名请求头名称
     */
    public static final String HEADER_USER_SIGN = "X-User-Sign";
    
    /**
     * 客户端类型请求头名称
     */
    public static final String HEADER_CLIENT_TYPE = "X-Client-Type";
    
    /**
     * 设备标识请求头名称
     */
    public static final String HEADER_IMEI = "X-Imei";
    
    /**
     * 从当前请求中获取应用ID
     * 优先从请求头获取，如果没有则从查询参数获取
     */
    public static Integer getAppId() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        String appIdStr = getParameterFromHeaderOrQuery(request, "appId", HEADER_APP_ID);
        if (StringUtils.isNotBlank(appIdStr)) {
            try {
                return Integer.valueOf(appIdStr);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 从当前请求中获取用户标识
     */
    public static String getIdentifier() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        return getParameterFromHeaderOrQuery(request, "identifier", HEADER_IDENTIFIER);
    }
    
    /**
     * 从当前请求中获取用户签名
     */
    public static String getUserSign() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        return getParameterFromHeaderOrQuery(request, "userSign", HEADER_USER_SIGN);
    }
    
    /**
     * 从当前请求中获取客户端类型
     */
    public static Integer getClientType() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        String clientTypeStr = getParameterFromHeaderOrQuery(request, "clientType", HEADER_CLIENT_TYPE);
        if (StringUtils.isNotBlank(clientTypeStr)) {
            try {
                return Integer.valueOf(clientTypeStr);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }
    
    /**
     * 从当前请求中获取设备标识
     */
    public static String getImei() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return null;
        }
        
        return getParameterFromHeaderOrQuery(request, "imei", HEADER_IMEI);
    }
    
    /**
     * 优先从请求头获取参数，如果没有则从查询参数获取（向后兼容）
     */
    private static String getParameterFromHeaderOrQuery(HttpServletRequest request, String paramName, String headerName) {
        // 优先从请求头获取
        String value = request.getHeader(headerName);
        if (StringUtils.isNotBlank(value)) {
            return value;
        }
        
        // 如果请求头没有，则从查询参数获取（向后兼容）
        return request.getParameter(paramName);
    }
    
    /**
     * 获取当前HTTP请求对象
     */
    private static HttpServletRequest getCurrentRequest() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            return attributes.getRequest();
        }
        return null;
    }
    
    /**
     * 检查请求头是否包含所有必需的公共参数
     */
    public static boolean hasRequiredHeaders() {
        return getAppId() != null && 
               StringUtils.isNotBlank(getIdentifier()) && 
               StringUtils.isNotBlank(getUserSign());
    }
    
    /**
     * 获取请求的来源信息（用于日志记录）
     */
    public static String getRequestSource() {
        HttpServletRequest request = getCurrentRequest();
        if (request == null) {
            return "unknown";
        }
        
        StringBuilder source = new StringBuilder();
        source.append("appId=").append(getAppId())
              .append(", identifier=").append(getIdentifier())
              .append(", clientType=").append(getClientType())
              .append(", imei=").append(getImei());
        
        return source.toString();
    }
}
