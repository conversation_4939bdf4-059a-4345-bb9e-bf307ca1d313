package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户会话信息
 * @description: 用户会话状态和连接信息
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户会话信息模型")
@Data
public class UserSession {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "用户的唯一标识")
    private String userId;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "客户端类型 (0:webApi 1:web 2:ios 3:android 4:windows 5:mac)", required = true, example = "1", notes = "客户端设备类型标识")
    private Integer clientType;

    @ApiModelProperty(value = "SDK版本号", example = "1", notes = "客户端SDK的版本号")
    private Integer version;

    @ApiModelProperty(value = "连接状态 (1:在线 2:离线)", example = "1", notes = "用户当前连接状态")
    private Integer connectState;

    @ApiModelProperty(value = "代理服务器ID", example = "1", notes = "处理该用户连接的代理服务器ID")
    private Integer brokerId;

    @ApiModelProperty(value = "代理服务器地址", example = "*************", notes = "处理该用户连接的代理服务器地址")
    private String brokerHost;

    @ApiModelProperty(value = "设备标识", required = true, example = "device123", notes = "设备的唯一标识符")
    private String imei;

    @ApiModelProperty(value = "用户昵称", example = "张三", notes = "用户的显示昵称，登录时从用户服务获取并缓存")
    private String nickname;

}
