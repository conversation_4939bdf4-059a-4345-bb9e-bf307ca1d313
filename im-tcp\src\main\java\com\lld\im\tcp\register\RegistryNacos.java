package com.lld.im.tcp.register;

import com.lld.im.codec.config.BootstrapConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: Nacos服务注册器，替代RegistryZK
 * @author: lld
 * @version: 1.0
 */
public class RegistryNacos implements Runnable {

    private static Logger logger = LoggerFactory.getLogger(RegistryNacos.class);

    private NacosKit nacosKit;
    private String ip;
    private BootstrapConfig.TcpConfig tcpConfig;

    public RegistryNacos(NacosKit nacosKit, String ip, BootstrapConfig.TcpConfig tcpConfig) {
        this.nacosKit = nacosKit;
        this.ip = ip;
        this.tcpConfig = tcpConfig;
    }

    @Override
    public void run() {
        try {
            // 检查Nacos服务可用性
            if (!nacosKit.isServiceAvailable()) {
                logger.error("Nacos service is not available, registration failed");
                throw new RuntimeException("Nacos service is not available");
            }

            // 构建服务元数据
            Map<String, String> metadata = buildMetadata();

            // 注册TCP服务实例
            nacosKit.registerTcpInstance(ip, tcpConfig.getTcpPort(), metadata);
            logger.info("Register Nacos TCP service success, address=[{}:{}], brokerId=[{}]", 
                    ip, tcpConfig.getTcpPort(), tcpConfig.getBrokerId());

            // 注册WebSocket服务实例
            nacosKit.registerWebSocketInstance(ip, tcpConfig.getWebSocketPort(), metadata);
            logger.info("Register Nacos WebSocket service success, address=[{}:{}], brokerId=[{}]", 
                    ip, tcpConfig.getWebSocketPort(), tcpConfig.getBrokerId());

            // 添加JVM关闭钩子，确保优雅下线
            addShutdownHook();

        } catch (Exception e) {
            logger.error("Register Nacos service failed", e);
            throw new RuntimeException("Service registration failed", e);
        }
    }

    /**
     * 构建服务元数据
     *
     * @return 元数据Map
     */
    private Map<String, String> buildMetadata() {
        Map<String, String> metadata = new HashMap<>();
        metadata.put("brokerId", String.valueOf(tcpConfig.getBrokerId()));
        metadata.put("version", "1.0.0");
        metadata.put("region", "default");
        metadata.put("tcpPort", String.valueOf(tcpConfig.getTcpPort()));
        metadata.put("webSocketPort", String.valueOf(tcpConfig.getWebSocketPort()));
        metadata.put("heartBeatTime", String.valueOf(tcpConfig.getHeartBeatTime()));
        metadata.put("loginModel", String.valueOf(tcpConfig.getLoginModel()));
        
        if (tcpConfig.getLogicUrl() != null) {
            metadata.put("logicUrl", tcpConfig.getLogicUrl());
        }
        
        return metadata;
    }

    /**
     * 添加JVM关闭钩子，确保服务优雅下线
     */
    private void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                logger.info("Shutting down, deregistering services...");
                
                // 注销TCP服务实例
                nacosKit.deregisterTcpInstance(ip, tcpConfig.getTcpPort());
                logger.info("Deregister TCP service success");
                
                // 注销WebSocket服务实例
                nacosKit.deregisterWebSocketInstance(ip, tcpConfig.getWebSocketPort());
                logger.info("Deregister WebSocket service success");
                
            } catch (Exception e) {
                logger.error("Deregister services failed during shutdown", e);
            }
        }));
    }
}
