package com.lld.im.codec.pack.group;

import lombok.Data;

/**
 * @description: 审批申请结果通知报文
 * @author: lld
 * @version: 1.0
 */
@Data
public class ApproveGroupApplyPack {

    /**
     * 申请ID
     */
    private Long applyId;

    /**
     * 群组ID
     */
    private String groupId;

    /**
     * 群组名称
     */
    private String groupName;

    /**
     * 申请人用户ID
     */
    private String applicantId;

    /**
     * 审批人用户ID
     */
    private String approverId;

    /**
     * 审批人昵称
     */
    private String approverNickname;

    /**
     * 审批结果：1-同意 2-拒绝
     */
    private Integer approveResult;

    /**
     * 拒绝理由（审批结果为拒绝时）
     */
    private String rejectReason;

    /**
     * 审批时间戳
     */
    private Long approveTime;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 序列号
     */
    private Long sequence;
}
