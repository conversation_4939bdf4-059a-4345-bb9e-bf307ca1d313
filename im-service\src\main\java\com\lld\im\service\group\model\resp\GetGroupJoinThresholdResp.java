package com.lld.im.service.group.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 获取群组进群门槛响应
 * @author: lld
 * @description: 获取群组进群门槛的响应模型
 */
@ApiModel(description = "获取群组进群门槛响应模型")
@Data
public class GetGroupJoinThresholdResp {
    
    @ApiModelProperty(value = "群组ID", example = "group123")
    private String groupId;
    
    @ApiModelProperty(value = "门槛类型", example = "1", 
                     notes = "1-无要求 2-关注我 3-关注我超过7天 4-关注我超过30天")
    private Integer thresholdType;
    
    @ApiModelProperty(value = "门槛描述", example = "无要求")
    private String thresholdDescription;
    
    @ApiModelProperty(value = "更新时间", example = "1751601164680")
    private Long updateTime;
    
    @ApiModelProperty(value = "更新者", example = "lld")
    private String updatedBy;
}
