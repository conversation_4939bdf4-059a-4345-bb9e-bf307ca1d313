package com.lld.im.service.liveroom.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.liveroom.model.entity.LiveRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 直播间Mapper接口
 */
@Mapper
public interface LiveRoomMapper extends BaseMapper<LiveRoom> {
    /**
     * 根据直播间ID和应用ID查询直播间信息
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 直播间信息
     */
    @Select("SELECT * FROM im_live_room WHERE room_id = #{roomId} AND app_id = #{appId}")
    LiveRoom selectByRoomId(@Param("roomId") String roomId, @Param("appId") Integer appId);
} 