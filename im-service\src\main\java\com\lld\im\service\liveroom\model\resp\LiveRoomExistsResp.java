package com.lld.im.service.liveroom.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 直播间存在状态响应模型
 * 
 * @description: 单个直播间的存在状态信息
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "直播间存在状态响应模型")
@Data
public class LiveRoomExistsResp {

    @ApiModelProperty(value = "直播间ID", example = "room123", notes = "直播间的唯一标识")
    private String roomId;

    @ApiModelProperty(value = "是否存在", example = "true", notes = "直播间是否存在于系统中")
    private Boolean exists;

    @ApiModelProperty(value = "直播间状态", example = "1", notes = "直播间状态：0-未开播，1-直播中，2-已结束（仅当exists为true时有值）")
    private Integer status;

    @ApiModelProperty(value = "直播间名称", example = "精彩直播间", notes = "直播间名称（仅当exists为true时有值）")
    private String roomName;

    public LiveRoomExistsResp() {}

    public LiveRoomExistsResp(String roomId, Boolean exists) {
        this.roomId = roomId;
        this.exists = exists;
    }

    public LiveRoomExistsResp(String roomId, Boolean exists, Integer status, String roomName) {
        this.roomId = roomId;
        this.exists = exists;
        this.status = status;
        this.roomName = roomName;
    }
}
