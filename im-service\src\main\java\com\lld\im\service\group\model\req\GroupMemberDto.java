package com.lld.im.service.group.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 群成员信息DTO
 * @description: 群成员数据传输对象
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "群成员信息数据传输对象")
@Data
public class GroupMemberDto {

    @ApiModelProperty(value = "成员用户ID", required = true, example = "user123", notes = "群成员的用户唯一标识")
    private String memberId;

    @ApiModelProperty(value = "群内昵称", example = "小明", notes = "成员在群内的显示昵称")
    private String alias;

    @ApiModelProperty(value = "群内角色 (0:普通成员 1:管理员 2:群主 3:已移除)", example = "0", notes = "成员在群组中的角色权限")
    private Integer role;

    @ApiModelProperty(value = "禁言截止时间", example = "1640995200000", notes = "成员禁言截止时间戳，单位毫秒，0表示未禁言")
    private Long speakDate;

    @ApiModelProperty(value = "加入方式 (invite:邀请 apply:申请 qr:扫码)", example = "invite", notes = "成员加入群组的方式")
    private String joinType;

    @ApiModelProperty(value = "加入时间", example = "1640995200000", notes = "加入群组的时间戳，单位毫秒")
    private Long joinTime;

}
