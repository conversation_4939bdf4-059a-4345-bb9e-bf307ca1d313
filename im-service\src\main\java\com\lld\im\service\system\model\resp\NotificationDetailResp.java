package com.lld.im.service.system.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 通知详情响应模型
 * @description: 系统通知的详细信息响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "通知详情响应模型")
@Data
public class NotificationDetailResp {

    @ApiModelProperty(value = "通知ID", example = "123456789", notes = "通知的唯一标识")
    private Long notificationId;

    @ApiModelProperty(value = "应用ID", example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "通知类型", example = "1", notes = "通知类型(1:系统消息 2:竞猜通知 3:我的关注 4:点赞评论 5:直播提醒 6:活动消息)")
    private Integer notificationType;

    @ApiModelProperty(value = "通知标题", example = "系统维护通知", notes = "通知的标题")
    private String title;

    @ApiModelProperty(value = "通知内容", example = "系统将于今晚22:00-24:00进行维护", notes = "通知的详细内容")
    private String content;

    @ApiModelProperty(value = "扩展字段", example = "{\"url\": \"https://example.com\"}", notes = "额外的扩展信息，JSON格式")
    private Map<String, Object> extra;

    @ApiModelProperty(value = "创建时间", example = "1640995200000", notes = "通知创建时间戳，单位毫秒")
    private Long createTime;

    @ApiModelProperty(value = "已读状态", example = "0", notes = "读取状态：0-未读，1-已读")
    private Integer readStatus;

    @ApiModelProperty(value = "已读时间", example = "1640995200000", notes = "读取时间戳，单位毫秒，未读时为null")
    private Long readTime;

    @ApiModelProperty(value = "序列号", example = "1000", notes = "通知序列号，用于排序和同步")
    private Long sequence;
}
