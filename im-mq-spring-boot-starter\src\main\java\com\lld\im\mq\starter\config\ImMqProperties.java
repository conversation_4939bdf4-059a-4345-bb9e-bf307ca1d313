package com.lld.im.mq.starter.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * IM MQ配置属性类
 * 
 * <AUTHOR>
 */
@Data
@Validated
@ConfigurationProperties(prefix = "im.mq")
public class ImMqProperties {
    
    /**
     * 是否启用IM MQ功能
     */
    private boolean enabled = true;
    
    /**
     * 系统通知配置
     */
    @Valid
    private NotificationConfig notification = new NotificationConfig();
    
    /**
     * 直播间消息配置
     */
    @Valid
    private LiveroomConfig liveroom = new LiveroomConfig();
    
    /**
     * 系统通知配置
     */
    @Data
    public static class NotificationConfig {
        
        /**
         * 系统通知交换机名称
         */
        @NotBlank(message = "系统通知交换机名称不能为空")
        private String exchange = "im.notification.exchange";
        
        /**
         * 系统通知路由键
         */
        @NotBlank(message = "系统通知路由键不能为空")
        private String routingKey = "im.notification";
        
        /**
         * 交换机类型
         */
        private String exchangeType = "topic";
        
        /**
         * 是否持久化
         */
        private boolean durable = true;
    }
    
    /**
     * 直播间消息配置
     */
    @Data
    public static class LiveroomConfig {
        
        /**
         * 直播间消息交换机名称
         */
        @NotBlank(message = "直播间消息交换机名称不能为空")
        private String exchange = "im.liveroom.exchange";
        
        /**
         * 直播间消息路由键
         */
        @NotBlank(message = "直播间消息路由键不能为空")
        private String routingKey = "im.liveroom";
        
        /**
         * 交换机类型
         */
        private String exchangeType = "topic";
        
        /**
         * 是否持久化
         */
        private boolean durable = true;
    }
}
