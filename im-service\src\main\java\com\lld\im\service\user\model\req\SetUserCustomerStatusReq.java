package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 设置用户自定义状态请求
 * @description: 设置用户的自定义在线状态和状态文本
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "设置用户自定义状态请求模型")
@Data
public class SetUserCustomerStatusReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要设置状态的用户ID")
    private String userId;

    @ApiModelProperty(value = "自定义状态文本", example = "忙碌中", notes = "用户自定义的状态描述文本")
    private String customText;

    @ApiModelProperty(value = "自定义状态码 (0:在线 1:忙碌 2:离开 3:隐身)", example = "1", notes = "用户自定义在线状态")
    private Integer customStatus;

}
