package com.lld.im.tcp.utils;

import com.lld.im.codec.config.BootstrapConfig;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.Address;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * @description: RabbitMQ连接工厂 - 支持单机和集群模式
 * @author: lld
 * @version: 1.0
 */
public class MqFactory {

    private static final Logger logger = LoggerFactory.getLogger(MqFactory.class);

    private static ConnectionFactory factory = null;
    private static BootstrapConfig.Rabbitmq rabbitmqConfig = null;
    private static ConcurrentHashMap<String,Channel> channelMap = new ConcurrentHashMap<>();

    /**
     * 获取连接 - 支持集群模式
     */
    private static Connection getConnection() throws IOException, TimeoutException {
        if (rabbitmqConfig.getAddresses() != null && !rabbitmqConfig.getAddresses().isEmpty()) {
            // 集群模式
            logger.info("使用RabbitMQ集群模式连接，节点数量: {}", rabbitmqConfig.getAddresses().size());
            List<Address> addresses = rabbitmqConfig.getAddresses().stream()
                    .map(addr -> new Address(addr.getHost(), addr.getPort()))
                    .collect(Collectors.toList());
            return factory.newConnection(addresses);
        } else {
            // 单机模式（向后兼容）
            logger.info("使用RabbitMQ单机模式连接: {}:{}", rabbitmqConfig.getHost(), rabbitmqConfig.getPort());
            return factory.newConnection();
        }
    }

    /**
     * 获取Channel，支持连接重用和故障恢复
     */
    public static Channel getChannel(String channelName) throws IOException, TimeoutException {
        Channel channel = channelMap.get(channelName);
        if(channel == null || !channel.isOpen()){
            logger.info("创建新的RabbitMQ Channel: {}", channelName);
            channel = getConnection().createChannel();
            channelMap.put(channelName, channel);
        }
        return channel;
    }

    /**
     * 初始化RabbitMQ连接工厂 - 支持集群配置
     */
    public static void init(BootstrapConfig.Rabbitmq rabbitmq){
        if(factory == null){
            logger.info("初始化RabbitMQ连接工厂...");
            rabbitmqConfig = rabbitmq;
            factory = new ConnectionFactory();

            // 设置基本连接参数
            factory.setUsername(rabbitmq.getUserName());
            factory.setPassword(rabbitmq.getPassword());
            factory.setVirtualHost(rabbitmq.getVirtualHost());

            // 设置连接超时和心跳
            if (rabbitmq.getConnectionTimeout() != null) {
                factory.setConnectionTimeout(rabbitmq.getConnectionTimeout());
            }
            if (rabbitmq.getRequestedHeartbeat() != null) {
                factory.setRequestedHeartbeat(rabbitmq.getRequestedHeartbeat());
            }
            if (rabbitmq.getNetworkRecoveryInterval() != null) {
                factory.setNetworkRecoveryInterval(rabbitmq.getNetworkRecoveryInterval());
            }
            if (rabbitmq.getAutomaticRecoveryEnabled() != null) {
                factory.setAutomaticRecoveryEnabled(rabbitmq.getAutomaticRecoveryEnabled());
            }

            // 单机模式需要设置host和port
            if (rabbitmq.getAddresses() == null || rabbitmq.getAddresses().isEmpty()) {
                factory.setHost(rabbitmq.getHost());
                factory.setPort(rabbitmq.getPort());
            }

            logger.info("RabbitMQ连接工厂初始化完成");
        }
    }

}
