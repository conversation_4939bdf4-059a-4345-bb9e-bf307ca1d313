package com.lld.im.service.interceptor;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.BaseErrorCode;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.enums.GateWayErrorCode;
import com.lld.im.common.exception.ApplicationExceptionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Component
public class GateWayInterceptor implements HandlerInterceptor {

    @Autowired
    IdentityCheck identityCheck;

    // ThreadLocal保存当前用户identifier
    private static final ThreadLocal<String> currentUserThreadLocal = new ThreadLocal<>();

    // ThreadLocal保存当前应用ID
    private static final ThreadLocal<Integer> currentAppIdThreadLocal = new ThreadLocal<>();

    // ThreadLocal保存当前用户签名
    private static final ThreadLocal<String> currentUserSignThreadLocal = new ThreadLocal<>();

    //appService -》im接口 -》 userSign
    //appService（gen userSig）

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        // 优先从请求头获取公共参数，兼容查询参数方式
        String appIdStr = getParameterFromHeaderOrQuery(request, "appId", "X-App-Id");
        if(StringUtils.isBlank(appIdStr)){
            resp(ResponseVO.errorResponse(GateWayErrorCode
            .APPID_NOT_EXIST),response);
            return false;
        }

        String identifier = getParameterFromHeaderOrQuery(request, "identifier", "X-Identifier");
        if(StringUtils.isBlank(identifier)){
            resp(ResponseVO.errorResponse(GateWayErrorCode
                    .OPERATER_NOT_EXIST),response);
            return false;
        }

        String userSign = getParameterFromHeaderOrQuery(request, "userSign", "X-User-Sign");
        if(StringUtils.isBlank(userSign)){
            resp(ResponseVO.errorResponse(GateWayErrorCode
                    .USERSIGN_NOT_EXIST),response);
            return false;
        }

        //签名和操作人和appid是否匹配
        ApplicationExceptionEnum applicationExceptionEnum = identityCheck.checkUserSig(identifier, appIdStr, userSign);
        if(applicationExceptionEnum != BaseErrorCode.SUCCESS){
            resp(ResponseVO.errorResponse(applicationExceptionEnum),response);
            return false;
        }

        // 校验通过后，将公共参数存入ThreadLocal供后续使用
        currentUserThreadLocal.set(identifier);
        setAppIdToThreadLocal(Integer.valueOf(appIdStr));
        setUserSignToThreadLocal(userSign);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 请求完成后清理ThreadLocal，防止内存泄漏
        currentUserThreadLocal.remove();
        currentAppIdThreadLocal.remove();
        currentUserSignThreadLocal.remove();
    }

    private void resp(ResponseVO respVo ,HttpServletResponse response){

        PrintWriter writer = null;
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html; charset=utf-8");
        try {
            String resp = JSONObject.toJSONString(respVo);

            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-type", "application/json;charset=UTF-8");
            response.setHeader("Access-Control-Allow-Origin","*");
            response.setHeader("Access-Control-Allow-Credentials","true");
            response.setHeader("Access-Control-Allow-Methods","*");
            response.setHeader("Access-Control-Allow-Headers","*");
            response.setHeader("Access-Control-Max-Age","3600");

            writer = response.getWriter();
            writer.write(resp);
        } catch (Exception e){
            e.printStackTrace();
        } finally {
            if(writer != null){
                writer.checkError();
            }
        }

    }

    /**
     * 优先从请求头获取参数，如果没有则从查询参数获取（向后兼容）
     */
    private String getParameterFromHeaderOrQuery(HttpServletRequest request, String paramName, String headerName) {
        // 优先从请求头获取
        String value = request.getHeader(headerName);
        if (StringUtils.isNotBlank(value)) {
            return value;
        }

        // 如果请求头没有，则从查询参数获取（向后兼容）
        return request.getParameter(paramName);
    }

    /**
     * 设置应用ID到ThreadLocal
     */
    private void setAppIdToThreadLocal(Integer appId) {
        currentAppIdThreadLocal.set(appId);
    }

    /**
     * 设置用户签名到ThreadLocal
     */
    private void setUserSignToThreadLocal(String userSign) {
        currentUserSignThreadLocal.set(userSign);
    }

    /**
     * 获取当前登录用户identifier
     */
    public static String getCurrentUser() {
        return currentUserThreadLocal.get();
    }

    /**
     * 获取当前应用ID
     */
    public static Integer getCurrentAppId() {
        return currentAppIdThreadLocal.get();
    }

    /**
     * 获取当前用户签名
     */
    public static String getCurrentUserSign() {
        return currentUserSignThreadLocal.get();
    }

    /**
     * 全局越权校验：判断参数userId/identifier是否等于当前登录用户
     * 不一致则抛出异常
     */
    public static void checkUserPermission(String paramUserId) {
        String currentUser = getCurrentUser();
        if (currentUser == null || !currentUser.equals(paramUserId)) {
            throw new RuntimeException("越权访问：无权操作他人数据");
        }
    }
}
