package com.lld.im.tcp.redis;

import com.lld.im.codec.config.BootstrapConfig;
import org.redisson.api.RedissonClient;

/**
 * Redis客户端策略接口
 * 支持单机、集群、哨兵等不同模式的Redis连接
 * 
 * @author: lld
 * @version: 1.0
 */
public interface RedisClientStrategy {
    
    /**
     * 根据配置创建RedissonClient
     * 
     * @param redisConfig Redis配置
     * @return RedissonClient实例
     */
    RedissonClient getRedissonClient(BootstrapConfig.RedisConfig redisConfig);
    
    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();
}
