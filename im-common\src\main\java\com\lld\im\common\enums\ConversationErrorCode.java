package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 **/
public enum ConversationErrorCode implements ApplicationExceptionEnum {

    CONVERSATION_UPDATE_PARAM_ERROR(50000,"conversation.update.param.error"),


    ;

    private int code;
    private String messageKey;

    ConversationErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case CONVERSATION_UPDATE_PARAM_ERROR:
                return "會話修改參數錯誤";
            default:
                return this.messageKey;
        }
    }

}
