package com.lld.im.service.group.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.enums.GroupJoinThresholdEnum;
import com.lld.im.service.group.model.GroupJoinThresholdConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 群组进群门槛检查器
 * @author: lld
 * @description: 处理群组进群门槛相关的配置和检查逻辑
 */
@Component
public class GroupJoinThresholdChecker {
    
    private static final Logger logger = LoggerFactory.getLogger(GroupJoinThresholdChecker.class);
    
    /**
     * extra字段中进群门槛配置的key
     */
    private static final String JOIN_THRESHOLD_KEY = "joinThreshold";
    
    /**
     * 从群组extra字段解析进群门槛配置
     * @param extra 群组的extra字段
     * @return 进群门槛配置，如果不存在则返回默认配置
     */
    public GroupJoinThresholdConfig parseThresholdConfig(String extra) {
        if (StringUtils.isBlank(extra)) {
            return GroupJoinThresholdConfig.createDefault();
        }

        try {
            JSONObject extraJson = JSON.parseObject(extra);
            JSONObject joinThresholdJson = extraJson.getJSONObject(JOIN_THRESHOLD_KEY);

            if (joinThresholdJson == null) {
                return GroupJoinThresholdConfig.createDefault();
            }

            GroupJoinThresholdConfig config = new GroupJoinThresholdConfig();

            // 解析门槛类型
            Integer type = joinThresholdJson.getInteger("type");
            if (type != null && GroupJoinThresholdEnum.isValidCode(type)) {
                config.setType(GroupJoinThresholdEnum.getByCode(type));
            }

            // 解析更新时间和更新者
            config.setUpdateTime(joinThresholdJson.getLong("updateTime"));
            config.setUpdatedBy(joinThresholdJson.getString("updatedBy"));

            // 验证配置有效性
            if (!config.isValid()) {
                logger.warn("解析到无效的进群门槛配置，使用默认配置: {}", joinThresholdJson);
                return GroupJoinThresholdConfig.createDefault();
            }

            return config;
        } catch (Exception e) {
            logger.error("解析群组进群门槛配置失败，使用默认配置: extra={}", extra, e);
            return GroupJoinThresholdConfig.createDefault();
        }
    }

    /**
     * 检查extra字段中是否存在进群门槛配置
     * @param extra 群组的extra字段
     * @return 是否存在进群门槛配置
     */
    public boolean hasThresholdConfig(String extra) {
        if (StringUtils.isBlank(extra)) {
            return false;
        }

        try {
            JSONObject extraJson = JSON.parseObject(extra);
            return extraJson.containsKey(JOIN_THRESHOLD_KEY) && extraJson.getJSONObject(JOIN_THRESHOLD_KEY) != null;
        } catch (Exception e) {
            logger.error("检查进群门槛配置存在性失败: extra={}", extra, e);
            return false;
        }
    }
    
    /**
     * 构建包含进群门槛配置的extra字段JSON字符串
     * @param originalExtra 原始的extra字段
     * @param config 进群门槛配置
     * @return 更新后的extra字段JSON字符串
     */
    public String buildThresholdExtra(String originalExtra, GroupJoinThresholdConfig config) {
        if (config == null || !config.isValid()) {
            logger.warn("进群门槛配置无效，不更新extra字段: {}", config);
            return originalExtra;
        }
        
        try {
            JSONObject extraJson;
            
            if (StringUtils.isBlank(originalExtra)) {
                extraJson = new JSONObject();
            } else {
                extraJson = JSON.parseObject(originalExtra);
            }
            
            // 构建进群门槛配置
            JSONObject joinThresholdJson = new JSONObject();
            joinThresholdJson.put("type", config.getType().getCode());
            
            joinThresholdJson.put("updateTime", 
                config.getUpdateTime() != null ? config.getUpdateTime() : System.currentTimeMillis());
            
            if (StringUtils.isNotBlank(config.getUpdatedBy())) {
                joinThresholdJson.put("updatedBy", config.getUpdatedBy());
            }
            
            // 将进群门槛配置添加到extra中
            extraJson.put(JOIN_THRESHOLD_KEY, joinThresholdJson);
            
            return extraJson.toJSONString();
        } catch (Exception e) {
            logger.error("构建进群门槛extra字段失败: originalExtra={}, config={}", originalExtra, config, e);
            return originalExtra;
        }
    }
    
    /**
     * 从extra字段中移除进群门槛配置
     * @param originalExtra 原始的extra字段
     * @return 移除门槛配置后的extra字段
     */
    public String removeThresholdFromExtra(String originalExtra) {
        if (StringUtils.isBlank(originalExtra)) {
            return originalExtra;
        }
        
        try {
            JSONObject extraJson = JSON.parseObject(originalExtra);
            extraJson.remove(JOIN_THRESHOLD_KEY);
            
            // 如果移除后为空对象，返回null
            if (extraJson.isEmpty()) {
                return null;
            }
            
            return extraJson.toJSONString();
        } catch (Exception e) {
            logger.error("从extra字段移除进群门槛配置失败: originalExtra={}", originalExtra, e);
            return originalExtra;
        }
    }
}
