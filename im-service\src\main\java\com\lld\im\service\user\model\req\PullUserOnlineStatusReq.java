package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 拉取用户在线状态请求
 * @description: 批量获取指定用户的在线状态信息
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "拉取用户在线状态请求模型")
@Data
public class PullUserOnlineStatusReq extends RequestBase {

    @ApiModelProperty(value = "用户ID列表", example = "[\"user123\", \"user456\"]", notes = "要查询在线状态的用户ID列表")
    private List<String> userList;

}
