package com.lld.im.service.user.service;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.user.dao.ImUserDataEntity;
import com.lld.im.service.user.model.req.*;
import com.lld.im.service.user.model.resp.GetUserInfoResp;

import java.util.Map;
import java.util.Set;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public interface ImUserService {

    public ResponseVO importUser(ImportUserReq req);

    public ResponseVO<GetUserInfoResp> getUserInfo(GetUserInfoReq req);

    public ResponseVO<ImUserDataEntity> getSingleUserInfo(String userId , Integer appId);

    public ResponseVO deleteUser(DeleteUserReq req);

    public ResponseVO modifyUserInfo(ModifyUserInfoReq req);

    public ResponseVO login(LoginReq req);

    ResponseVO getUserSequence(GetUserSequenceReq req);
    
    /**
     * 获取用户信息，直接通过userId和appId
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户信息
     */
    ResponseVO<Map<String, Object>> getUserInfo(String userId, Integer appId);
    /**
     * 批量获取用户信息
     * @param userIds 用户ID集合
     * @param appId 应用ID
     * @return 用户ID到用户信息的映射
     */
    Map<String, ImUserDataEntity> batchGetUserInfo(Set<String> userIds, Integer appId);


}
