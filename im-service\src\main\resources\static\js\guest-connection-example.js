/**
 * 游客连接WebSocket示例代码
 */

// WebSocket客户端封装
class ImWebSocketClient {
  constructor(options = {}) {
    this.options = {
      reconnectInterval: 3000,    // 重连间隔时间
      heartbeatInterval: 30000,   // 心跳间隔时间
      ...options
    };
    
    this.socket = null;
    this.reconnectAttempts = 0;
    this.reconnecting = false;
    this.isGuest = options.isGuest || false;  // 是否为游客模式
    this.guestId = options.guestId || this.generateGuestId();
    this.userId = options.userId;
    this.appId = options.appId || 10000;
    this.clientType = options.clientType || 3;  // 默认为Web客户端
    this.imei = options.imei || 'web';
    this.token = options.token || '';
    this.serverUrl = '';
    this.handlers = {};
    this.heartbeatTimer = null;
    
    // 绑定方法
    this.onOpen = this.onOpen.bind(this);
    this.onMessage = this.onMessage.bind(this);
    this.onClose = this.onClose.bind(this);
    this.onError = this.onError.bind(this);
  }
  
  /**
   * 连接到WebSocket服务器
   * @param {string} url 服务器地址
   */
  connect(url) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      console.log('WebSocket已经连接，无需重复连接');
      return;
    }
    
    try {
      this.serverUrl = url;
      this.socket = new WebSocket(url);
      this.socket.onopen = this.onOpen;
      this.socket.onmessage = this.onMessage;
      this.socket.onclose = this.onClose;
      this.socket.onerror = this.onError;
      
      console.log(`${this.isGuest ? '游客' : '用户'} ${this.isGuest ? this.guestId : this.userId} 正在连接WebSocket...`);
    } catch (error) {
      console.error('WebSocket连接错误:', error);
      this.reconnect();
    }
  }
  
  /**
   * 连接打开时的处理
   */
  onOpen() {
    console.log(`WebSocket连接已建立，${this.isGuest ? '游客' : '用户'} ${this.isGuest ? this.guestId : this.userId}`);
    this.reconnectAttempts = 0;
    this.reconnecting = false;
    
    // 发送认证消息
    this.sendAuthMessage();
    
    // 开始心跳
    this.startHeartbeat();
    
    // 触发自定义成功回调
    if (this.options.onConnectSuccess) {
      this.options.onConnectSuccess();
    }
  }
  
  /**
   * 收到消息时的处理
   * @param {MessageEvent} event 
   */
  onMessage(event) {
    try {
      // 检查消息类型
      if (event.data instanceof Blob) {
        // 处理Blob消息 - 先将Blob转为ArrayBuffer
        console.log('📦 收到Blob消息，大小:', event.data.size, '字节，正在转换为二进制...');
        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            const arrayBuffer = e.target.result;
            this.handleBinaryMessage(arrayBuffer);
          } catch (error) {
            console.error('处理Blob消息失败:', error);
            
            // 如果无法解析为二进制消息，直接将Blob转为文本尝试解析
            const textReader = new FileReader();
            textReader.onload = (textEvent) => {
              try {
                const text = textEvent.target.result;
                this.handleTextMessage(text);
              } catch (textError) {
                console.error('将Blob转为文本处理失败:', textError);
                // 触发自定义消息回调，传递原始Blob
                if (this.options.onMessage) {
                  this.options.onMessage(event.data);
                }
              }
            };
            textReader.readAsText(event.data);
          }
        };
        reader.readAsArrayBuffer(event.data);
        return; // 异步处理，直接返回
      } else if (event.data instanceof ArrayBuffer) {
        // 处理二进制消息
        this.handleBinaryMessage(event.data);
      } else {
        // 尝试处理文本消息
        this.handleTextMessage(event.data);
      }
    } catch (error) {
      console.error('解析消息失败:', error, event.data);
      // 触发自定义消息回调，传递原始数据
      if (this.options.onMessage) {
        this.options.onMessage(event.data);
      }
    }
  }
  
  /**
   * 处理二进制消息
   * @param {ArrayBuffer} arrayBuffer 
   */
  handleBinaryMessage(arrayBuffer) {
    console.log('📦 处理二进制消息，长度:', arrayBuffer.byteLength);
    const dataView = new DataView(arrayBuffer);

    // 解析头部
    const command = dataView.getInt32(0); // 前4字节是命令
    const bodyLength = dataView.getInt32(4); // 接下来4字节是长度

    console.log('📋 二进制帧头部:', { command, bodyLength });

    // 解析消息体
    const bodyStart = 8;
    const bodyEnd = bodyStart + bodyLength;
    const jsonBytes = new Uint8Array(arrayBuffer.slice(bodyStart, bodyEnd));
    const jsonString = new TextDecoder('utf-8').decode(jsonBytes);
    let data;

    try {
      data = JSON.parse(jsonString);
      // 添加命令
      data.command = command;

      // 特殊标记系统通知
      if (command === 9004) {
        console.log('🔔 收到系统通知 (二进制帧):', data);
      } else {
        console.log('✅ 解析二进制消息成功:', data);
      }
      
      // 调用对应的消息处理器
      if (this.handlers[command]) {
        this.handlers[command].forEach(handler => {
          try {
            handler(data);
          } catch (e) {
            console.error(`处理消息出错，command=${command}:`, e);
          }
        });
      }
      
      // 触发自定义消息回调
      if (this.options.onMessage) {
        this.options.onMessage(data);
      }
    } catch (e) {
      console.error('❌ 解析二进制消息JSON失败:', e);
      console.error('📄 原始JSON字符串:', jsonString);
      console.error('🔍 二进制帧信息:', { command, bodyLength, totalLength: arrayBuffer.byteLength });

      // 触发自定义消息回调，传递原始二进制数据
      if (this.options.onMessage) {
        this.options.onMessage(arrayBuffer);
      }
    }
  }
  
  /**
   * 处理文本消息
   * @param {string} textData 
   */
  handleTextMessage(textData) {
    console.log('📝 处理文本消息');
    const data = JSON.parse(textData);

    const command = data.command;
    if (command === 9004) {
      console.log('🔔 收到系统通知 (文本格式):', data);
      console.warn('⚠️ 注意：系统通知应该使用二进制帧格式，当前收到的是文本格式');
    } else {
      console.log('📨 收到文本消息:', data);
    }
    // 调用对应的消息处理器
    if (this.handlers[command]) {
      this.handlers[command].forEach(handler => {
        try {
          handler(data);
        } catch (e) {
          console.error(`处理消息出错，command=${command}:`, e);
        }
      });
    }
    
    // 触发自定义消息回调
    if (this.options.onMessage) {
      this.options.onMessage(data);
    }
  }
  
  /**
   * 连接关闭时的处理
   */
  onClose() {
    console.log('WebSocket连接已关闭');
    this.stopHeartbeat();
    
    if (!this.reconnecting) {
      this.reconnect();
    }
    
    // 触发自定义关闭回调
    if (this.options.onClose) {
      this.options.onClose();
    }
  }
  
  /**
   * 连接错误时的处理
   * @param {Event} error 
   */
  onError(error) {
    console.error('WebSocket连接错误:', error);
    
    // 触发自定义错误回调
    if (this.options.onError) {
      this.options.onError(error);
    }
  }
  
  /**
   * 重新连接
   */
  reconnect() {
    if (this.reconnecting) return;
    
    this.reconnecting = true;
    const maxReconnectAttempts = this.options.maxReconnectAttempts || 10;
    
    if (this.reconnectAttempts < maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重新连接 ${this.reconnectAttempts}/${maxReconnectAttempts}...`);
      
      setTimeout(() => {
        this.connect(this.serverUrl);
      }, this.options.reconnectInterval);
    } else {
      console.error('达到最大重连次数，停止重连');
      this.reconnecting = false;
      
      // 触发自定义重连失败回调
      if (this.options.onReconnectFailed) {
        this.options.onReconnectFailed();
      }
    }
  }
  
  /**
   * 发送鉴权消息
   */
  sendAuthMessage() {
    // 发送符合后端期望格式的认证消息
    const authData = {
      command: 9000,  // LOGIN
      version: 1,     // 版本号
      clientType: this.clientType,
      messageType: 0x0,  // JSON格式
      appId: this.appId,
      imei: this.imei,
      data: {
        userId: this.isGuest ? this.guestId : this.userId,
        appId: this.appId,
        clientType: this.clientType,
        imei: this.imei,
        isGuest: this.isGuest  // 添加游客标识
      }
    };
    
    // 使用JSON字符串格式发送
    this.send(JSON.stringify(authData));
    console.log('已发送认证消息:', authData);
  }
  
  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        // 发送符合后端期望格式的心跳消息
        const heartbeatData = {
          command: 9999,  // PING
          version: 1,     // 版本号
          clientType: this.clientType,
          messageType: 0x0,  // JSON格式
          appId: this.appId,
          imei: this.imei,
          data: {
            timestamp: Date.now()
          }
        };
        
        this.send(JSON.stringify(heartbeatData));
        console.log('已发送心跳:', heartbeatData);
      }
    }, this.options.heartbeatInterval);
  }
  
  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
  
  /**
   * 发送消息
   * @param {object|string} data 消息对象或字符串
   */
  send(data) {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error('WebSocket未连接，无法发送消息');
      return false;
    }
    
    try {
      // 使用文本格式发送JSON数据
      const message = typeof data === 'string' ? data : JSON.stringify(data);
      this.socket.send(message);
      console.log('已发送文本消息:', JSON.parse(message));
      return true;
    } catch (error) {
      console.error('发送消息失败:', error);
      return false;
    }
  }
  
  /**
   * 关闭连接
   */
  close() {
    this.stopHeartbeat();
    
    if (this.socket) {
      // 发送登出命令
      const logoutData = {
        command: 9002,  // LOGOUT
        version: 1,     // 版本号
        clientType: this.clientType,
        messageType: 0x0,  // JSON格式
        appId: this.appId,
        imei: this.imei,
        data: {}
      };
      
      this.send(JSON.stringify(logoutData));
      console.log('已发送登出消息');
      
      // 关闭连接
      this.socket.close();
      this.socket = null;
    }
  }
  
  /**
   * 注册消息处理器
   * @param {number} command 命令类型
   * @param {function} handler 处理函数
   */
  registerMessageHandler(command, handler) {
    if (!this.handlers[command]) {
      this.handlers[command] = [];
    }
    
    this.handlers[command].push(handler);
  }
  
  /**
   * 生成游客ID
   */
  generateGuestId() {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `guest_${timestamp}${random}`;
  }
}

// 使用示例
function connectAsGuest() {
  // 1. 创建游客WebSocket客户端
  const client = new ImWebSocketClient({
    isGuest: true,                     // 设置为游客模式
    appId: 10000,                      // 应用ID
    clientType: 3,                     // 客户端类型（Web）
    reconnectInterval: 3000,           // 重连间隔
    maxReconnectAttempts: 5,           // 最大重连次数
    heartbeatInterval: 30000,          // 心跳间隔
    onConnectSuccess: () => {
      console.log('游客连接成功');
    },
    onMessage: (data) => {
      console.log('收到消息:', data);
    },
    onClose: () => {
      console.log('连接已关闭');
    },
    onError: (error) => {
      console.error('连接错误:', error);
    }
  });
  
  // 2. 注册系统消息处理器
  client.registerMessageHandler(9004, (data) => {
    console.log('🔔 系统通知处理器被调用:', data);
    console.log('📋 完整消息结构:', {
      command: data.command,
      appId: data.appId,
      clientType: data.clientType,
      imei: data.imei,
      timestamp: data.timestamp,
      hasData: !!data.data
    });

    // 处理系统通知消息
    const notification = data.data;
    if (notification) {
      showSystemNotification(notification);
    } else {
      console.error('❌ 系统通知数据缺失:', data);
    }
  });
  
  // 3. 连接服务器
  client.connect('ws://localhost:9000/ws');
  
  return client;
}

// 显示系统通知
function showSystemNotification(notification) {
  console.log('🔔 显示系统通知:', notification);

  // 验证通知数据
  if (!notification) {
    console.error('❌ 系统通知数据为空');
    return;
  }

  // 根据通知类型处理
  const type = notification.notificationType || 1;
  const title = notification.title || '系统通知';
  const content = notification.content || '';
  const timestamp = notification.timestamp || Date.now();

  console.log('📋 通知详情:', { type, title, content, timestamp });

  // 在页面上显示通知
  const notificationDiv = document.createElement('div');
  notificationDiv.className = `system-notification type-${type}`;
  notificationDiv.innerHTML = `
    <div class="notification-title">${title}</div>
    <div class="notification-content">${content}</div>
    <div class="notification-time">${new Date(timestamp).toLocaleTimeString()}</div>
  `;

  // 添加到通知区域
  const notificationsContainer = document.getElementById('notifications-container');
  if (notificationsContainer) {
    // 移除空提示
    const emptyNotice = notificationsContainer.querySelector('.empty-notice');
    if (emptyNotice) {
      notificationsContainer.removeChild(emptyNotice);
    }

    notificationsContainer.appendChild(notificationDiv);
    notificationsContainer.scrollTop = notificationsContainer.scrollHeight;

    console.log('✅ 系统通知已显示在页面上');

    // 5秒后自动移除通知
    setTimeout(() => {
      if (notificationDiv.parentNode) {
        notificationDiv.classList.add('fade-out');
        setTimeout(() => {
          if (notificationDiv.parentNode) {
            notificationsContainer.removeChild(notificationDiv);
          }
        }, 500);
      }
    }, 5000);
  } else {
    console.error('❌ 找不到通知容器元素');
  }
}