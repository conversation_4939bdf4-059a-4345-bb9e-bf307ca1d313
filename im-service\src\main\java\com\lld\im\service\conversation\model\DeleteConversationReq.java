package com.lld.im.service.conversation.model;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除会话请求
 */
@ApiModel(description = "删除会话请求模型")
@Data
public class DeleteConversationReq extends RequestBase {

    @ApiModelProperty(value = "会话ID", required = true, example = "conv_123456", notes = "要删除的会话唯一标识")
    @NotBlank(message = "会话id不能为空")
    private String conversationId;

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "执行删除操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

}
