package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 检查好友关系请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "检查好友关系请求模型")
@Data
public class CheckFriendShipReq extends RequestBase {

    @ApiModelProperty(value = "发起者用户ID", required = true, example = "user123")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "目标用户ID列表", required = true, example = "[\"user456\", \"user789\"]")
    @NotEmpty(message = "toIds不能为空")
    private List<String> toIds;

    @ApiModelProperty(value = "检查类型(1:单向检查 2:双向检查)", required = true, example = "1", notes = "1-单向检查 2-双向检查")
    @NotNull(message = "checkType不能为空")
    private Integer checkType;
}
