package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 修改用户信息请求
 */
@ApiModel(description = "修改用户信息请求模型")
@Data
public class ModifyUserInfoReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要修改信息的用户唯一标识")
    @NotEmpty(message = "{validation.user.id.not.blank}")
    private String userId;

    @ApiModelProperty(value = "用户昵称", example = "张三", notes = "用户的显示昵称")
    private String nickName;

    @ApiModelProperty(value = "用户位置", example = "北京市朝阳区", notes = "用户所在地理位置")
    private String location;

    @ApiModelProperty(value = "生日", example = "1990-01-01", notes = "用户生日，格式：YYYY-MM-DD")
    private String birthDay;

    @ApiModelProperty(value = "密码", example = "123456", notes = "用户登录密码")
    private String password;

    @ApiModelProperty(value = "头像URL", example = "https://example.com/avatar.jpg", notes = "用户头像图片地址")
    private String photo;

    @ApiModelProperty(value = "性别", example = "M", notes = "用户性别：M-男性 F-女性 U-未知")
    private String userSex;

    @ApiModelProperty(value = "个性签名", example = "这个人很懒，什么都没留下", notes = "用户的个性签名")
    private String selfSignature;

    @ApiModelProperty(value = "加好友验证类型", example = "1", notes = "0-不需要验证 1-需要验证")
    private Integer friendAllowType;

    @ApiModelProperty(value = "扩展字段", example = "{\"hobby\": \"reading\"}", notes = "用户的扩展信息")
    private String extra;

}
