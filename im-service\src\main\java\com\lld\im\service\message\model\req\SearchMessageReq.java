package com.lld.im.service.message.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;

/**
 * 搜索消息请求
 * @description: 模糊搜索会话聊天内容的请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "搜索消息请求模型")
@Data
public class SearchMessageReq extends RequestBase {

    @ApiModelProperty(value = "搜索关键词", required = true, example = "hello", notes = "搜索的关键词，支持模糊匹配")
    @NotBlank(message = "{validation.keyword.not.blank}")
    @Size(min = 1, max = 100, message = "{validation.keyword.size}")
    private String keyword;

    @ApiModelProperty(value = "搜索用户ID", required = true, example = "user123", notes = "发起搜索的用户ID，用于权限验证")
    @NotBlank(message = "{validation.user.id.not.blank}")
    private String userId;

    @ApiModelProperty(value = "会话ID", example = "0_user123_user456", required = true, notes = "要搜索的会话ID，格式：0_userId1_userId2(单聊) 或 1_userId_groupId(群聊)")
    @NotBlank(message = "{validation.conversation.id.not.blank}")
    private String conversationId;

    @ApiModelProperty(value = "开始时间", example = "1640995200000", notes = "搜索开始时间戳，单位毫秒（可选）")
    private Long startTime;

    @ApiModelProperty(value = "结束时间", example = "1640995200000", notes = "搜索结束时间戳，单位毫秒（可选）")
    private Long endTime;



    @ApiModelProperty(value = "排序方式", example = "DESC", notes = "排序方式：ASC-升序，DESC-降序，默认降序（最新消息在前）")
    @Pattern(regexp = "^(ASC|DESC)$", message = "{validation.order.by.pattern}")
    private String orderBy = "DESC";

    @ApiModelProperty(value = "高亮开始标签", example = "<mark>", notes = "自定义高亮开始标签，默认为<em>")
    private String highlightStartTag = "<em>";

    @ApiModelProperty(value = "高亮结束标签", example = "</mark>", notes = "自定义高亮结束标签，默认为</em>")
    private String highlightEndTag = "</em>";

    @ApiModelProperty(value = "片段长度", example = "50", notes = "高亮片段中关键词前后显示的字符数，默认50")
    @Min(value = 10, message = "{validation.fragment.length.min}")
    @Max(value = 200, message = "{validation.fragment.length.max}")
    private Integer fragmentLength = 50;

    @ApiModelProperty(value = "是否启用HTML转义", example = "true", notes = "是否对消息内容进行HTML转义，默认true")
    private Boolean enableHtmlEscape = true;

}
