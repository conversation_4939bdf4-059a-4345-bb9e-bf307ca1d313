package com.lld.im.service.group.model.resp;

import com.lld.im.service.group.model.req.UserApplyDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户最近群组申请记录响应
 * @description: 用户查询自己在指定群组的最近申请记录响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户最近群组申请记录响应模型")
@Data
public class UserApplyListResp {

    @ApiModelProperty(value = "最近申请记录", notes = "用户在指定群组的最近申请记录，如果没有申请记录则为null")
    private UserApplyDto latestApply;
}
