lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  # Redis集群模式配置 - 远程Docker集群环境
  redis:
    mode: cluster # 使用集群模式
    database: 0
    password: 123456
    timeout: 5000         # 远程连接增加超时时间
    poolMinIdle: 8
    poolConnTimeout: 5000 # 远程连接增加连接超时
    poolSize: 15          # 远程连接增加连接池大小
    
    cluster:
      nodes:
        # 请替换为实际的远程服务器IP地址
        - *************:7000    # redis-node-1 远程地址 (主节点，槽位: 0-5460)
        - *************:7001    # redis-node-2 远程地址 (主节点，槽位: 5461-10922)
        - *************:7002    # redis-node-3 远程地址 (主节点，槽位: 10923-16383)
        # 如果是多台服务器部署，可以配置如下：
        # - *************:7000    # 服务器1 redis-node-1
        # - *************:7000    # 服务器2 redis-node-2  
        # - *************:7000    # 服务器3 redis-node-3
      maxRedirects: 5      # 远程集群增加重定向次数
      scanInterval: 2000   # 远程集群增加扫描间隔（毫秒）
      # 远程集群连接优化参数
      retryAttempts: 3     # 重试次数
      retryInterval: 1500  # 重试间隔（毫秒）

  # RabbitMQ集群模式配置 - 远程Docker集群环境
  rabbitmq:
    # 集群模式配置
    addresses:
      # 请替换为实际的远程服务器IP地址
      - host: *************
        port: 5672      # rabbitmq-node-1 远程地址
      - host: *************
        port: 5673      # rabbitmq-node-2 远程地址
      - host: *************
        port: 5674      # rabbitmq-node-3 远程地址
      # 如果是多台服务器部署，可以配置如下：
      # - host: *************
      #   port: 5672    # 服务器1 rabbitmq-node-1
      # - host: *************  
      #   port: 5672    # 服务器2 rabbitmq-node-2
      # - host: *************
      #   port: 5672    # 服务器3 rabbitmq-node-3
    virtualHost: /
    userName: admin
    password: admin123
    connectionTimeout: 10000      # 远程连接增加超时时间
    requestedHeartbeat: 60        # 远程连接增加心跳间隔
    networkRecoveryInterval: 10000 # 远程连接增加网络恢复间隔
    automaticRecoveryEnabled: true
    # 远程连接优化参数
    channelCacheSize: 50          # 增加通道缓存
    publisherConfirms: true       # 启用发布确认
    publisherReturns: true        # 启用发布返回

  # Nacos集群配置 - 远程环境
  nacosConfig:
    # 请替换为实际的远程Nacos服务器地址
    serverAddr: *************:8848,*************:8848,*************:8848
    namespace: im-system
    group: DEFAULT_GROUP
    username: nacos
    password: nacos
    connectTimeout: 5000
    readTimeout: 10000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId

# 远程集群环境说明
# 
# 网络要求：
# 1. 确保远程服务器防火墙开放以下端口：
#    - Redis: 7000-7002 (数据端口), 17000-17002 (集群总线端口)
#    - RabbitMQ: 5672-5674 (AMQP端口), 15672-15674 (管理界面端口)
#    - Nacos: 8848 (服务端口)
#
# 2. 网络延迟优化：
#    - 增加了连接超时时间
#    - 增加了重试次数和间隔
#    - 增加了连接池大小
#
# 3. 安全建议：
#    - 修改默认密码
#    - 配置VPN或专线连接
#    - 启用SSL/TLS加密（如果支持）
#
# 4. 监控建议：
#    - 监控网络延迟
#    - 监控连接池使用情况
#    - 监控集群节点状态
