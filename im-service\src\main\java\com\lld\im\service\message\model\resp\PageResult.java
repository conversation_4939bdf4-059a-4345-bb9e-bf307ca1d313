package com.lld.im.service.message.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 分页结果模型
 * @description: 通用分页查询结果模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "分页结果模型")
@Data
public class PageResult<T> {

    @ApiModelProperty(value = "数据列表", notes = "当前页的数据列表")
    private List<T> list;

    @ApiModelProperty(value = "总记录数", example = "100", notes = "符合条件的总记录数")
    private Long total;

    @ApiModelProperty(value = "当前页码", example = "1", notes = "当前页码，从1开始")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小", example = "20", notes = "每页显示的记录数")
    private Integer pageSize;

    @ApiModelProperty(value = "总页数", example = "5", notes = "总页数")
    private Integer totalPages;

    @ApiModelProperty(value = "是否有下一页", example = "true", notes = "是否还有下一页数据")
    private Boolean hasNext;

    @ApiModelProperty(value = "是否有上一页", example = "false", notes = "是否还有上一页数据")
    private Boolean hasPrevious;

    /**
     * 构造分页结果
     */
    public static <T> PageResult<T> of(List<T> list, Long total, Integer pageNum, Integer pageSize) {
        PageResult<T> result = new PageResult<>();
        result.setList(list);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / pageSize);
        result.setTotalPages(totalPages);

        // 计算是否有上一页和下一页
        result.setHasPrevious(pageNum > 1);
        result.setHasNext(pageNum < totalPages);

        return result;
    }

}