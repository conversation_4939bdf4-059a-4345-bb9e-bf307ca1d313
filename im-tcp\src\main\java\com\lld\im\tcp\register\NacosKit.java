package com.lld.im.tcp.register;

import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * @description: Nacos工具类，用于TCP模块的服务注册
 * @author: lld
 * @version: 1.0
 */
public class NacosKit {

    private static Logger logger = LoggerFactory.getLogger(NacosKit.class);

    private NamingService namingService;

    public NacosKit(NamingService namingService) {
        this.namingService = namingService;
    }

    /**
     * 注册TCP服务实例
     *
     * @param ip       服务IP地址
     * @param port     服务端口
     * @param metadata 元数据信息
     * @throws Exception 注册异常
     */
    public void registerTcpInstance(String ip, int port, Map<String, String> metadata) throws Exception {
        Instance instance = new Instance();
        instance.setIp(ip);
        instance.setPort(port);
        instance.setMetadata(metadata);
        instance.setHealthy(true);
        instance.setEnabled(true);
        instance.setWeight(1.0);

        namingService.registerInstance("im-tcp-service", instance);
        logger.info("Register TCP service instance success: {}:{}", ip, port);
    }

    /**
     * 注册WebSocket服务实例
     *
     * @param ip       服务IP地址
     * @param port     服务端口
     * @param metadata 元数据信息
     * @throws Exception 注册异常
     */
    public void registerWebSocketInstance(String ip, int port, Map<String, String> metadata) throws Exception {
        Instance instance = new Instance();
        instance.setIp(ip);
        instance.setPort(port);
        instance.setMetadata(metadata);
        instance.setHealthy(true);
        instance.setEnabled(true);
        instance.setWeight(1.0);

        namingService.registerInstance("im-websocket-service", instance);
        logger.info("Register WebSocket service instance success: {}:{}", ip, port);
    }

    /**
     * 注销TCP服务实例
     *
     * @param ip   服务IP地址
     * @param port 服务端口
     * @throws Exception 注销异常
     */
    public void deregisterTcpInstance(String ip, int port) throws Exception {
        namingService.deregisterInstance("im-tcp-service", ip, port);
        logger.info("Deregister TCP service instance success: {}:{}", ip, port);
    }

    /**
     * 注销WebSocket服务实例
     *
     * @param ip   服务IP地址
     * @param port 服务端口
     * @throws Exception 注销异常
     */
    public void deregisterWebSocketInstance(String ip, int port) throws Exception {
        namingService.deregisterInstance("im-websocket-service", ip, port);
        logger.info("Deregister WebSocket service instance success: {}:{}", ip, port);
    }

    /**
     * 检查服务是否可用
     *
     * @return true if available
     */
    public boolean isServiceAvailable() {
        try {
            return namingService.getServerStatus().equals("UP");
        } catch (Exception e) {
            logger.error("Check Nacos service status failed", e);
            return false;
        }
    }
}
