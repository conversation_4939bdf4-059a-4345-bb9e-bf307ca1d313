package com.lld.im.codec.pack.conversation;

import lombok.Data;

/**
 * @description: 创建会话通知数据包
 * @author: lld
 * @version: 1.0
 */
@Data
public class CreateConversationPack {

    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 会话类型：0-单聊，1-群聊
     */
    private Integer conversationType;

    /**
     * 发起方用户ID
     */
    private String fromId;

    /**
     * 目标用户ID（单聊）或群组ID（群聊）
     */
    private String toId;

    /**
     * 会话序列号
     */
    private Long sequence;

    /**
     * 最新消息序列号
     */
    private Long lastMessageSequence;

    /**
     * 是否置顶：0-否，1-是
     */
    private Integer isTop = 0;

    /**
     * 是否免打扰：0-否，1-是
     */
    private Integer isMute = 0;

    /**
     * 已读消息序列号
     */
    private Long readedSequence = 0L;

}
