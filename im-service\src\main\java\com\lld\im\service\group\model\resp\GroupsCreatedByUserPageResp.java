package com.lld.im.service.group.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 用户创建的群组列表分页响应
 * @description: 分页查询用户创建的群组列表响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户创建的群组列表分页响应")
@Data
public class GroupsCreatedByUserPageResp {

    @ApiModelProperty(value = "群组列表", notes = "当前页的群组数据列表")
    private List<GroupCreatedByUserResp> groupList;

    @ApiModelProperty(value = "总记录数", example = "100", notes = "符合条件的总记录数")
    private Long total;

    @ApiModelProperty(value = "当前页码", example = "1", notes = "当前页码，从1开始")
    private Integer pageNum;

    @ApiModelProperty(value = "每页大小", example = "20", notes = "每页显示的记录数")
    private Integer pageSize;

    @ApiModelProperty(value = "总页数", example = "5", notes = "总页数")
    private Integer totalPages;

    @ApiModelProperty(value = "是否有下一页", example = "true", notes = "是否还有下一页数据")
    private Boolean hasNext;

    @ApiModelProperty(value = "是否有上一页", example = "false", notes = "是否还有上一页数据")
    private Boolean hasPrevious;

    /**
     * 构造分页结果
     */
    public static GroupsCreatedByUserPageResp of(List<GroupCreatedByUserResp> groupList, Long total, Integer pageNum, Integer pageSize) {
        GroupsCreatedByUserPageResp result = new GroupsCreatedByUserPageResp();
        result.setGroupList(groupList);
        result.setTotal(total);
        result.setPageNum(pageNum);
        result.setPageSize(pageSize);

        // 计算总页数
        int totalPages = (int) Math.ceil((double) total / pageSize);
        result.setTotalPages(totalPages);

        // 计算是否有上一页和下一页
        result.setHasPrevious(pageNum > 1);
        result.setHasNext(pageNum < totalPages);

        return result;
    }
}
