package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除黑名单请求
 */
@ApiModel(description = "删除黑名单请求模型")
@Data
public class DeleteBlackReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起移除黑名单操作的用户ID")
    @NotBlank(message = "用户id不能为空")
    private String fromId;

    @ApiModelProperty(value = "被移除黑名单用户ID", required = true, example = "user456", notes = "要从黑名单中移除的用户ID")
    @NotBlank(message = "好友id不能为空")
    private String toId;

}
