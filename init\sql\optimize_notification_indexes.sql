-- 系统通知数据库索引优化脚本
-- 开发阶段优化，无需考虑兼容性

-- =====================================================
-- 1. 优化现有索引
-- =====================================================

-- 为im_system_notification表添加复合索引
-- 优化按应用ID和创建时间查询
ALTER TABLE `im_system_notification` 
ADD INDEX `idx_app_create_time` (`app_id`, `create_time` DESC);

-- 优化按应用ID和通知类型查询
ALTER TABLE `im_system_notification` 
ADD INDEX `idx_app_type_time` (`app_id`, `notification_type`, `create_time` DESC);

-- 为im_user_system_notification表添加优化索引
-- 优化未读通知数量查询（覆盖索引）
ALTER TABLE `im_user_system_notification` 
ADD INDEX `idx_user_read_status` (`app_id`, `user_id`, `read_status`, `notification_id`);

-- 优化分页查询性能（覆盖索引，包含所有需要的字段）
ALTER TABLE `im_user_system_notification` 
ADD INDEX `idx_user_create_covering` (`app_id`, `user_id`, `create_time` DESC, `notification_id`, `read_status`, `sequence`);

-- =====================================================
-- 2. 数据库配置优化建议
-- =====================================================

-- 批量插入优化配置（在application.yml中配置）
-- spring:
--   datasource:
--     hikari:
--       maximum-pool-size: 20
--       minimum-idle: 5
--       connection-timeout: 30000
--       idle-timeout: 600000
--       max-lifetime: 1800000
--       leak-detection-threshold: 60000

-- MyBatis-Plus批量插入优化
-- mybatis-plus:
--   configuration:
--     batch-size: 1000
--     default-executor-type: batch

-- =====================================================
-- 3. 重建表统计信息
-- =====================================================

ANALYZE TABLE `im_system_notification`;
ANALYZE TABLE `im_user_system_notification`;

SELECT 'Database indexes optimization completed!' as status;
