package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息体模型
 * @description: 消息体数据模型，用于存储消息的具体内容
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "消息体模型")
@Data
public class ImMessageBody {

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "消息体ID", required = true, example = "123456789", notes = "消息体的唯一标识")
    private Long messageKey;

    @ApiModelProperty(value = "消息内容", required = true, example = "Hello, this is a test message", notes = "消息的具体内容")
    private String messageBody;

    @ApiModelProperty(value = "安全密钥", example = "seckey123", notes = "消息加密的安全密钥")
    private String securityKey;

    @ApiModelProperty(value = "消息时间", required = true, example = "1640995200000", notes = "消息发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "创建时间", required = true, example = "1640995200000", notes = "消息创建时间戳，单位毫秒")
    private Long createTime;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\"}", notes = "消息的扩展信息")
    private String extra;

    @ApiModelProperty(value = "删除标识 (0:未删除 1:已删除)", example = "0", notes = "消息体删除状态标识")
    private Integer delFlag;
}
