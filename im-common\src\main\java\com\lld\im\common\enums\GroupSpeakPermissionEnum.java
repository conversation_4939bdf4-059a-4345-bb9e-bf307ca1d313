package com.lld.im.common.enums;

/**
 * 群组发言权限枚举
 * @author: lld
 * @description: 定义群组中不同的发言权限级别
 */
public enum GroupSpeakPermissionEnum {
    
    /**
     * 允许所有人发言（默认）
     */
    ALL_MEMBERS(0, "所有人可发言"),
    
    /**
     * 仅群主和管理员可发言
     */
    MANAGERS_ONLY(1, "仅群主/管理员可发言"),
    
    /**
     * 指定成员可发言
     */
    SPECIFIED_MEMBERS(2, "指定成员可发言"),
    
    /**
     * 禁止所有人发言
     */
    NOBODY(3, "禁止发言");
    
    private final int code;
    private final String description;
    
    GroupSpeakPermissionEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举值
     * @param code 权限代码
     * @return 对应的枚举值，如果不存在则返回ALL_MEMBERS
     */
    public static GroupSpeakPermissionEnum getByCode(int code) {
        for (GroupSpeakPermissionEnum permission : values()) {
            if (permission.getCode() == code) {
                return permission;
            }
        }
        return ALL_MEMBERS; // 默认返回所有人可发言
    }
    
    /**
     * 验证权限代码是否有效
     * @param code 权限代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        for (GroupSpeakPermissionEnum permission : values()) {
            if (permission.getCode() == code) {
                return true;
            }
        }
        return false;
    }
}
