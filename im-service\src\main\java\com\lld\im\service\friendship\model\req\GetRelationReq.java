package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 获取好友关系请求
 */
@ApiModel(description = "获取好友关系请求模型")
@Data
public class GetRelationReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "查询关系的发起用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "目标用户ID", required = true, example = "user456", notes = "查询关系的目标用户ID")
    @NotBlank(message = "toId不能为空")
    private String toId;
}
