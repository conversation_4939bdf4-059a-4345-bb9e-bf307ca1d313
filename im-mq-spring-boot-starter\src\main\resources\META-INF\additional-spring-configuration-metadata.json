{"properties": [{"name": "im.mq.enabled", "type": "java.lang.Bo<PERSON>an", "description": "是否启用IM MQ功能", "defaultValue": true}, {"name": "im.mq.notification.exchange", "type": "java.lang.String", "description": "系统通知交换机名称", "defaultValue": "im.notification.exchange"}, {"name": "im.mq.notification.routing-key", "type": "java.lang.String", "description": "系统通知路由键", "defaultValue": "im.notification"}, {"name": "im.mq.notification.exchange-type", "type": "java.lang.String", "description": "系统通知交换机类型", "defaultValue": "topic"}, {"name": "im.mq.notification.durable", "type": "java.lang.Bo<PERSON>an", "description": "系统通知交换机是否持久化", "defaultValue": true}, {"name": "im.mq.liveroom.exchange", "type": "java.lang.String", "description": "直播间消息交换机名称", "defaultValue": "im.liveroom.exchange"}, {"name": "im.mq.liveroom.routing-key", "type": "java.lang.String", "description": "直播间消息路由键", "defaultValue": "im.liveroom"}, {"name": "im.mq.liveroom.exchange-type", "type": "java.lang.String", "description": "直播间消息交换机类型", "defaultValue": "topic"}, {"name": "im.mq.liveroom.durable", "type": "java.lang.Bo<PERSON>an", "description": "直播间消息交换机是否持久化", "defaultValue": true}]}