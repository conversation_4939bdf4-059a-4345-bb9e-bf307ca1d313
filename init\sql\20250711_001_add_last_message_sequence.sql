-- 为会话预创建机制添加 last_message_sequence 字段
-- 该字段用于记录会话中最新消息的序列号，用于未读数量计算

-- 检查字段是否存在，如果不存在则添加
SET @sql = (
    SELECT IF(
        (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() 
         AND TABLE_NAME = 'im_conversation_set' 
         AND COLUMN_NAME = 'last_message_sequence') = 0,
        'ALTER TABLE im_conversation_set ADD COLUMN last_message_sequence BIGINT(20) NULL DEFAULT 0 COMMENT ''最新消息序列号'';',
        'SELECT ''Column last_message_sequence already exists'' AS message;'
    )
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 为现有记录设置默认值
UPDATE im_conversation_set SET last_message_sequence = 0 WHERE last_message_sequence IS NULL;

-- 添加注释说明
ALTER TABLE im_conversation_set MODIFY COLUMN last_message_sequence BIGINT(20) NULL DEFAULT 0 COMMENT '最新消息序列号 - 用于未读数量计算';
