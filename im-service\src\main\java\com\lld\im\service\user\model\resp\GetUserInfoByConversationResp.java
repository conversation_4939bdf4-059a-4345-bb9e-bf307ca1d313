package com.lld.im.service.user.model.resp;

import com.lld.im.service.user.dao.ImUserDataEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 基于会话关系获取用户信息响应
 * 
 * <AUTHOR>
 */
@ApiModel(description = "基于会话关系获取用户信息响应模型")
@Data
public class GetUserInfoByConversationResp {

    @ApiModelProperty(value = "成功获取的用户信息列表", notes = "包含用户详细信息的列表")
    private List<ImUserDataEntity> userDataList;

    @ApiModelProperty(value = "用户关系类型映射", notes = "用户ID到关系类型的映射，1-好友关系，2-会话关系，3-好友+会话关系")
    private Map<String, Integer> relationTypeMap;

    @ApiModelProperty(value = "无权限访问的用户ID列表", notes = "没有好友关系或会话关系的用户ID列表")
    private List<String> noPermissionUsers;

    @ApiModelProperty(value = "不存在的用户ID列表", notes = "系统中不存在的用户ID列表")
    private List<String> notExistUsers;
}
