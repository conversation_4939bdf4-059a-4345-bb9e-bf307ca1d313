package com.lld.im.service.message.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 搜索消息响应
 * @description: 消息搜索结果的响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "搜索消息响应模型")
@Data
public class SearchMessageResp {

    @ApiModelProperty(value = "消息ID", example = "123456789", notes = "消息的唯一标识")
    private Long messageKey;

    @ApiModelProperty(value = "发送者ID", example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者ID", example = "user456", notes = "消息接收者的用户ID或群组ID")
    private String toId;

    @ApiModelProperty(value = "消息内容", example = "Hello, this is a test message", notes = "消息的具体内容")
    private String messageBody;

    @ApiModelProperty(value = "消息时间", example = "1640995200000", notes = "消息发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "创建时间", example = "1640995200000", notes = "消息创建时间戳，单位毫秒")
    private Long createTime;

    @ApiModelProperty(value = "消息序列号", example = "1001", notes = "消息的序列号，用于排序")
    private Long sequence;

    @ApiModelProperty(value = "消息随机数", example = "12345", notes = "消息的随机数，用于去重")
    private Integer messageRandom;

    @ApiModelProperty(value = "会话ID", example = "0_user1_user2", notes = "消息所属的会话ID")
    private String conversationId;

    @ApiModelProperty(value = "会话类型", example = "0", notes = "会话类型：0-单聊，1-群聊")
    private Integer conversationType;

    @ApiModelProperty(value = "消息类型", example = "1", notes = "消息类型：1-文本，2-图片，3-语音，4-视频，5-表情包")
    private String messageType;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\"}", notes = "消息的扩展信息")
    private String extra;

    @ApiModelProperty(value = "高亮消息内容", example = "Hello, this is a <em>test</em> message", notes = "带有高亮标记的消息内容")
    private String highlightedMessageBody;

    @ApiModelProperty(value = "匹配片段列表", notes = "包含关键词的消息片段列表，用于展示搜索上下文")
    private List<HighlightFragment> highlightFragments;

    /**
     * 高亮片段信息
     */
    @ApiModel(description = "高亮片段模型")
    @Data
    public static class HighlightFragment {

        @ApiModelProperty(value = "片段内容", example = "this is a test message", notes = "包含关键词的消息片段")
        private String fragment;

        @ApiModelProperty(value = "高亮片段", example = "this is a <em>test</em> message", notes = "带有高亮标记的片段内容")
        private String highlightedFragment;

        @ApiModelProperty(value = "片段在原文中的起始位置", example = "10", notes = "片段在原始消息中的起始字符位置")
        private Integer startIndex;

        @ApiModelProperty(value = "片段在原文中的结束位置", example = "30", notes = "片段在原始消息中的结束字符位置")
        private Integer endIndex;
    }

}
