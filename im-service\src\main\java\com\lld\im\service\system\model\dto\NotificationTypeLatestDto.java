package com.lld.im.service.system.model.dto;

import lombok.Data;

/**
 * 通知类型最新记录数据传输对象
 * @description: 用于数据库查询结果映射的DTO
 * @author: lld
 * @version: 1.0
 */
@Data
public class NotificationTypeLatestDto {

    /**
     * 通知ID
     */
    private Long notificationId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 通知类型
     */
    private Integer notificationType;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 扩展字段(JSON)
     */
    private String extra;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 读取状态 0未读 1已读
     */
    private Integer readStatus;

    /**
     * 读取时间
     */
    private Long readTime;

    /**
     * 序列号
     */
    private Long sequence;
}
