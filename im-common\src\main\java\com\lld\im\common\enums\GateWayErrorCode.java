package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 6
 **/
public enum GateWayErrorCode implements ApplicationExceptionEnum {

    USERSIGN_NOT_EXIST(60000,"gateway.user.sign.not.exist"),

    APPID_NOT_EXIST(60001,"gateway.app.id.not.exist"),

    OPERATER_NOT_EXIST(60002,"gateway.operator.not.exist"),

    USERSIGN_IS_ERROR(60003,"gateway.user.sign.error"),

    USERSIGN_OPERATE_NOT_MATE(60005,"gateway.user.sign.operator.not.match"),

    USERSIGN_IS_EXPIRED(60004,"gateway.user.sign.expired"),

    ;

    private int code;
    private String messageKey;

    GateWayErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case USERSIGN_NOT_EXIST:
                return "用户签名不存在";
            case APPID_NOT_EXIST:
                return "appId不存在";
            case OPERATER_NOT_EXIST:
                return "操作人不存在";
            case USERSIGN_IS_ERROR:
                return "用户签名不正确";
            case USERSIGN_OPERATE_NOT_MATE:
                return "用户签名与操作人不匹配";
            case USERSIGN_IS_EXPIRED:
                return "用户签名已过期";
            default:
                return this.messageKey;
        }
    }

}
