-- 通知类型分组查询性能优化索引脚本
-- 执行时间：2025-07-10
-- 目的：为分步查询优化添加必要的数据库索引，提升查询性能

-- =====================================================
-- 1. 分步查询优化索引
-- =====================================================

-- 优化第一步查询：获取用户通知类型列表
-- 支持查询：SELECT DISTINCT sn.notification_type FROM im_user_system_notification un INNER JOIN im_system_notification sn
CREATE INDEX IF NOT EXISTS idx_user_notification_type_optimized 
ON im_user_system_notification(app_id, user_id, notification_id);

-- 为系统通知表添加del_flag索引，优化过滤条件
CREATE INDEX IF NOT EXISTS idx_system_notification_del_flag 
ON im_system_notification(del_flag, notification_type);

-- 优化第二步查询：按类型获取最新通知
-- 支持查询：ORDER BY create_time DESC, notification_id DESC LIMIT 1
CREATE INDEX IF NOT EXISTS idx_notification_type_time_id 
ON im_system_notification(app_id, notification_type, del_flag, create_time DESC, notification_id DESC);

-- 优化第三步查询：按类型统计未读数量
-- 支持查询：COUNT(*) WHERE read_status = 0 AND notification_type = ?
CREATE INDEX IF NOT EXISTS idx_user_read_status_type 
ON im_user_system_notification(app_id, user_id, read_status, notification_id);

-- 为用户通知关系表添加通知ID索引，优化JOIN查询
CREATE INDEX IF NOT EXISTS idx_user_notification_id_optimized 
ON im_user_system_notification(notification_id, app_id, user_id, read_status);

-- =====================================================
-- 2. 复合索引优化（覆盖索引）
-- =====================================================

-- 为系统通知表创建覆盖索引，减少回表查询
CREATE INDEX IF NOT EXISTS idx_system_notification_covering 
ON im_system_notification(app_id, notification_type, del_flag, create_time DESC, notification_id, title, content, extra);

-- 为用户通知关系表创建覆盖索引
CREATE INDEX IF NOT EXISTS idx_user_notification_covering 
ON im_user_system_notification(app_id, user_id, notification_id, read_status, read_time, sequence, create_time);

-- =====================================================
-- 3. 清理可能重复的索引
-- =====================================================

-- 检查是否存在重复索引（手动执行以下查询来检查）
/*
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) AS COLUMNS
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME IN ('im_system_notification', 'im_user_system_notification')
GROUP BY TABLE_NAME, INDEX_NAME
ORDER BY TABLE_NAME, INDEX_NAME;
*/

-- =====================================================
-- 4. 统计信息更新
-- =====================================================

-- 更新表统计信息，帮助查询优化器选择最佳执行计划
ANALYZE TABLE im_system_notification;
ANALYZE TABLE im_user_system_notification;

-- =====================================================
-- 5. 性能监控建议
-- =====================================================

-- 启用慢查询日志监控（在my.cnf中配置）
-- slow_query_log = 1
-- slow_query_log_file = /var/log/mysql/slow.log
-- long_query_time = 0.5

-- 监控查询执行计划的SQL示例：
/*
-- 监控原始复杂JOIN查询
EXPLAIN SELECT n.notification_id, n.app_id, n.notification_type, n.title, n.content, n.extra, n.create_time,
        COALESCE(un.read_status, 0) as read_status, un.read_time, un.sequence
FROM im_system_notification n
INNER JOIN (
    SELECT notification_type, MAX(create_time) as max_create_time
    FROM im_system_notification
    WHERE app_id = 10000 AND del_flag = 0
    GROUP BY notification_type
) latest ON n.notification_type = latest.notification_type AND n.create_time = latest.max_create_time
LEFT JOIN im_user_system_notification un ON n.notification_id = un.notification_id 
    AND un.app_id = 10000 AND un.user_id = 'test_user'
WHERE n.app_id = 10000 AND n.del_flag = 0
ORDER BY n.notification_type ASC;

-- 监控优化后的分步查询
EXPLAIN SELECT DISTINCT sn.notification_type
FROM im_user_system_notification un
INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
WHERE un.app_id = 10000 AND un.user_id = 'test_user' AND sn.del_flag = 0
ORDER BY sn.notification_type ASC;

EXPLAIN SELECT n.notification_id, n.app_id, n.notification_type, n.title, n.content, n.extra, n.create_time,
        COALESCE(un.read_status, 0) as read_status, un.read_time, un.sequence
FROM im_system_notification n
LEFT JOIN im_user_system_notification un ON n.notification_id = un.notification_id 
    AND un.app_id = 10000 AND un.user_id = 'test_user'
WHERE n.app_id = 10000 AND n.notification_type = 1 AND n.del_flag = 0
ORDER BY n.create_time DESC, n.notification_id DESC
LIMIT 1;

EXPLAIN SELECT COUNT(*) as unread_count
FROM im_user_system_notification un
INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
WHERE un.app_id = 10000 AND un.user_id = 'test_user' AND un.read_status = 0 
    AND sn.notification_type = 1 AND sn.del_flag = 0;
*/

-- =====================================================
-- 6. 执行完成提示
-- =====================================================

SELECT 'Notification query performance optimization completed!' as status,
       'Please run EXPLAIN on your queries to verify index usage' as next_step,
       'Monitor slow query log for further optimization opportunities' as monitoring;
