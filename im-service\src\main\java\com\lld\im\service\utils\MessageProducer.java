package com.lld.im.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.common.ClientType;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.command.Command;
import com.lld.im.common.enums.command.MessageCommand;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.UserSession;
import com.lld.im.service.message.model.BroadcastMessageContent;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
public class MessageProducer implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnCallback {

    private static Logger logger = LoggerFactory.getLogger(MessageProducer.class);

    @Autowired
    RabbitTemplate rabbitTemplate;

    @Autowired
    UserSessionUtils userSessionUtils;

    private String queueName = Constants.RabbitConstants.MessageService2Im;

    // 初始化回调
    public MessageProducer(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnCallback(this);
    }

    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        String id = correlationData != null ? correlationData.getId() : "";
        if (ack) {
            logger.info("消息投递成功，消息ID: {}", id);
        } else {
            logger.error("消息投递到交换机失败，消息ID: {}, 原因: {}", id, cause);
        }
    }

    @Override
    public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
        try {
            logger.error("消息从交换机路由到队列失败: exchange: {}, routingKey: {}, replyCode: {}, replyText: {}, 消息内容: {}",
                    exchange, routingKey, replyCode, replyText, new String(message.getBody(), "UTF-8"));
        } catch (Exception e) {
            logger.error("消息从交换机路由到队列失败: exchange: {}, routingKey: {}, replyCode: {}, replyText: {}",
                    exchange, routingKey, replyCode, replyText);
        }
    }

    public boolean sendMessage(UserSession session, Object msg) {
        try {
            String msgId = UUID.randomUUID().toString();
            CorrelationData correlationData = new CorrelationData(msgId);
            logger.info("发送消息，目标broker: {}, 消息ID: {}, 消息内容: {}", session.getBrokerId(), msgId, msg);
            rabbitTemplate.convertAndSend(queueName, session.getBrokerId()+"", msg, correlationData);
            return true;
        } catch (Exception e) {
            logger.error("消息发送异常: {}", e.getMessage(), e);
            return false;
        }
    }
    
    // 包装数据，调用sendMessage
    public boolean sendPack(String toId, Command command, Object msg, UserSession session) {
        MessagePack messagePack = new MessagePack();
        messagePack.setCommand(command.getCommand());
        messagePack.setToId(toId);
        messagePack.setClientType(session.getClientType());
        messagePack.setAppId(session.getAppId());
        messagePack.setImei(session.getImei());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(msg));
        messagePack.setData(jsonObject);

        String body = JSONObject.toJSONString(messagePack);
        return sendMessage(session, body);
    }

    // 发送给所有端的方法
    public List<ClientInfo> sendToUser(String toId, Command command, Object data, Integer appId) {
        List<UserSession> userSession = userSessionUtils.getUserSession(appId, toId);
        List<ClientInfo> list = new ArrayList<>();
        
        logger.info("准备向用户 {} 的 {} 个终端发送命令: {}", toId, userSession.size(), command.getCommand());
        
        for (UserSession session : userSession) {
            boolean b = sendPack(toId, command, data, session);
            if (b) {
                list.add(new ClientInfo(session.getAppId(), session.getClientType(), session.getImei()));
                logger.info("成功向用户 {} 的终端发送消息: clientType={}, imei={}", toId, session.getClientType(), session.getImei());
            } else {
                logger.warn("向用户 {} 的终端发送消息失败: clientType={}, imei={}", toId, session.getClientType(), session.getImei());
            }
        }
        
        logger.info("向用户 {} 发送消息完成，成功: {}/{}", toId, list.size(), userSession.size());
        return list;
    }

    public void sendToUser(String toId, Integer clientType,String imei, Command command,
                           Object data, Integer appId){
        if(clientType != null && StringUtils.isNotBlank(imei)){
            ClientInfo clientInfo = new ClientInfo(appId, clientType, imei);
            sendToUserExceptClient(toId,command,data,clientInfo);
        }else{
            sendToUser(toId,command,data,appId);
        }

    }

    //发送给某个用户的指定客户端
    public void sendToUser(String toId, Command command
            , Object data, ClientInfo clientInfo){
        UserSession userSession = userSessionUtils.getUserSession(clientInfo.getAppId(), toId, clientInfo.getClientType(),
                clientInfo.getImei());
        sendPack(toId,command,data,userSession);
    }

    private boolean isMatch(UserSession sessionDto, ClientInfo clientInfo) {
        return Objects.equals(sessionDto.getAppId(), clientInfo.getAppId())
                && Objects.equals(sessionDto.getImei(), clientInfo.getImei())
                && Objects.equals(sessionDto.getClientType(), clientInfo.getClientType());
    }

    //发送给除了某一端的其他端
    public void sendToUserExceptClient(String toId, Command command
            , Object data, ClientInfo clientInfo){
        List<UserSession> userSession = userSessionUtils
                .getUserSession(clientInfo.getAppId(),
                        toId);
        for (UserSession session : userSession) {
/*            if(!isMatch(session,clientInfo)){

            }*/
            sendPack(toId,command,data,session);
        }
    }

    /**
     * 发送消息给所有用户，包括游客
     * @param appId 应用ID
     * @param command 命令
     * @param data 数据
     * @param includeGuests 是否包括游客
     * @return 是否成功发送到MQ
     */
    public boolean sendToAllUsers(Integer appId, Integer command, Object data, boolean includeGuests) {
        String messageId = UUID.randomUUID().toString();
        logger.info("开始广播消息，ID: {}, 应用ID: {}, 命令: {}, 包含游客: {}", messageId, appId, command, includeGuests);
        
        // 创建广播消息内容
        JSONObject broadcastMsg = new JSONObject();
        broadcastMsg.put("appId", appId);
        broadcastMsg.put("command", command);
        broadcastMsg.put("data", data);
        broadcastMsg.put("includeGuests", includeGuests);

        // 发送到MQ，由im-tcp服务处理并广播给所有在线用户
        try {
            boolean result = sendMessage(appId, command, broadcastMsg, MessageCommand.MSG_BROADCAST.getCommand(), messageId);
            if (result) {
                logger.info("广播消息成功投递到MQ，消息ID: {}", messageId);
            } else {
                logger.error("广播消息投递到MQ失败，消息ID: {}", messageId);
            }
            return result;
        } catch (Exception e) {
            logger.error("发送广播消息失败，消息ID: {}, 错误: {}", messageId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 发送消息到RabbitMQ
     * @param appId 应用ID
     * @param command 命令
     * @param data 数据
     * @param type 消息类型
     * @param messageId 消息唯一ID
     * @return 是否成功发送
     */
    private boolean sendMessage(Integer appId, Integer command, Object data, Integer type, String messageId) {
        try {
            CorrelationData correlationData = new CorrelationData(messageId);
            
            // 消息属性与头信息
            MessagePostProcessor messagePostProcessor = message -> {
                message.getMessageProperties().setMessageId(messageId);
                message.getMessageProperties().setType(type.toString());
                message.getMessageProperties().setAppId(appId.toString());
                message.getMessageProperties().setTimestamp(new Date());
                return message;
            };
            
            rabbitTemplate.convertAndSend(queueName, String.valueOf(appId), 
                    JSONObject.toJSONString(data), messagePostProcessor, correlationData);
            return true;
        } catch (Exception e) {
            logger.error("发送消息到MQ失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 重载原有方法，增加消息ID
     */
    private void sendMessage(Integer appId, Integer command, Object data, int type) {
        sendMessage(appId, command, data, type, UUID.randomUUID().toString());
    }

    /**
     * 发送消息到指定的交换机和路由
     * @param exchange 交换机名称
     * @param routingKey 路由键
     * @param data 消息数据
     * @param appId 应用ID
     * @return 是否发送成功
     */
    public boolean sendMessage(String exchange, String routingKey, Object data, Integer appId) {
        String messageId = UUID.randomUUID().toString();
        logger.info("开始发送消息到交换机，ID: {}, 交换机: {}, 路由键: {}, 应用ID: {}", 
                messageId, exchange, routingKey, appId);
        
        try {
            CorrelationData correlationData = new CorrelationData(messageId);
            
            // 消息属性与头信息
            MessagePostProcessor messagePostProcessor = message -> {
                message.getMessageProperties().setMessageId(messageId);
                message.getMessageProperties().setAppId(appId.toString());
                message.getMessageProperties().setTimestamp(new Date());
                return message;
            };
            
            rabbitTemplate.convertAndSend(exchange, routingKey, 
                    JSONObject.toJSONString(data), messagePostProcessor, correlationData);
            logger.info("消息成功发送到交换机，ID: {}", messageId);
            return true;
        } catch (Exception e) {
            logger.error("发送消息到交换机失败: {}, 错误: {}", messageId, e.getMessage(), e);
            return false;
        }
    }
    


}
