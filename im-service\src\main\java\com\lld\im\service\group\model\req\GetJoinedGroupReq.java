package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 获取用户加入的群组列表请求
 */
@ApiModel(description = "获取用户加入的群组列表请求模型")
@Data
public class GetJoinedGroupReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要查询的用户唯一标识")
    @NotBlank(message = "用户id不能为空")
    private String memberId;

    @ApiModelProperty(value = "群组类型列表", example = "[1, 2]", notes = "筛选的群组类型：1-私有群 2-公开群，不填则查询所有类型")
    private List<Integer> groupType;

    @ApiModelProperty(value = "单次拉取数量", example = "20", notes = "分页查询的每页数量，不填则返回所有群组")
    private Integer limit;

    @ApiModelProperty(value = "偏移量", example = "0", notes = "分页查询的起始位置")
    private Integer offset;

}
