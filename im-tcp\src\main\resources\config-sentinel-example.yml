lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  # Redis哨兵模式配置示例
  redis:
    mode: sentinel # 使用哨兵模式
    database: 0
    password: 123456
    timeout: 3000
    poolMinIdle: 8
    poolConnTimeout: 3000
    poolSize: 10
    
    sentinel:
      masterName: mymaster
      sentinels:
        - ************:26379
        - ************:26379
        - ************:26379
      failoverTimeout: 2000  # 保留字段，当前Redisson版本使用默认值

  # RabbitMQ集群模式配置示例
  rabbitmq:
    # 集群模式配置
    addresses:
      - host: rabbit1
        port: 5672
      - host: rabbit2
        port: 5672
      - host: rabbit3
        port: 5672
    virtualHost: /
    userName: guest
    password: guest
    connectionTimeout: 5000
    requestedHeartbeat: 30
    networkRecoveryInterval: 5000
    automaticRecoveryEnabled: true

  # Nacos集群配置示例
  nacosConfig:
    serverAddr: nacos1:8848,nacos2:8848,nacos3:8848
    namespace: im-system
    group: DEFAULT_GROUP
    username: nacos
    password: nacos
    connectTimeout: 3000
    readTimeout: 5000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId
