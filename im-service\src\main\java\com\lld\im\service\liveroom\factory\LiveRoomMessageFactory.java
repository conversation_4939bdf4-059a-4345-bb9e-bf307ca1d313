package com.lld.im.service.liveroom.factory;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.enums.command.LiveRoomCommand;
import com.lld.im.service.liveroom.model.data.LiveRoomMessageData;
import com.lld.im.service.liveroom.model.req.JoinLiveRoomReq;
import com.lld.im.service.liveroom.model.req.SendLiveRoomMsgReq;
import com.lld.im.service.liveroom.util.MessageFieldExtractor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 直播间消息工厂类
 * 负责创建各种类型的直播间消息，统一消息格式
 */
@Slf4j
public class LiveRoomMessageFactory {

    /**
     * 创建加入直播间消息
     * 
     * @param req 加入直播间请求
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createJoinMessage(JoinLiveRoomReq req) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createJoinMessage(
                req.getRoomId(),
                req.getUserId(),
                req.getNickname(),
                req.getAvatar(),
                req.getRole(),
                req.getIsGuest()
        );

        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_JOIN.getCommand(),
                req.getAppId(),
                req.getClientType(),
                req.getImei(),
                data
        );
    }

    /**
     * 创建离开直播间消息
     *
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @param reason 离开原因
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createLeaveMessage(String roomId, String userId, String nickname, Integer appId,
                                               Integer clientType, String imei, String reason) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createLeaveMessage(roomId, userId,nickname, reason);
        JSONObject data = buildDataObject(messageData);

        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_LEAVE.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建聊天消息
     * 
     * @param req 发送消息请求
     * @param messageId 消息ID
     * @param sequence 消息序列号
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createChatMessage(SendLiveRoomMsgReq req, String messageId, Long sequence) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createChatMessage(
                req.getRoomId(),
                req.getFromId(),
                req.getContent(),
                req.getMessageType(),
                messageId
        );
        
        // 设置发送者信息
        messageData.setFromNickname(req.getFromNickname())
                  .setFromAvatar(req.getFromAvatar())
                  .setSequence(sequence)
                  .setSendTime(System.currentTimeMillis());

        // 合并扩展字段
        if (req.getExtra() != null) {
            messageData.setExtra(req.getExtra());
        }

        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_MSG.getCommand(),
                req.getAppId(),
                req.getClientType(),
                req.getImei(),
                data
        );
    }

    /**
     * 创建礼物消息
     * 
     * @param roomId 直播间ID
     * @param fromId 发送者ID
     * @param toId 接收者ID
     * @param giftId 礼物ID
     * @param giftName 礼物名称
     * @param giftCount 礼物数量
     * @param giftValue 礼物价值
     * @param messageId 消息ID
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createGiftMessage(String roomId, String fromId, String toId, 
                                              String giftId, String giftName, Integer giftCount, 
                                              Integer giftValue, String messageId, Integer appId, 
                                              Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createGiftMessage(
                roomId, fromId, toId, giftId, giftName, giftCount, giftValue, messageId
        );
        
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_GIFT.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建点赞消息
     * 
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param messageId 消息ID
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createLikeMessage(String roomId, String userId, String messageId, 
                                              Integer appId, Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createLikeMessage(roomId, userId, messageId);
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_LIKE.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建禁言消息
     * 
     * @param roomId 直播间ID
     * @param operatorId 操作者ID
     * @param targetUserId 被禁言用户ID
     * @param reason 禁言原因
     * @param duration 禁言时长（秒）
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createMuteMessage(String roomId, String operatorId, String targetUserId, 
                                              String reason, Integer duration, Integer appId, 
                                              Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createMuteMessage(
                roomId, operatorId, targetUserId, reason, duration
        );
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_MUTE.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建踢人消息
     * 
     * @param roomId 直播间ID
     * @param operatorId 操作者ID
     * @param targetUserId 被踢用户ID
     * @param reason 踢出原因
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createKickMessage(String roomId, String operatorId, String targetUserId, 
                                              String reason, Integer appId, Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createKickMessage(
                roomId, operatorId, targetUserId, reason
        );
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_KICK.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建公告消息
     * 
     * @param roomId 直播间ID
     * @param operatorId 操作者ID
     * @param announcement 公告内容
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createAnnouncementMessage(String roomId, String operatorId, String announcement, 
                                                      Integer appId, Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createAnnouncementMessage(
                roomId, operatorId, announcement
        );
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);
        
        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_ANNOUNCEMENT.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建全员禁言消息
     *
     * @param roomId 直播间ID
     * @param operatorId 操作者ID
     * @param muteAll 是否全员禁言
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createMuteAllMessage(String roomId, String operatorId, Boolean muteAll,
                                                 Integer appId, Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createMuteAllMessage(
                roomId, operatorId, muteAll
        );
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);

        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_MUTE_ALL.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 创建关闭直播间消息
     *
     * @param roomId 直播间ID
     * @param operatorId 操作者ID
     * @param reason 关闭原因
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @return 标准格式的消息JSON对象
     */
    public static JSONObject createCloseMessage(String roomId, String operatorId, String reason,
                                               Integer appId, Integer clientType, String imei) {
        LiveRoomMessageData messageData = LiveRoomMessageData.createCloseMessage(
                roomId, operatorId, reason
        );
        messageData.setSendTime(System.currentTimeMillis());
        JSONObject data = buildDataObject(messageData);

        return MessageFieldExtractor.buildStandardMessage(
                LiveRoomCommand.LIVE_ROOM_CLOSE.getCommand(),
                appId,
                clientType,
                imei,
                data
        );
    }

    /**
     * 将LiveRoomMessageData转换为JSON对象
     * 
     * @param messageData 消息数据
     * @return JSON对象
     */
    private static JSONObject buildDataObject(LiveRoomMessageData messageData) {
        JSONObject data = new JSONObject();
        
        // 基础字段
        putIfNotNull(data, "roomId", messageData.getRoomId());
        putIfNotNull(data, "userId", messageData.getUserId());
        putIfNotNull(data, "action", messageData.getAction());
        putIfNotNull(data, "messageType", messageData.getMessageType());
        putIfNotNull(data, "content", messageData.getContent());
        putIfNotNull(data, "messageId", messageData.getMessageId());
        
        // 发送者信息
        putIfNotNull(data, "fromNickname", messageData.getFromNickname());
        putIfNotNull(data, "fromAvatar", messageData.getFromAvatar());
        putIfNotNull(data, "fromRole", messageData.getFromRole());
        
        // 接收者信息
        putIfNotNull(data, "toId", messageData.getToId());
        putIfNotNull(data, "toNickname", messageData.getToNickname());
        
        // 其他字段
        putIfNotNull(data, "isGuest", messageData.getIsGuest());
        putIfNotNull(data, "sequence", messageData.getSequence());
        putIfNotNull(data, "sendTime", messageData.getSendTime());
        
        // 礼物相关字段
        putIfNotNull(data, "giftId", messageData.getGiftId());
        putIfNotNull(data, "giftName", messageData.getGiftName());
        putIfNotNull(data, "giftCount", messageData.getGiftCount());
        putIfNotNull(data, "giftValue", messageData.getGiftValue());
        
        // 管理相关字段
        putIfNotNull(data, "muteReason", messageData.getMuteReason());
        putIfNotNull(data, "muteDuration", messageData.getMuteDuration());
        putIfNotNull(data, "kickReason", messageData.getKickReason());
        putIfNotNull(data, "announcement", messageData.getAnnouncement());
        
        // 扩展字段
        if (messageData.getExtra() != null && !messageData.getExtra().isEmpty()) {
            data.put("extra", messageData.getExtra());
        }
        
        return data;
    }

    /**
     * 如果值不为空则放入JSON对象
     */
    private static void putIfNotNull(JSONObject json, String key, Object value) {
        if (value != null) {
            if (value instanceof String && StringUtils.isEmpty((String) value)) {
                return;
            }
            json.put(key, value);
        }
    }
}
