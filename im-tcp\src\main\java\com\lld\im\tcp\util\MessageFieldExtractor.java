package com.lld.im.tcp.util;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * TCP消息字段提取工具类
 * 用于从JSON消息中提取和验证基础字段
 */
@Slf4j
public class MessageFieldExtractor {

    /**
     * 消息字段数据模型
     */
    @Data
    public static class MessageFields {
        private Integer command;
        private Integer appId;
        private Integer clientType;
        private String imei;
        private Long timestamp;
        private JSONObject data;

        /**
         * 验证字段有效性并设置默认值
         */
        public void validateAndSetDefaults() {
            // 设置默认值
            if (clientType == null) {
                clientType = 1; // 默认客户端类型
            }
            if (StringUtils.isEmpty(imei)) {
                imei = "unknown"; // 默认设备标识
            }
            if (timestamp == null) {
                timestamp = System.currentTimeMillis(); // 默认当前时间
            }
        }

        /**
         * 检查必要字段是否有效
         */
        public boolean isValid() {
            if (command == null) {
                log.error("消息缺少command字段");
                return false;
            }
            if (appId == null) {
                log.error("消息缺少appId字段");
                return false;
            }
            return true;
        }

        /**
         * 检查data中是否包含roomId
         */
        public boolean hasRoomId() {
            return data != null && StringUtils.isNotEmpty(data.getString("roomId"));
        }

        /**
         * 获取roomId
         */
        public String getRoomId() {
            return data != null ? data.getString("roomId") : null;
        }

        /**
         * 获取userId
         */
        public String getUserId() {
            return data != null ? data.getString("userId") : null;
        }

        /**
         * 获取action
         */
        public String getAction() {
            return data != null ? data.getString("action") : null;
        }
    }

    /**
     * 从JSON对象中提取消息字段
     * 
     * @param jsonObject 原始JSON消息
     * @return 提取的字段对象
     */
    public static MessageFields extract(JSONObject jsonObject) {
        if (jsonObject == null) {
            log.error("输入的JSON对象为空");
            return null;
        }

        MessageFields fields = new MessageFields();
        
        try {
            // 提取基础字段
            fields.setCommand(jsonObject.getInteger("command"));
            fields.setAppId(jsonObject.getInteger("appId"));
            fields.setClientType(jsonObject.getInteger("clientType"));
            fields.setImei(jsonObject.getString("imei"));
            fields.setTimestamp(jsonObject.getLong("timestamp"));
            
            // 提取data对象
            fields.setData(jsonObject.getJSONObject("data"));
            
            log.debug("成功提取消息字段: command={}, appId={}, clientType={}, imei={}", 
                     fields.getCommand(), fields.getAppId(), fields.getClientType(), fields.getImei());
            
        } catch (Exception e) {
            log.error("提取消息字段时发生异常", e);
            return null;
        }
        
        return fields;
    }

    /**
     * 从字符串中解析并提取消息字段
     * 
     * @param jsonString JSON字符串
     * @return 提取的字段对象
     */
    public static MessageFields extractFromString(String jsonString) {
        if (StringUtils.isEmpty(jsonString)) {
            log.error("输入的JSON字符串为空");
            return null;
        }

        try {
            JSONObject jsonObject = JSONObject.parseObject(jsonString);
            return extract(jsonObject);
        } catch (Exception e) {
            log.error("解析JSON字符串时发生异常: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 验证消息格式是否符合新的标准格式
     * 
     * @param jsonObject 消息JSON对象
     * @return 是否符合标准格式
     */
    public static boolean isValidFormat(JSONObject jsonObject) {
        if (jsonObject == null) {
            return false;
        }

        // 检查必要的顶层字段
        if (!jsonObject.containsKey("command") || 
            !jsonObject.containsKey("appId") || 
            !jsonObject.containsKey("data")) {
            return false;
        }

        // 检查data字段是否为对象
        Object dataObj = jsonObject.get("data");
        if (!(dataObj instanceof JSONObject)) {
            return false;
        }

        JSONObject data = (JSONObject) dataObj;
        
        // 检查data中是否包含roomId（直播间消息必须字段）
        if (!data.containsKey("roomId")) {
            return false;
        }

        return true;
    }

    /**
     * 从路由键中提取直播间ID
     * 
     * @param routingKey 路由键
     * @param routingKeyPrefix 路由键前缀
     * @return 直播间ID，如果无法提取则返回null
     */
    public static String extractRoomIdFromRoutingKey(String routingKey, String routingKeyPrefix) {
        if (StringUtils.isEmpty(routingKey) || StringUtils.isEmpty(routingKeyPrefix)) {
            return null;
        }

        if (!routingKey.startsWith(routingKeyPrefix)) {
            return null;
        }

        String suffix = routingKey.substring(routingKeyPrefix.length());
        
        // 如果是纯数字，可能是分片ID，不是直播间ID
        if (suffix.matches("\\d+")) {
            log.debug("路由键后缀是数字，可能是分片ID: {}", suffix);
            return null;
        }

        // 如果包含点号，可能是 roomId.userId 格式，提取roomId部分
        if (suffix.contains(".")) {
            String roomId = suffix.substring(0, suffix.indexOf("."));
            log.debug("从路由键提取直播间ID: {}", roomId);
            return roomId;
        }

        // 否则整个后缀就是直播间ID
        log.debug("从路由键提取直播间ID: {}", suffix);
        return suffix;
    }
}
