-- 系统通知数据库优化脚本
-- 执行时间：2025-07-04
-- 目的：优化系统通知相关表的索引和结构，提升查询性能

-- =====================================================
-- 1. 优化索引结构
-- =====================================================

-- 为im_system_notification表添加复合索引
-- 优化按应用ID和创建时间查询的性能
ALTER TABLE `im_system_notification` 
ADD INDEX `idx_app_create_time` (`app_id`, `create_time` DESC);

-- 优化按应用ID和通知类型查询的性能
ALTER TABLE `im_system_notification` 
ADD INDEX `idx_app_type` (`app_id`, `notification_type`);

-- 为im_user_system_notification表添加优化索引
-- 优化未读通知数量查询
ALTER TABLE `im_user_system_notification` 
ADD INDEX `idx_user_read_status` (`app_id`, `user_id`, `read_status`);

-- 优化按读取时间查询
ALTER TABLE `im_user_system_notification` 
ADD INDEX `idx_user_read_time` (`app_id`, `user_id`, `read_time`);

-- 优化分页查询性能（覆盖索引）
ALTER TABLE `im_user_system_notification` 
ADD INDEX `idx_user_create_time_covering` (`app_id`, `user_id`, `create_time` DESC, `notification_id`, `read_status`);

-- =====================================================
-- 2. 数据清理和归档策略
-- =====================================================

-- 创建系统通知归档表（用于历史数据归档）
CREATE TABLE IF NOT EXISTS `im_system_notification_archive` (
    `notification_id` bigint(20) NOT NULL COMMENT '通知ID',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `notification_type` int(11) NOT NULL COMMENT '通知类型',
    `title` varchar(255) NOT NULL COMMENT '通知标题',
    `content` text NOT NULL COMMENT '通知内容',
    `extra` json DEFAULT NULL COMMENT '扩展字段',
    `create_time` bigint(20) NOT NULL COMMENT '创建时间',
    `archive_time` bigint(20) NOT NULL COMMENT '归档时间',
    `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`notification_id`),
    KEY `idx_app_id` (`app_id`),
    KEY `idx_archive_time` (`archive_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知归档表';

-- 创建用户通知关系归档表
CREATE TABLE IF NOT EXISTS `im_user_system_notification_archive` (
    `id` bigint(20) NOT NULL,
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `notification_id` bigint(20) NOT NULL COMMENT '通知ID',
    `sequence` bigint(20) NOT NULL COMMENT '序列号',
    `read_status` tinyint(4) DEFAULT '0' COMMENT '读取状态',
    `read_time` bigint(20) DEFAULT NULL COMMENT '读取时间',
    `create_time` bigint(20) NOT NULL COMMENT '创建时间',
    `archive_time` bigint(20) NOT NULL COMMENT '归档时间',
    PRIMARY KEY (`id`),
    KEY `idx_app_user` (`app_id`, `user_id`),
    KEY `idx_archive_time` (`archive_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户系统通知关系归档表';

-- =====================================================
-- 3. 数据库配置优化建议
-- =====================================================

-- 以下配置建议在my.cnf中设置，需要重启MySQL生效：

-- # 优化批量插入性能
-- innodb_buffer_pool_size = 2G          # 根据服务器内存调整
-- innodb_log_file_size = 256M           # 增大日志文件大小
-- innodb_log_buffer_size = 64M          # 增大日志缓冲区
-- innodb_flush_log_at_trx_commit = 2    # 提高写入性能（可能丢失1秒数据）
-- 
-- # 优化批量操作
-- bulk_insert_buffer_size = 64M         # 批量插入缓冲区
-- max_allowed_packet = 64M              # 允许更大的数据包
-- 
-- # 优化查询性能
-- query_cache_size = 256M               # 查询缓存大小
-- query_cache_type = 1                  # 启用查询缓存
-- 
-- # 优化连接和线程
-- max_connections = 1000                # 最大连接数
-- thread_cache_size = 50                # 线程缓存大小

-- =====================================================
-- 4. 分区表优化（可选，适用于大数据量场景）
-- =====================================================

-- 如果系统通知数据量很大，可以考虑按时间分区
-- 注意：分区需要在表创建时指定，现有表需要重建

-- 示例：按月分区的系统通知表（仅供参考）
/*
CREATE TABLE `im_system_notification_partitioned` (
    `notification_id` bigint(20) NOT NULL COMMENT '通知ID',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `notification_type` int(11) NOT NULL COMMENT '通知类型',
    `title` varchar(255) NOT NULL COMMENT '通知标题',
    `content` text NOT NULL COMMENT '通知内容',
    `extra` json DEFAULT NULL COMMENT '扩展字段',
    `create_time` bigint(20) NOT NULL COMMENT '创建时间',
    `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`notification_id`, `create_time`),
    KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
PARTITION BY RANGE (create_time) (
    PARTITION p202501 VALUES LESS THAN (UNIX_TIMESTAMP('2025-02-01') * 1000),
    PARTITION p202502 VALUES LESS THAN (UNIX_TIMESTAMP('2025-03-01') * 1000),
    PARTITION p202503 VALUES LESS THAN (UNIX_TIMESTAMP('2025-04-01') * 1000),
    PARTITION p202504 VALUES LESS THAN (UNIX_TIMESTAMP('2025-05-01') * 1000),
    PARTITION p202505 VALUES LESS THAN (UNIX_TIMESTAMP('2025-06-01') * 1000),
    PARTITION p202506 VALUES LESS THAN (UNIX_TIMESTAMP('2025-07-01') * 1000),
    PARTITION p202507 VALUES LESS THAN (UNIX_TIMESTAMP('2025-08-01') * 1000),
    PARTITION p202508 VALUES LESS THAN (UNIX_TIMESTAMP('2025-09-01') * 1000),
    PARTITION p202509 VALUES LESS THAN (UNIX_TIMESTAMP('2025-10-01') * 1000),
    PARTITION p202510 VALUES LESS THAN (UNIX_TIMESTAMP('2025-11-01') * 1000),
    PARTITION p202511 VALUES LESS THAN (UNIX_TIMESTAMP('2025-12-01') * 1000),
    PARTITION p202512 VALUES LESS THAN (UNIX_TIMESTAMP('2026-01-01') * 1000),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- =====================================================
-- 5. 执行完成提示
-- =====================================================

-- 优化完成后，建议执行以下操作：
-- 1. 重建表统计信息：ANALYZE TABLE im_system_notification, im_user_system_notification;
-- 2. 监控慢查询日志，识别需要进一步优化的查询
-- 3. 定期清理过期数据，保持表大小合理
-- 4. 考虑使用读写分离，将查询操作分散到从库

SELECT 'System notification database optimization completed!' as status;
