package com.lld.im.service.group.model.resp;

import com.lld.im.service.group.dao.ImGroupEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 群组同步响应模型
 * @description: 包含群组信息和用户在群组中的状态
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "群组同步响应模型")
@Data
public class GroupSyncResp {

    @ApiModelProperty(value = "群组信息", notes = "群组的基本信息")
    private ImGroupEntity groupInfo;

    @ApiModelProperty(value = "用户在群组中的角色", example = "1", notes = "0-普通成员 1-管理员 2-群主 3-已离开")
    private Integer memberRole;

    @ApiModelProperty(value = "加入时间", example = "1640995200000", notes = "用户加入群组的时间戳")
    private Long joinTime;

    @ApiModelProperty(value = "离开时间", example = "1640995200000", notes = "用户离开群组的时间戳，未离开时为null")
    private Long leaveTime;

    @ApiModelProperty(value = "群组序列号", example = "1500", notes = "群组信息的序列号，用于增量同步")
    private Long groupSequence;

    /**
     * 判断用户是否已离开群组
     * @return true-已离开 false-仍在群组中
     */
    public boolean isLeft() {
        return memberRole != null && memberRole == 3; // GroupMemberRoleEnum.LEAVE.getCode()
    }

    /**
     * 判断群组是否已解散
     * @return true-已解散 false-正常状态
     */
    public boolean isDestroyed() {
        return groupInfo != null && groupInfo.getStatus() != null && groupInfo.getStatus() == 2; // GroupStatusEnum.DESTROY.getCode()
    }
}
