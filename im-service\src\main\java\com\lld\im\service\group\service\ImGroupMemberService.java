package com.lld.im.service.group.service;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.model.resp.BatchSpeakMemberResp;
import com.lld.im.service.group.model.resp.BatchRemoveGroupMemberResp;
import com.lld.im.service.group.model.resp.GetRoleInGroupResp;
import com.lld.im.service.group.model.resp.GroupApplyListResp;
import com.lld.im.service.group.model.resp.UserApplyListResp;

import java.util.Collection;
import java.util.List;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public interface ImGroupMemberService {

    public ResponseVO importGroupMember(ImportGroupMemberReq req);

    public ResponseVO addMember(AddGroupMemberReq req);

    public ResponseVO removeMember(RemoveGroupMemberReq req);

    public ResponseVO<BatchRemoveGroupMemberResp> batchRemoveMember(BatchRemoveGroupMemberReq req);

    public ResponseVO addGroupMember(String groupId, Integer appId, GroupMemberDto dto);

    public ResponseVO removeGroupMember(String groupId, Integer appId, String memberId);

    public ResponseVO<GetRoleInGroupResp> getRoleInGroupOne(String groupId, String memberId, Integer appId);

    public ResponseVO<Collection<String>> getMemberJoinedGroup(GetJoinedGroupReq req);

    public ResponseVO<List<GroupMemberDto>> getGroupMember(String groupId, Integer appId);

    public List<String> getGroupMemberId(String groupId, Integer appId);

    public List<GroupMemberDto> getGroupManager(String groupId, Integer appId);

    public ResponseVO updateGroupMember(UpdateGroupMemberReq req);

    public ResponseVO transferGroupMember(String owner, String groupId, Integer appId);

    public ResponseVO speak(SpeaMemberReq req);

    public ResponseVO<BatchSpeakMemberResp> batchSpeak(BatchSpeakMemberReq req);

    ResponseVO<Collection<String>> syncMemberJoinedGroup(String operater, Integer appId);

    // 群申请相关方法
    public ResponseVO applyJoinGroup(ApplyJoinGroupReq req);

    public ResponseVO approveGroupApply(ApproveGroupApplyReq req);

    public ResponseVO freeJoinGroup(FreeJoinGroupReq req);

    public ResponseVO<GroupApplyListResp> getGroupApplyList(GetGroupApplyListReq req);

    /**
     * 查询用户的群组申请记录
     * @param req 查询用户申请记录请求
     * @return 用户申请记录列表
     */
    public ResponseVO<UserApplyListResp> getUserApplyList(GetUserApplyListReq req);

    /**
     * 退出群组
     * @param req 退出群组请求
     * @return 操作结果
     */
    public ResponseVO<Object> exitGroup(ExitGroupReq req);
}
