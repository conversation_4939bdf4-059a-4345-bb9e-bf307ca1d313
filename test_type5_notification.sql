-- 测试类型5（直播提醒）通知数据库持久化验证脚本
-- 执行前先清空相关表数据，便于观察测试结果

-- 1. 清空测试数据（可选）
-- TRUNCATE TABLE im_system_notification;
-- TRUNCATE TABLE im_user_system_notification;

-- 2. 查看修改前的数据状态
SELECT '=== 修改前数据状态 ===' as status;
SELECT 
    notification_type,
    COUNT(*) as count,
    MIN(create_time) as earliest_time,
    MAX(create_time) as latest_time
FROM im_system_notification 
WHERE app_id = 10000
GROUP BY notification_type
ORDER BY notification_type;

-- 3. 查看类型5通知的详细信息
SELECT '=== 类型5通知详细信息 ===' as status;
SELECT 
    notification_id,
    notification_type,
    title,
    content,
    FROM_UNIXTIME(create_time/1000) as create_time_formatted,
    del_flag
FROM im_system_notification 
WHERE app_id = 10000 AND notification_type = 5
ORDER BY create_time DESC
LIMIT 10;

-- 4. 查看用户通知关系表中的类型5通知
SELECT '=== 用户通知关系表中的类型5通知 ===' as status;
SELECT 
    un.user_id,
    un.notification_id,
    sn.notification_type,
    sn.title,
    un.read_status,
    FROM_UNIXTIME(un.create_time/1000) as create_time_formatted
FROM im_user_system_notification un
INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
WHERE un.app_id = 10000 AND sn.notification_type = 5
ORDER BY un.create_time DESC
LIMIT 10;

-- 5. 统计各通知类型的数量分布
SELECT '=== 各通知类型数量统计 ===' as status;
SELECT 
    sn.notification_type,
    CASE 
        WHEN sn.notification_type = 1 THEN '系统消息'
        WHEN sn.notification_type = 2 THEN '竞猜通知'
        WHEN sn.notification_type = 3 THEN '我的关注'
        WHEN sn.notification_type = 4 THEN '点赞评论'
        WHEN sn.notification_type = 5 THEN '直播提醒'
        WHEN sn.notification_type = 6 THEN '活动消息'
        WHEN sn.notification_type = 7 THEN '赛事通知'
        WHEN sn.notification_type = 8 THEN '锦囊通知'
        ELSE '未知类型'
    END as type_name,
    COUNT(DISTINCT sn.notification_id) as notification_count,
    COUNT(un.id) as user_notification_count
FROM im_system_notification sn
LEFT JOIN im_user_system_notification un ON sn.notification_id = un.notification_id
WHERE sn.app_id = 10000
GROUP BY sn.notification_type
ORDER BY sn.notification_type;

-- 6. 验证查询：检查最近1小时内的类型5通知
SELECT '=== 最近1小时内的类型5通知 ===' as status;
SELECT 
    notification_id,
    title,
    content,
    FROM_UNIXTIME(create_time/1000) as create_time_formatted,
    (UNIX_TIMESTAMP() * 1000 - create_time) / 1000 / 60 as minutes_ago
FROM im_system_notification 
WHERE app_id = 10000 
    AND notification_type = 5
    AND create_time > (UNIX_TIMESTAMP() - 3600) * 1000  -- 最近1小时
ORDER BY create_time DESC;

-- 7. 检查索引使用情况（可选）
-- EXPLAIN SELECT * FROM im_system_notification WHERE app_id = 10000 AND notification_type = 5;
