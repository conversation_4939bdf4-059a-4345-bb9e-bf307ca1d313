package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import com.lld.im.service.user.dao.ImUserDataEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@ApiModel(description = "导入用户请求模型")
@Data
public class ImportUserReq extends RequestBase {

    @ApiModelProperty(value = "用户数据列表", required = true, notes = "批量导入的用户信息列表")
    private List<ImUserDataEntity> userData;

}
