# IM System Quick Build Script
param(
    [string[]]$Modules = @(),
    [switch]$Clean,
    [switch]$Package
)

Write-Host "IM System Quick Build Script" -ForegroundColor Cyan

# Check Maven
try {
    $mvnOutput = & mvn -version 2>&1
    if ($LASTEXITCODE -ne 0 -or -not $mvnOutput) {
        Write-Host "Maven not installed or configured incorrectly" -ForegroundColor Red
        exit 1
    }
    Write-Host "Maven check passed" -ForegroundColor Green
}
catch {
    Write-Host "Maven not installed or configured incorrectly" -ForegroundColor Red
    exit 1
}

# Check project structure
if (-not (Test-Path "pom.xml")) {
    Write-Host "pom.xml not found, please run from project root directory" -ForegroundColor Red
    exit 1
}

# Build Maven arguments
$mvnArgs = @()

if ($Clean) {
    $mvnArgs += "clean"
}

if ($Modules.Count -gt 0) {
    $mvnArgs += "package"
    $mvnArgs += "-pl"
    $mvnArgs += ($Modules -join ",")
    $mvnArgs += "-am"
    Write-Host "Building modules: $($Modules -join ', ')" -ForegroundColor Yellow
} else {
    $mvnArgs += "package"
    Write-Host "Building all modules" -ForegroundColor Yellow
}

$mvnArgs += "-DskipTests"
$mvnArgs += "-Dmaven.compile.fork=true"

Write-Host "Executing: mvn $($mvnArgs -join ' ')" -ForegroundColor White

# Execute build
$startTime = Get-Date
& mvn $mvnArgs

if ($LASTEXITCODE -eq 0) {
    $duration = (Get-Date) - $startTime
    Write-Host "Build successful! Duration: $($duration.ToString('mm\:ss'))" -ForegroundColor Green
    
    # Show results
    $modules = @("im-tcp", "im-service", "im-message-store")
    foreach ($module in $modules) {
        $jarPath = "$module/target"
        if (Test-Path $jarPath) {
            $jars = Get-ChildItem "$jarPath/*.jar" -ErrorAction SilentlyContinue
            foreach ($jar in $jars) {
                $size = [math]::Round($jar.Length / 1MB, 1)
                Write-Host "Built: $($jar.Name) ($size MB)" -ForegroundColor Green
            }
        }
    }
    
    # Auto package
    if ($Package -and (Test-Path "deploy_package.ps1")) {
        Write-Host "Starting packaging..." -ForegroundColor Yellow
        if ($Modules.Count -gt 0) {
            & .\deploy_package.ps1 $Modules
        } else {
            & .\deploy_package.ps1
        }
    }
} else {
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}
