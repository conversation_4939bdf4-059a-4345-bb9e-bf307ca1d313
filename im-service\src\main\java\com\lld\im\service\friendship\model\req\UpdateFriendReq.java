package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新好友信息请求
 */
@ApiModel(description = "更新好友信息请求模型")
@Data
public class UpdateFriendReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起更新操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "好友信息", required = true, notes = "要更新的好友详细信息")
    @NotNull(message = "toItem不能为空")
    private FriendDto toItem;
}
