package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 解散群组请求
 */
@ApiModel(description = "解散群组请求模型")
@Data
public class DestroyGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要解散的群组唯一标识")
    @NotNull(message = "群id不能为空")
    private String groupId;

}
