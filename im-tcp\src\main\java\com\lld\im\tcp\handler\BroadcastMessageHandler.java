package com.lld.im.tcp.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessageHeader;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.tcp.utils.SessionSocketHolder;
import com.lld.im.tcp.utils.AttributeKeys;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.AttributeKey;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;

/**
 * 广播消息处理器，负责将系统消息发送给所有用户，包括游客
 */
public class BroadcastMessageHandler {

    private static final Logger logger = LoggerFactory.getLogger(BroadcastMessageHandler.class);
    // 使用统一的AttributeKeys
    private static final AttributeKey<Boolean> IS_GUEST = AttributeKeys.IS_GUEST;
    
    /**
     * 处理广播消息
     * @param message 消息内容
     */
    public static void handle(Message message) {
        try {
            @SuppressWarnings("unchecked")
            MessagePack<Object> originalMessagePack = (MessagePack<Object>) message.getMessagePack();
            String data = JSONObject.toJSONString(originalMessagePack);
            //logger.info("处理广播消息: {}", data);

            // 解析消息以检查是否需要发送给游客
            JSONObject jsonObject = JSON.parseObject(data);
            // 检查是否只发送给特定类型的用户
            Boolean includeGuests = jsonObject.getBoolean("includeGuests");
            Boolean onlyForGuests = jsonObject.getBoolean("onlyForGuests");

            // 检查是否为精准推送（包含receiverIds）和排除用户（包含excludeUserIds）
            Object dataObj = jsonObject.get("data");
            List<String> receiverIds = null;
            List<String> excludeUserIds = null;
            if (dataObj instanceof JSONObject) {
                JSONObject dataJson = (JSONObject) dataObj;
                Object receiverIdsObj = dataJson.get("receiverIds");
                if (receiverIdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> tempList = (List<String>) receiverIdsObj;
                    receiverIds = tempList;
                }

                Object excludeUserIdsObj = dataJson.get("excludeUserIds");
                if (excludeUserIdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> tempExcludeList = (List<String>) excludeUserIdsObj;
                    excludeUserIds = tempExcludeList;
                }
            }

            // 构建标准的WebSocket消息报文
            Message standardMessage = buildStandardMessage(originalMessagePack, dataObj);

            // 如果有指定接收者ID列表，进行精准推送
            if (receiverIds != null && !receiverIds.isEmpty()) {
                //logger.info("执行精准推送，接收者数量: {}", receiverIds.size());
                if (onlyForGuests != null && onlyForGuests) {
                    sendToSpecificGuests(standardMessage, receiverIds, excludeUserIds);
                } else {
                    sendToSpecificNormalUsers(standardMessage, receiverIds, excludeUserIds);
                }
            }
            // 否则进行广播推送
            else {
                //logger.info("执行广播推送");
                if (excludeUserIds != null && !excludeUserIds.isEmpty()) {
                    //logger.info("广播推送包含排除用户列表，排除用户数量: {}", excludeUserIds.size());
                }
                // 如果指定了只发送给游客
                if (onlyForGuests != null && onlyForGuests) {
                    sendToAllGuests(standardMessage, excludeUserIds);
                }
                // 如果指定了包含游客，则发送给所有人
                else if (includeGuests != null && includeGuests) {
                    sendToAll(standardMessage, excludeUserIds);
                }
                // 否则只发送给普通用户
                else {
                    sendToNormalUsers(standardMessage, excludeUserIds);
                }
            }
        } catch (Exception e) {
            logger.error("处理广播消息失败", e);
        }
    }

    /**
     * 构建标准的WebSocket消息报文
     * @param originalMessagePack 原始MessagePack
     * @param systemNotificationData 系统通知数据
     * @return 标准格式的Message
     */
    private static Message buildStandardMessage(MessagePack<Object> originalMessagePack, Object systemNotificationData) {
        Message message = new Message();

        // 构建MessageHeader
        MessageHeader messageHeader = new MessageHeader();
        messageHeader.setCommand(originalMessagePack.getCommand());
        messageHeader.setAppId(originalMessagePack.getAppId() != null ? originalMessagePack.getAppId() : 10000);
        messageHeader.setClientType(originalMessagePack.getClientType() != 0 ? originalMessagePack.getClientType() : 1);
        messageHeader.setImei(originalMessagePack.getImei() != null ? originalMessagePack.getImei() : "system");
        messageHeader.setVersion(1); // 默认版本号
        messageHeader.setMessageType(0x0); // JSON格式

        // 构建MessagePack
        MessagePack<JSONObject> messagePack = new MessagePack<>();
        messagePack.setCommand(originalMessagePack.getCommand());
        messagePack.setAppId(messageHeader.getAppId());
        messagePack.setClientType(messageHeader.getClientType());
        messagePack.setImei(messageHeader.getImei());

        // 构建完整的JSON对象，包含顶层字段和data
        JSONObject fullMessage = new JSONObject();
        fullMessage.put("command", originalMessagePack.getCommand());
        fullMessage.put("appId", messageHeader.getAppId());
        fullMessage.put("clientType", messageHeader.getClientType());
        fullMessage.put("imei", messageHeader.getImei());
        fullMessage.put("timestamp", System.currentTimeMillis());
        fullMessage.put("data", systemNotificationData != null ? systemNotificationData : new JSONObject());

        messagePack.setData(fullMessage);

        message.setMessageHeader(messageHeader);
        message.setMessagePack(messagePack);

        logger.debug("构建标准WebSocket消息报文: command={}, appId={}, clientType={}, imei={}",
                    originalMessagePack.getCommand(), messageHeader.getAppId(),
                    messageHeader.getClientType(), messageHeader.getImei());

        return message;
    }


    /**
     * 发送消息给所有用户，包括普通用户和游客（支持排除用户）
     */
    private static void sendToAll(Message message, List<String> excludeUserIds) {
        //logger.info("发送广播消息给所有用户");
        // 发送给普通用户
        sendToNormalUsers(message, excludeUserIds);
        // 发送给游客
        sendToAllGuests(message, excludeUserIds);
    }
    

    /**
     * 发送消息仅给普通用户（不包括游客，支持排除用户）
     */
    private static void sendToNormalUsers(Message message, List<String> excludeUserIds) {
        //logger.info("发送广播消息仅给普通用户");
        @SuppressWarnings("unchecked")
        MessagePack<JSONObject> messagePack = (MessagePack<JSONObject>) message.getMessagePack();

        for (NioSocketChannel channel : SessionSocketHolder.getAllChannels()) {
            try {
                // 使用统一的游客判断逻辑
                boolean isGuest = isGuestUser(channel);

                // 只发送给非游客
                if (!isGuest) {
                    // 检查是否需要排除该用户
                    String userId = getUserIdFromChannel(channel);
                    if (excludeUserIds != null && !excludeUserIds.isEmpty() &&
                        userId != null && excludeUserIds.contains(userId)) {
                        logger.debug("排除用户: {}", userId);
                        continue;
                    }

                    // 统一发送方式，不区分连接类型
                    channel.writeAndFlush(messagePack);
                }
            } catch (Exception e) {
                logger.error("发送消息到普通用户失败", e);
            }
        }
    }
    

    /**
     * 发送消息给所有游客（支持排除用户）
     */
    private static void sendToAllGuests(Message message, List<String> excludeUserIds) {
        //logger.info("发送广播消息给所有游客");
        @SuppressWarnings("unchecked")
        MessagePack<JSONObject> messagePack = (MessagePack<JSONObject>) message.getMessagePack();

        Collection<NioSocketChannel> guestChannels = SessionSocketHolder.getAllGuestChannels();
        //logger.info("🎭 游客数量: {}", guestChannels.size());
        for (NioSocketChannel channel : guestChannels) {
            try {
                // 检查是否需要排除该用户
                String userId = getUserIdFromChannel(channel);
                if (excludeUserIds != null && !excludeUserIds.isEmpty() &&
                    userId != null && excludeUserIds.contains(userId)) {
                    logger.debug("🚫 排除游客用户: {}", userId);
                    continue;
                }

                // 二次验证游客身份（确保数据一致性）
                Integer appId = getAppIdFromChannel(channel);
                if (appId != null && userId != null && !SessionSocketHolder.isUserGuest(appId, userId)) {
                    logger.warn("⚠️ 发现非游客Channel在游客列表中，跳过: userId={}, appId={}", userId, appId);
                    continue;
                }

                // 统一发送方式，不区分连接类型
                //logger.info("向游客发送消息");
                channel.writeAndFlush(messagePack);
            } catch (Exception e) {
                logger.error("发送消息到游客失败", e);
            }
        }
    }


    /**
     * 发送消息给指定的普通用户（精准推送，支持排除用户）
     */
    private static void sendToSpecificNormalUsers(Message message, List<String> receiverIds, List<String> excludeUserIds) {
        //logger.info("精准推送消息给指定普通用户，用户数量: {}", receiverIds.size());
        @SuppressWarnings("unchecked")
        MessagePack<JSONObject> messagePack = (MessagePack<JSONObject>) message.getMessagePack();

        int successCount = 0;
        int failureCount = 0;

        for (NioSocketChannel channel : SessionSocketHolder.getAllChannels()) {
            try {
                // 使用统一的游客判断逻辑
                boolean isGuest = isGuestUser(channel);

                // 只处理非游客用户
                if (!isGuest) {
                    // 获取用户ID
                    String userId = getUserIdFromChannel(channel);

                    // 检查是否在接收者列表中
                    if (userId != null && receiverIds.contains(userId)) {
                        // 检查是否需要排除该用户
                        if (excludeUserIds != null && !excludeUserIds.isEmpty() && excludeUserIds.contains(userId)) {
                            logger.debug("排除指定普通用户: {}", userId);
                            continue;
                        }
                        // 统一发送方式，不区分连接类型
                        channel.writeAndFlush(messagePack);
                        successCount++;
                        logger.debug("成功发送消息给普通用户: {}", userId);
                    }
                }
            } catch (Exception e) {
                failureCount++;
                logger.error("发送消息到指定普通用户失败", e);
            }
        }

        //logger.info("精准推送完成，成功: {}, 失败: {}", successCount, failureCount);
    }



    /**
     * 发送消息给指定的游客用户（精准推送，支持排除用户）
     */
    private static void sendToSpecificGuests(Message message, List<String> receiverIds, List<String> excludeUserIds) {
        //logger.info("精准推送消息给指定游客，用户数量: {}", receiverIds.size());
        @SuppressWarnings("unchecked")
        MessagePack<JSONObject> messagePack = (MessagePack<JSONObject>) message.getMessagePack();

        int successCount = 0;
        int failureCount = 0;

        Collection<NioSocketChannel> guestChannels = SessionSocketHolder.getAllGuestChannels();
        for (NioSocketChannel channel : guestChannels) {
            try {
                // 获取用户ID
                String userId = getUserIdFromChannel(channel);

                // 检查是否在接收者列表中
                if (userId != null && receiverIds.contains(userId)) {
                    // 检查是否需要排除该用户
                    if (excludeUserIds != null && !excludeUserIds.isEmpty() && excludeUserIds.contains(userId)) {
                        logger.debug("排除指定游客用户: {}", userId);
                        continue;
                    }

                    // 统一发送方式，不区分连接类型
                    channel.writeAndFlush(messagePack);
                    successCount++;
                    logger.debug("成功发送消息给游客: {}", userId);
                }
            } catch (Exception e) {
                failureCount++;
                logger.error("发送消息到指定游客失败", e);
            }
        }

        //logger.info("精准推送给游客完成，成功: {}, 失败: {}", successCount, failureCount);
    }

    /**
     * 从Channel中获取用户ID
     */
    private static String getUserIdFromChannel(NioSocketChannel channel) {
        try {
            return channel.attr(AttributeKeys.USER_ID).get();
        } catch (Exception e) {
            logger.debug("获取Channel用户ID失败", e);
        }
        return null;
    }

    /**
     * 从Channel中获取应用ID
     */
    private static Integer getAppIdFromChannel(NioSocketChannel channel) {
        try {
            return channel.attr(AttributeKeys.APP_ID).get();
        } catch (Exception e) {
            logger.debug("获取Channel应用ID失败", e);
        }
        return null;
    }

    /**
     * 统一的游客身份判断方法
     * 优先使用Channel属性，备选使用统一判断服务
     *
     * @param channel 连接通道
     * @return 是否为游客
     */
    private static boolean isGuestUser(NioSocketChannel channel) {
        try {
            // 1. 优先使用Channel属性（最高效）
            Boolean isGuest = channel.attr(IS_GUEST).get();
            if (isGuest != null) {
                return isGuest;
            }

            // 2. 备选：使用统一的游客判断服务
            String userId = getUserIdFromChannel(channel);
            Integer appId = getAppIdFromChannel(channel);

            if (userId != null && appId != null) {
                boolean guestStatus = SessionSocketHolder.isUserGuest(appId, userId);
                // 将判断结果缓存到Channel属性中，避免重复查询
                channel.attr(IS_GUEST).set(guestStatus);
                return guestStatus;
            }

            return false;
        } catch (Exception e) {
            logger.warn("⚠️ 游客身份判断异常", e);
            return false;
        }
    }
}