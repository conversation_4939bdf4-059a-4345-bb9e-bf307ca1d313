package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 群组禁言请求
 */
@ApiModel(description = "群组禁言请求模型")
@Data
public class MuteGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要设置禁言的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "禁言状态", required = true, example = "1", notes = "0-取消全员禁言 1-开启全员禁言")
    @NotNull(message = "{validation.mute.status.not.null}")
    private Integer mute;

}
