package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 标记好友请求已读请求
 */
@ApiModel(description = "标记好友请求已读请求模型")
@Data
public class ReadFriendShipRequestReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要标记已读的用户ID")
    @NotBlank(message = "用户id不能为空")
    private String fromId;
}
