package com.lld.im.service.test;

import com.lld.im.common.BaseErrorCode;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.enums.UserErrorCode;
import com.lld.im.common.exception.ApplicationException;
import com.lld.im.service.utils.MessageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

/**
 * 国际化测试控制器
 * 
 * @description: 用于测试国际化功能的控制器，仅在开发和测试环境使用
 * @author: IM System
 * @version: 1.0
 */
@Api(tags = "国际化测试", description = "国际化功能测试接口")
@RestController
@RequestMapping("v1/test/i18n")
public class I18nTestController {

    @Autowired
    private MessageUtils messageUtils;

    /**
     * 测试基础国际化消息
     */
    @ApiOperation(value = "测试基础国际化消息", notes = "测试基础的国际化消息获取功能")
    @GetMapping("/basic")
    public ResponseVO<Map<String, Object>> testBasicI18n() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取当前语言环境
        Locale currentLocale = LocaleContextHolder.getLocale();
        result.put("currentLocale", currentLocale.toString());
        
        // 测试通用消息
        result.put("success", messageUtils.getMessage("common.success"));
        result.put("failed", messageUtils.getMessage("common.failed"));
        result.put("systemError", messageUtils.getMessage("common.system.error"));
        
        // 测试业务消息
        result.put("loginSuccess", messageUtils.getMessage("business.user.login.success"));
        result.put("messageSuccess", messageUtils.getMessage("business.message.send.success"));
        
        return ResponseVO.successResponse(result);
    }

    /**
     * 测试错误码国际化
     */
    @ApiOperation(value = "测试错误码国际化", notes = "测试错误码枚举的国际化功能")
    @GetMapping("/error-codes")
    public ResponseVO<Map<String, Object>> testErrorCodes() {
        Map<String, Object> result = new HashMap<>();
        
        // 获取当前语言环境
        Locale currentLocale = LocaleContextHolder.getLocale();
        result.put("currentLocale", currentLocale.toString());
        
        // 测试基础错误码
        result.put("systemError", BaseErrorCode.SYSTEM_ERROR.getError());
        result.put("parameterError", BaseErrorCode.PARAMETER_ERROR.getError());
        
        // 测试用户错误码
        result.put("userNotExist", UserErrorCode.USER_IS_NOT_EXIST.getError());
        result.put("userModifyError", UserErrorCode.MODIFY_USER_ERROR.getError());
        
        return ResponseVO.successResponse(result);
    }

    /**
     * 测试参数验证国际化
     */
    @ApiOperation(value = "测试参数验证国际化", notes = "测试参数验证注解的国际化功能")
    @PostMapping("/validation")
    public ResponseVO<String> testValidation(@Valid @RequestBody TestValidationReq req) {
        return ResponseVO.successResponse("验证通过");
    }

    /**
     * 测试异常国际化
     */
    @ApiOperation(value = "测试异常国际化", notes = "测试异常处理的国际化功能")
    @GetMapping("/exception")
    public ResponseVO<String> testException(@RequestParam String type) {
        switch (type) {
            case "user":
                throw new ApplicationException(UserErrorCode.USER_IS_NOT_EXIST);
            case "system":
                throw new ApplicationException(BaseErrorCode.SYSTEM_ERROR);
            case "runtime":
                throw new RuntimeException("测试运行时异常");
            default:
                return ResponseVO.successResponse("无异常");
        }
    }

    /**
     * 获取当前语言环境信息
     */
    @ApiOperation(value = "获取语言环境信息", notes = "获取当前请求的语言环境信息")
    @GetMapping("/locale-info")
    public ResponseVO<Map<String, Object>> getLocaleInfo() {
        Map<String, Object> result = new HashMap<>();
        
        Locale currentLocale = LocaleContextHolder.getLocale();
        result.put("locale", currentLocale.toString());
        result.put("language", currentLocale.getLanguage());
        result.put("country", currentLocale.getCountry());
        result.put("displayName", currentLocale.getDisplayName());
        
        return ResponseVO.successResponse(result);
    }

    /**
     * 测试验证请求模型
     */
    public static class TestValidationReq {
        @NotBlank(message = "{validation.user.id.not.blank}")
        private String userId;
        
        @NotBlank(message = "{validation.keyword.not.blank}")
        private String keyword;

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getKeyword() {
            return keyword;
        }

        public void setKeyword(String keyword) {
            this.keyword = keyword;
        }
    }
}
