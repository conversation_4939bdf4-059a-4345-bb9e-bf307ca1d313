package com.lld.im.service.group.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 批量群成员禁言响应
 */
@ApiModel(description = "批量群成员禁言响应模型")
@Data
public class BatchSpeakMemberResp {

    @ApiModelProperty(value = "成功禁言的用户ID列表", notes = "成功设置禁言状态的用户ID列表")
    private List<String> successMemberIds;

    @ApiModelProperty(value = "失败的用户信息列表", notes = "禁言失败的用户信息列表")
    private List<FailedMemberInfo> failedMembers;

    @ApiModelProperty(value = "总处理数量", notes = "总共处理的用户数量")
    private Integer totalCount;

    @ApiModelProperty(value = "成功数量", notes = "成功禁言的用户数量")
    private Integer successCount;

    @ApiModelProperty(value = "失败数量", notes = "禁言失败的用户数量")
    private Integer failedCount;

    /**
     * 失败的成员信息
     */
    @ApiModel(description = "禁言失败的成员信息")
    @Data
    public static class FailedMemberInfo {
        @ApiModelProperty(value = "用户ID", notes = "禁言失败的用户ID")
        private String memberId;

        @ApiModelProperty(value = "失败原因", notes = "禁言失败的具体原因")
        private String reason;

        public FailedMemberInfo() {}

        public FailedMemberInfo(String memberId, String reason) {
            this.memberId = memberId;
            this.reason = reason;
        }
    }
}
