package com.lld.im.common.model.message;

import com.lld.im.common.model.ClientInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息已读内容模型
 * @description: 消息已读状态数据模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "消息已读内容模型")
@Data
public class MessageReadedContent extends ClientInfo {

    @ApiModelProperty(value = "消息序列号", required = true, example = "1001", notes = "已读消息的序列号")
    private long messageSequence;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "群组ID", example = "group123", notes = "群组消息的群组ID，点对点消息时为空")
    private String groupId;

    @ApiModelProperty(value = "接收者用户ID", example = "user456", notes = "点对点消息的接收者用户ID，群组消息时为空")
    private String toId;

    @ApiModelProperty(value = "会话类型 (0 单聊 1群聊 2机器人 3公众号)", required = true, example = "1", notes = "消息所属的会话类型")
    private Integer conversationType;

}
