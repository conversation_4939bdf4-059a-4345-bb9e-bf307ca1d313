package com.lld.im.service.system.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通知类型最新记录响应模型
 * @description: 按通知类型分组的最新通知记录响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "通知类型最新记录响应模型")
@Data
public class NotificationTypeLatestResp {

    @ApiModelProperty(value = "通知类型", example = "1", notes = "通知类型(1:系统消息 2:竞猜通知 3:我的关注 4:点赞评论 5:直播提醒 6:活动消息)")
    private Integer notificationType;

    @ApiModelProperty(value = "通知类型名称", example = "系统消息", notes = "通知类型的中文名称")
    private String notificationTypeName;

    @ApiModelProperty(value = "最新通知详情", notes = "该类型下最新的一条通知记录")
    private NotificationDetailResp latestNotification;

    @ApiModelProperty(value = "该类型未读数量", example = "5", notes = "该通知类型下的未读通知数量")
    private Integer unreadCount;
}
