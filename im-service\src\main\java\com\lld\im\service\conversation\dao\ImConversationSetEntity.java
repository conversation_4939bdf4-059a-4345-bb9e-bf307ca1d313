package com.lld.im.service.conversation.dao;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 **/
@Data
@TableName("im_conversation_set")
public class ImConversationSetEntity {

    //会话id 0_fromId_toId
    private String conversationId;

    //会话类型
    private Integer conversationType;

    private String fromId;

    private String toId;

    private int isMute;

    private int isTop;

    // 会话序列号 - 用于增量同步
    private Long sequence;

    // 最新消息序列号 - 用于未读数量计算
    private Long lastMessageSequence;

    private Long readedSequence;

    private Integer appId;
}
