package com.lld.im.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * Redis 连接监控器
 * 在应用启动完成后检查 Redis 连接状态并输出简化的连接信息
 */
@Slf4j
@Component
public class RedisConnectionMonitor {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${spring.redis.cluster.nodes:}")
    private String clusterNodes;

    @Value("${spring.redis.host:localhost}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    /**
     * 应用启动完成后检查Redis连接状态
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logRedisConnectionInfo();
    }

    /**
     * 记录Redis连接信息 - 动态检测模式
     */
    private void logRedisConnectionInfo() {
        Instant startTime = Instant.now();

        try {
            log.info("=== IM-SERVICE Redis 连接信息检查开始 ===");
            log.info("当前环境: {}", activeProfile);
            log.info("Redis连接客户端: Redisson");

            // 动态检测Redis模式
            boolean isClusterMode = clusterNodes != null && !clusterNodes.trim().isEmpty();

            if (isClusterMode) {
                log.info("Redis模式: 集群模式");
                log.info("集群节点: {}", clusterNodes);

                // 解析节点数量
                String[] nodes = clusterNodes.split(",");
                log.info("解析的集群节点数量: {}", nodes.length);
                log.info("集群拓扑扫描: 已禁用");
            } else {
                log.info("Redis模式: 单机模式");
                log.info("服务器地址: {}:{}", redisHost, redisPort);
            }

            log.info("密码配置: 已配置");

            // 测试连接
            testRedisConnection(startTime);

        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("Redis连接检查失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage());
            log.info("=== IM-SERVICE Redis 连接信息检查完成 ===");
        }
    }

    /**
     * 测试Redis连接
     */
    private void testRedisConnection(Instant startTime) {
        try {
            log.info("开始测试Redis连接...");

            // 获取连接并测试
            redisConnectionFactory.getConnection().ping();

            Duration duration = Duration.between(startTime, Instant.now());
            log.info("✅ Redis连接测试成功 - 总耗时: {}ms", duration.toMillis());
            log.info("=== IM-SERVICE Redis 连接信息检查完成 ===");

        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("❌ Redis连接测试失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage());
            log.warn("请检查Redis服务状态和网络连接");
            log.info("=== IM-SERVICE Redis 连接信息检查完成 ===");
        }
    }
}
