package com.lld.im.common.model.message;

import com.lld.im.common.model.ClientInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息内容模型
 * @description: 点对点消息内容数据模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "消息内容模型")
@Data
public class MessageContent extends ClientInfo {

    @ApiModelProperty(value = "消息ID", required = true, example = "msg_123456", notes = "消息的唯一标识")
    private String messageId;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者用户ID", required = true, example = "user456", notes = "消息接收者的用户ID")
    private String toId;

    @ApiModelProperty(value = "消息内容", required = true, example = "Hello, this is a test message", notes = "消息的具体内容")
    private String messageBody;

    @ApiModelProperty(value = "消息时间", required = true, example = "1640995200000", notes = "消息发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\"}", notes = "消息的扩展信息")
    private String extra;

    @ApiModelProperty(value = "消息键", example = "123456789", notes = "消息存储的唯一键值")
    private Long messageKey;

    @ApiModelProperty(value = "消息序列号", example = "1001", notes = "消息的序列号，用于排序和同步")
    private long messageSequence;

}
