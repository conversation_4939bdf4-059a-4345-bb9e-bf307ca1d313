package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 发送群组消息请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "发送群组消息请求模型")
@Data
public class SendGroupMessageReq extends RequestBase {

    @ApiModelProperty(value = "客户端消息ID", example = "msg_group_123456", notes = "客户端生成的唯一消息标识")
    private String messageId;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123")
    private String fromId;

    @ApiModelProperty(value = "群组ID", required = true, example = "group123")
    private String groupId;

    @ApiModelProperty(value = "消息随机数", example = "12345", notes = "用于消息去重")
    private int messageRandom;

    @ApiModelProperty(value = "消息时间戳", example = "1640995200000")
    private long messageTime;

    @ApiModelProperty(value = "消息内容", required = true, example = "Hello everyone!")
    private String messageBody;

    @ApiModelProperty(value = "角标模式", example = "0", notes = "0-需要计数 1-不需要计数")
    private int badgeMode;

    @ApiModelProperty(value = "消息生存时间", example = "86400000", notes = "消息的生存时间，单位毫秒")
    private Long messageLifeTime;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000")
    private Integer appId;

}
