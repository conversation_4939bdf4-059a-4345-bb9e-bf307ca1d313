package com.lld.im.service.system.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 系统通知模型
 */
@ApiModel(description = "系统通知请求模型")
@Data
public class SystemNotification {

    @ApiModelProperty(value = "通知类型(1:系统消息 2:竞猜通知 3:我的关注 4:点赞评论 5:直播提醒 6:活动消息)", notes = "通知类型，根据实际业务添加拓展类型", example = "1", required = true)
    private Integer notificationType;

    @ApiModelProperty(value = "通知标题", notes = "系统通知的标题", example = "系统维护通知", required = true)
    private String title;

    @ApiModelProperty(value = "通知内容", notes = "系统通知的详细内容", example = "系统将于今晚22:00-24:00进行维护", required = true)
    private String content;

    @ApiModelProperty(value = "接收者ID列表", notes = "指定接收通知的用户ID列表，为空则发送给所有用户", example = "[\"user123\", \"user456\"]")
    private List<String> receiverIds;

    @ApiModelProperty(value = "排除用户ID列表", notes = "指定需要排除的用户ID列表，这些用户不会收到通知。该参数为可选参数，当为空或null时不影响现有功能", example = "[\"user789\", \"user101\"]")
    private List<String> excludeUserIds;

    @ApiModelProperty(value = "扩展字段", notes = "额外的扩展信息，可包含跳转链接、图片等", example = "{\"url\": \"https://example.com\", \"image\": \"https://example.com/image.jpg\"}")
    private Map<String, Object> extra;

    @ApiModelProperty(value = "Redis过期时间", notes = "通知在Redis中的缓存过期时间，单位：分钟。默认43200分钟（30天）。当通知类型为5时，支持参数传递，为空或默认值时使用180分钟", example = "43200")
    private Integer redisExpireMinutes = 43200;

    @ApiModelProperty(value = "数据库持久化", notes = "是否将通知持久化存储到数据库。默认true。当通知类型为7时，强制设置为false", example = "true")
    private Boolean persistToDatabase = true;
}