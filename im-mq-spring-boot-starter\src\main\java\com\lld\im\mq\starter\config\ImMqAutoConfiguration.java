package com.lld.im.mq.starter.config;

import com.lld.im.mq.starter.service.ImMessageSender;
import com.lld.im.mq.starter.service.impl.ImMessageSenderImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.validation.Validator;

/**
 * IM MQ自动配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@ConditionalOnClass({RabbitTemplate.class, ConnectionFactory.class})
@ConditionalOnProperty(prefix = "im.mq", name = "enabled", havingValue = "true", matchIfMissing = true)
@EnableConfigurationProperties(ImMqProperties.class)
public class ImMqAutoConfiguration {
    
    private final ImMqProperties imMqProperties;
    
    public ImMqAutoConfiguration(ImMqProperties imMqProperties) {
        this.imMqProperties = imMqProperties;
        log.info("IM MQ Auto Configuration initialized with properties: {}", imMqProperties);
    }
    
    /**
     * 配置RabbitTemplate
     */
    @Bean
    @ConditionalOnMissingBean
    public RabbitTemplate imMqRabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        
        // 设置JSON消息转换器
        rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        
        // 设置发送确认
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.debug("Message sent successfully, correlationData: {}", correlationData);
            } else {
                log.error("Message send failed, correlationData: {}, cause: {}", correlationData, cause);
            }
        });
        
        // 设置返回确认
        rabbitTemplate.setReturnsCallback(returned -> {
            log.error("Message returned, exchange: {}, routingKey: {}, replyCode: {}, replyText: {}", 
                    returned.getExchange(), returned.getRoutingKey(), 
                    returned.getReplyCode(), returned.getReplyText());
        });
        
        log.info("IM MQ RabbitTemplate configured successfully");
        return rabbitTemplate;
    }
    
    /**
     * 系统通知交换机
     */
    @Bean
    @ConditionalOnMissingBean(name = "imNotificationExchange")
    public TopicExchange imNotificationExchange() {
        TopicExchange exchange = new TopicExchange(
            imMqProperties.getNotification().getExchange(),
            imMqProperties.getNotification().isDurable(),
            false
        );
        log.info("IM Notification Exchange created: {}", exchange.getName());
        return exchange;
    }
    
    /**
     * 直播间消息交换机
     */
    @Bean
    @ConditionalOnMissingBean(name = "imLiveroomExchange")
    public TopicExchange imLiveroomExchange() {
        TopicExchange exchange = new TopicExchange(
            imMqProperties.getLiveroom().getExchange(),
            imMqProperties.getLiveroom().isDurable(),
            false
        );
        log.info("IM Liveroom Exchange created: {}", exchange.getName());
        return exchange;
    }
    
    /**
     * IM消息发送服务
     */
    @Bean
    @ConditionalOnMissingBean
    public ImMessageSender imMessageSender(RabbitTemplate rabbitTemplate, Validator validator) {
        log.info("IM Message Sender service created");
        return new ImMessageSenderImpl();
    }
}
