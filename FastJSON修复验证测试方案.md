# FastJSON修复验证测试方案

## 修复总结

### 1. 问题根因
- **全局配置冲突**：`FastJsonConfig`的`@PostConstruct`方法修改了`JSON.DEFAULT_GENERATE_FEATURE`，影响所有直接使用`JSONObject.toJSONString()`的地方
- **序列化不一致**：存储时使用新配置，读取时期望旧格式，导致反序列化失败

### 2. 修复策略
- **移除全局配置**：注释掉可能导致兼容性问题的全局FastJSON配置
- **分类处理**：
  - 直播间相关：使用`FastJsonConfig.toJSONStringWithEmoji()`和`FastJsonConfig.parseObjectWithEmoji()`
  - 其他模块：使用`JSON.toJSONString()`和`JSON.parseObject()`保持兼容性

### 3. 修改文件清单

#### 配置文件
- `im-service/src/main/java/com/lld/im/service/config/FastJsonConfig.java`
  - 移除全局配置修改
  - 提供emoji专用和标准JSON处理方法

#### 直播间模块（使用emoji专用方法）
- `im-service/src/main/java/com/lld/im/service/liveroom/service/impl/LiveRoomServiceImpl.java`
  - 4处调用改为`FastJsonConfig.toJSONStringWithEmoji()`
  - 1处调用改为`FastJsonConfig.parseObjectWithEmoji()`

#### 编码器模块（使用emoji专用方法）
- `im-codec/src/main/java/com/lld/im/codec/MessageEncoder.java`
- `im-codec/src/main/java/com/lld/im/codec/WebSocketMessageEncoder.java`
- `im-codec/src/main/java/com/lld/im/codec/TextWebSocketFrameEncoder.java`

#### 消息服务模块（使用标准方法保持兼容性）
- `im-service/src/main/java/com/lld/im/service/message/service/MessageSyncService.java`
- `im-service/src/main/java/com/lld/im/service/message/service/MessageStoreService.java`

## 测试验证方案

### 1. 单元测试

#### 1.1 FastJsonConfig测试
```java
@Test
public void testEmojiSerialization() {
    // 测试emoji表情序列化
    String emojiText = "Hello 😀🎉👍";
    Map<String, Object> data = new HashMap<>();
    data.put("message", emojiText);
    
    // 使用emoji专用方法
    String json1 = FastJsonConfig.toJSONStringWithEmoji(data);
    Map<String, Object> result1 = FastJsonConfig.parseObjectWithEmoji(json1, Map.class);
    assertEquals(emojiText, result1.get("message"));
    
    // 使用标准方法
    String json2 = FastJsonConfig.toJSONString(data);
    Map<String, Object> result2 = FastJsonConfig.parseObject(json2, Map.class);
    assertEquals(emojiText, result2.get("message"));
}
```

#### 1.2 离线消息兼容性测试
```java
@Test
public void testOfflineMessageCompatibility() {
    // 模拟旧数据格式
    OfflineMessageContent oldMessage = createTestOfflineMessage();
    String oldJson = JSON.toJSONString(oldMessage); // 旧格式
    
    // 验证新代码能正确解析旧数据
    OfflineMessageContent parsed = JSON.parseObject(oldJson, OfflineMessageContent.class);
    assertNotNull(parsed);
    assertEquals(oldMessage.getMessageKey(), parsed.getMessageKey());
}
```

### 2. 集成测试

#### 2.1 直播间emoji消息测试
1. **发送emoji消息**
   - 在直播间发送包含emoji的消息
   - 验证消息正确存储到Redis
   - 验证其他用户能正确接收emoji

2. **历史消息查询**
   - 查询包含emoji的历史消息
   - 验证emoji显示正确

#### 2.2 离线消息功能测试
1. **发送离线消息**
   - 用户A离线时，用户B发送消息
   - 验证消息正确存储到Redis ZSet

2. **同步离线消息**
   - 用户A上线后同步离线消息
   - 验证能正确获取所有离线消息

#### 2.3 系统通知测试
1. **发送系统通知**
   - 发送包含特殊字符的系统通知
   - 验证通知正确存储和推送

### 3. 性能测试

#### 3.1 JSON序列化性能对比
- 对比修复前后的序列化性能
- 确保性能没有显著下降

#### 3.2 内存使用测试
- 监控修复后的内存使用情况
- 确保没有内存泄漏

### 4. 回归测试

#### 4.1 核心功能验证
- [ ] 用户登录/登出
- [ ] 单聊消息发送/接收
- [ ] 群聊消息发送/接收
- [ ] 直播间加入/退出
- [ ] 直播间消息发送/接收
- [ ] 离线消息同步
- [ ] 消息撤回
- [ ] 系统通知

#### 4.2 边界情况测试
- [ ] 空消息处理
- [ ] 超长消息处理
- [ ] 特殊字符消息处理
- [ ] 网络异常情况

### 5. 验证步骤

#### 步骤1：编译验证
```bash
cd d:\workSpace\sst-jim
mvn clean compile
```

#### 步骤2：运行单元测试
```bash
mvn test
```

#### 步骤3：启动服务验证
1. 启动Redis
2. 启动MySQL
3. 启动im-service
4. 启动im-tcp

#### 步骤4：功能验证
1. 使用客户端连接测试
2. 发送包含emoji的直播间消息
3. 测试离线消息功能
4. 验证系统通知功能

### 6. 预期结果

#### 成功标准
- [ ] 直播间emoji消息显示正常
- [ ] 离线消息同步功能正常
- [ ] 系统通知功能正常
- [ ] 所有现有功能保持正常
- [ ] 性能没有显著下降
- [ ] 没有新的错误日志

#### 失败处理
如果测试失败，按以下步骤处理：
1. 检查错误日志
2. 确认JSON序列化/反序列化是否正确
3. 验证Redis数据格式
4. 必要时回滚修改

## 注意事项

1. **数据兼容性**：确保新代码能处理旧格式的Redis数据
2. **渐进式部署**：建议先在测试环境验证，再部署到生产环境
3. **监控告警**：部署后密切监控错误日志和性能指标
4. **回滚准备**：准备快速回滚方案，以防出现问题
