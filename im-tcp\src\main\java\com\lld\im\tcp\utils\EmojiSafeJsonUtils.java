package com.lld.im.tcp.utils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * Emoji安全的JSON工具类
 * 专门用于处理包含emoji表情的JSON序列化和反序列化
 * 
 * <AUTHOR>
 */
@Slf4j
public class EmojiSafeJsonUtils {

    /**
     * 专门用于处理emoji表情的JSON序列化
     * 确保emoji字符在序列化过程中不会被损坏
     *
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public static String toJSONStringWithEmoji(Object object) {
        if (object == null) {
            return null;
        }

        try {
            // 使用标准的JSON序列化，但确保字符串编码正确
            String jsonString = JSON.toJSONString(object);

            // 确保字符串使用UTF-8编码（处理emoji）
            byte[] utf8Bytes = jsonString.getBytes(StandardCharsets.UTF_8);
            return new String(utf8Bytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("JSON序列化失败（emoji模式）: {}", object, e);
            // 降级到标准序列化
            return JSON.toJSONString(object);
        }
    }

    /**
     * 专门用于处理emoji表情的JSONObject解析
     * 
     * @param jsonString JSON字符串
     * @return JSONObject对象
     */
    public static JSONObject parseObjectWithEmoji(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            // 确保字符串使用UTF-8编码处理
            byte[] utf8Bytes = jsonString.getBytes(StandardCharsets.UTF_8);
            String utf8String = new String(utf8Bytes, StandardCharsets.UTF_8);

            return JSON.parseObject(utf8String);
        } catch (Exception e) {
            log.error("JSONObject解析失败（emoji模式）: {}", jsonString, e);
            // 降级到标准解析
            try {
                return JSON.parseObject(jsonString);
            } catch (Exception ex) {
                log.error("标准JSONObject解析也失败: {}", jsonString, ex);
                return null;
            }
        }
    }


    /**
     * 从Redis存储解码字符串，解码Base64确保emoji字符正确恢复
     *
     * @param encodedText Base64编码的字符串
     * @return 解码后的原始字符串
     */
    public static String decodeFromRedisStorage(String encodedText) {
        if (encodedText == null) {
            return null;
        }

        try {
            // 清理可能的前缀字符（如Redisson添加的序列化标识符）
            String cleanedText = encodedText;

            // 如果字符串以非Base64字符开头，尝试找到Base64部分
            if (!isValidBase64Start(encodedText)) {
                // 查找可能的Base64开始位置
                for (int i = 0; i < encodedText.length(); i++) {
                    char c = encodedText.charAt(i);
                    if (isBase64Char(c)) {
                        cleanedText = encodedText.substring(i);
                        log.debug("检测到前缀字符，清理后进行Base64解码: 原始={}, 清理后={}",
                                encodedText.substring(0, Math.min(20, encodedText.length())),
                                cleanedText.substring(0, Math.min(20, cleanedText.length())));
                        break;
                    }
                }
            }

            // 尝试Base64解码
            byte[] decodedBytes = Base64.getDecoder().decode(cleanedText);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            // 如果解码失败，可能是旧格式的数据，直接返回原始字符串
            log.debug("Base64解码失败，可能是旧格式数据: {}", encodedText);
            return encodedText;
        }
    }

    /**
     * 检查字符串是否以有效的Base64字符开头
     */
    private static boolean isValidBase64Start(String text) {
        if (text == null || text.isEmpty()) {
            return false;
        }
        return isBase64Char(text.charAt(0));
    }

    /**
     * 检查字符是否为有效的Base64字符
     */
    private static boolean isBase64Char(char c) {
        return (c >= 'A' && c <= 'Z') ||
                (c >= 'a' && c <= 'z') ||
                (c >= '0' && c <= '9') ||
                c == '+' || c == '/' || c == '=';
    }
}
