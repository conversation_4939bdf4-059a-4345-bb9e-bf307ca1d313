# VS Code Terminal调试环境使用指南

## 🎯 问题解决

您遇到的问题是因为 `debug-vscode.ps1` 脚本有编码和配置问题。现在已经修复了VS Code任务配置，使用稳定的 `debug-simple.ps1` 脚本。

## 🚀 在VS Code Terminal中启动调试环境

### 方法1: 直接在VS Code Terminal中运行（推荐）

打开VS Code Terminal (`Ctrl + \``)，然后运行：

```powershell
# 启动单个服务
.\debug-simple.ps1 im-service -NoSuspend

# 启动所有服务
.\debug-simple.ps1 -NoSuspend

# 启动TCP服务
.\debug-simple.ps1 im-tcp -NoSuspend

# 启动消息存储服务
.\debug-simple.ps1 im-message-store -NoSuspend
```

### 方法2: 使用VS Code任务

按 `Ctrl+Shift+P` → 输入 `Tasks: Run Task` → 选择：

- **Start All IM Debug Services (VS Code Terminal)** - 启动所有服务
- **Start IM Service (VS Code Terminal)** - 启动业务服务
- **Start IM-TCP Debug** - 启动TCP服务
- **Start IM-Message-Store Debug** - 启动消息存储服务

### 方法3: 直接使用Java命令

```powershell
# 启动im-service（调试端口5006）
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar

# 启动im-tcp（调试端口5005）
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml

# 启动im-message-store（调试端口5007）
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5007 -jar im-message-store/target/im-message-store.jar
```

## 🔧 VS Code Terminal的优势

### 为什么使用VS Code Terminal？

1. **集成环境**: 所有日志直接在VS Code中显示
2. **无额外窗口**: 不会创建新的控制台窗口
3. **便于调试**: 可以直接看到服务启动过程和错误信息
4. **快速操作**: 使用VS Code的智能提示和命令历史
5. **统一界面**: 代码、终端、调试器都在同一个界面

### 与系统Terminal的区别

| 特性 | VS Code Terminal | 系统Terminal |
|------|------------------|--------------|
| 窗口管理 | 集成在VS Code中 | 独立窗口 |
| 日志查看 | 直接在编辑器中 | 需要切换窗口 |
| 调试集成 | 无缝连接调试器 | 需要手动配置 |
| 命令历史 | VS Code智能提示 | 基本历史记录 |
| 多任务处理 | 多个Terminal标签 | 多个独立窗口 |

## 🎮 完整调试工作流

### 步骤1: 启动调试服务

在VS Code Terminal中：
```powershell
.\debug-simple.ps1 im-service -NoSuspend
```

输出示例：
```
IM System Debug Launcher
========================
Starting Business Service (Debug Port: 5006)
Command: java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar

Success: Business Service started
  Debug Port: 5006
```

### 步骤2: 连接VS Code调试器

1. 按 `F5` 或点击左侧调试图标
2. 选择 `Attach to IM-Service`
3. 调试器连接成功

### 步骤3: 设置断点并调试

1. 在代码中设置断点
2. 触发相关功能（API调用、前端操作等）
3. 开始调试

### 步骤4: 停止服务

```powershell
.\stop-im-en.ps1 -Force
```

## 📊 调试端口对照表

| 服务 | 调试端口 | VS Code配置 | 启动命令 |
|------|----------|-------------|----------|
| im-tcp | 5005 | Attach to IM-TCP | `.\debug-simple.ps1 im-tcp -NoSuspend` |
| im-service | 5006 | Attach to IM-Service | `.\debug-simple.ps1 im-service -NoSuspend` |
| im-message-store | 5007 | Attach to IM-Message-Store | `.\debug-simple.ps1 im-message-store -NoSuspend` |

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 脚本执行失败
**症状**: 终端进程退出代码为1
**解决方案**:
```powershell
# 检查执行策略
Get-ExecutionPolicy

# 如果受限，临时设置
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 或直接使用参数运行
powershell -ExecutionPolicy Bypass -File .\debug-simple.ps1 im-service -NoSuspend
```

#### 2. 端口被占用
**症状**: "Port already in use"
**解决方案**:
```powershell
# 检查端口占用
netstat -ano | findstr ":5006"

# 停止所有服务
.\stop-im-en.ps1 -Force
```

#### 3. JAR文件不存在
**症状**: "JAR file not found"
**解决方案**:
```powershell
# 构建项目
.\build.ps1
```

#### 4. VS Code调试器无法连接
**症状**: "Failed to connect to remote VM"
**解决方案**:
1. 确认服务已启动且端口正确
2. 检查防火墙设置
3. 重新启动调试服务

## 💡 最佳实践

### 开发调试流程

1. **单服务调试**（推荐）:
   ```powershell
   .\debug-simple.ps1 im-service -NoSuspend
   ```
   - 资源占用少
   - 启动速度快
   - 调试目标明确

2. **全服务调试**（完整测试）:
   ```powershell
   .\debug-simple.ps1 -NoSuspend
   ```
   - 完整的消息流程
   - 服务间交互调试
   - 集成测试

### 性能优化建议

1. **使用-NoSuspend参数**: 避免等待调试器连接
2. **按需启动服务**: 只启动需要调试的服务
3. **及时停止服务**: 调试完成后释放资源
4. **使用条件断点**: 提高调试效率

### 团队协作

1. **统一调试端口**: 团队成员使用相同的端口配置
2. **共享配置文件**: 同步 `.vscode` 目录
3. **文档化流程**: 记录调试步骤和注意事项

## 🔄 快速命令参考

```powershell
# 快速启动
.\debug-simple.ps1 im-service -NoSuspend

# 快速停止
.\stop-im-en.ps1 -Force

# 检查状态
netstat -ano | findstr ":500"

# 查看进程
Get-Process java

# 构建项目
.\build.ps1

# 查看帮助
.\debug-simple.ps1 -Help
```

## 📝 总结

现在您可以完全在VS Code Terminal中进行调试：

1. ✅ **修复了任务配置** - 使用稳定的 `debug-simple.ps1`
2. ✅ **提供了多种启动方式** - 命令行、任务、直接Java命令
3. ✅ **优化了调试体验** - 所有输出在VS Code中显示
4. ✅ **完善了故障排除** - 常见问题的解决方案

推荐使用 `.\debug-simple.ps1 im-service -NoSuspend` 在VS Code Terminal中启动单个服务进行调试！
