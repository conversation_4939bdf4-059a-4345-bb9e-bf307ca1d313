package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 全员禁言请求模型
 * 
 * @description: 直播间全员禁言操作的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "全员禁言请求模型")
@Data
public class MuteAllReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要执行全员禁言操作的直播间唯一标识")
    @NotBlank(message = "{validation.room.id.not.blank}")
    private String roomId;

    @ApiModelProperty(value = "禁言状态", required = true, example = "1", notes = "1-全员禁言 0-解除全员禁言")
    @NotNull(message = "{validation.mute.status.not.null}")
    private Integer mute;

}
