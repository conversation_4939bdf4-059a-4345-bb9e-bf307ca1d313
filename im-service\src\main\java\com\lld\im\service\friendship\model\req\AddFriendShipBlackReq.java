package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 添加黑名单请求
 */
@ApiModel(description = "添加黑名单请求模型")
@Data
public class AddFriendShipBlackReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起拉黑操作的用户ID")
    @NotBlank(message = "用户id不能为空")
    private String fromId;

    @ApiModelProperty(value = "被拉黑用户ID", required = true, example = "user456", notes = "要添加到黑名单的用户ID")
    private String toId;
}
