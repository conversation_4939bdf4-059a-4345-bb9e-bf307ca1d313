# IM系统构建脚本使用指南

本项目提供了多种构建脚本，适用于不同的开发和部署场景。

## 📁 脚本文件概览

### 主要构建脚本
- **`build-simple.ps1`** - 主要构建脚本（推荐）
- **`quick-build.ps1`** - 快速构建脚本
- **`build.bat`** - 批处理菜单界面

### 辅助脚本
- **`deploy_package.ps1`** - 部署打包脚本（已存在）
- **`stop-im-en.ps1`** - 服务停止脚本（已存在）

## 🚀 快速开始

### 1. 基本构建
```powershell
# 构建所有模块
.\build-simple.ps1

# 快速构建（跳过测试）
.\quick-build.ps1

# 使用批处理菜单
.\build.bat
```

### 2. 指定模块构建
```powershell
# 只构建im-service模块
.\build-simple.ps1 im-service

# 构建多个模块
.\build-simple.ps1 im-tcp im-service

# 快速构建指定模块
.\quick-build.ps1 im-message-store
```

### 3. 清理构建
```powershell
# 清理并构建所有模块
.\build-simple.ps1 -Clean

# 清理构建指定模块
.\build-simple.ps1 -Clean im-service
```

## 🛠️ 详细使用说明

### build-simple.ps1 (主要构建脚本)

**功能特点：**
- 完整的环境检查
- 支持模块化构建
- 清晰的构建日志
- 自动构建结果统计

**参数说明：**
```powershell
-Modules        # 指定构建模块
-Clean          # 清理构建
-SkipTests      # 跳过测试
-Package        # 构建后自动打包
-Help           # 显示帮助
```

**使用示例：**
```powershell
# 基本构建
.\build-simple.ps1

# 清理构建，跳过测试
.\build-simple.ps1 -Clean -SkipTests

# 构建指定模块
.\build-simple.ps1 im-service im-tcp

# 构建并自动打包
.\build-simple.ps1 -Package
```

### quick-build.ps1 (快速构建脚本)

**功能特点：**
- 轻量级，启动快速
- 自动跳过测试
- 简化的输出信息

**使用示例：**
```powershell
# 快速构建所有模块
.\quick-build.ps1

# 快速构建指定模块
.\quick-build.ps1 im-service

# 清理快速构建
.\quick-build.ps1 -Clean

# 构建并打包
.\quick-build.ps1 -Package
```



## 🏗️ 项目结构说明

### Maven模块结构
```
im-system/
├── pom.xml                 # 父POM文件
├── im-common/              # 公共模块
├── im-codec/               # 编解码模块
├── im-tcp/                 # TCP服务模块
├── im-service/             # 核心业务服务
├── im-message-store/       # 消息存储服务
└── im-mq-spring-boot-starter/  # MQ启动器
```

### 构建产物
构建完成后，各模块的JAR文件位置：
- `im-tcp/target/im-tcp.jar`
- `im-service/target/im-service.jar`
- `im-message-store/target/im-message-store.jar`

## ⚙️ 环境要求

### 必需环境
- **Java**: JDK 8+ (推荐Java 8, 11或17)
- **Maven**: 3.6+ (推荐3.8+)
- **PowerShell**: 5.0+ (Windows 10/11自带)

### 环境检查
```powershell
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 检查PowerShell版本
$PSVersionTable.PSVersion
```

## 🔧 常见问题解决

### 1. Maven未找到
```powershell
# 检查Maven是否在PATH中
where mvn

# 设置MAVEN_HOME环境变量
$env:MAVEN_HOME = "C:\apache-maven-3.8.6"
$env:PATH += ";$env:MAVEN_HOME\bin"
```

### 2. Java版本问题
```powershell
# 检查JAVA_HOME
echo $env:JAVA_HOME

# 设置JAVA_HOME
$env:JAVA_HOME = "C:\Program Files\Java\jdk1.8.0_XXX"
```

### 3. 构建失败
```powershell
# 清理Maven缓存
mvn dependency:purge-local-repository

# 强制更新依赖
mvn clean package -U

# 离线模式构建
.\build.ps1 -Offline
```

### 4. 内存不足
```powershell
# 设置Maven内存参数
$env:MAVEN_OPTS = "-Xmx2048m -XX:MaxPermSize=512m"
```

## 📋 构建流程

### 标准构建流程
1. **环境检查** - 验证Java和Maven环境
2. **依赖解析** - 下载和解析项目依赖
3. **编译** - 编译源代码
4. **测试** - 运行单元测试（可跳过）
5. **打包** - 生成JAR文件
6. **验证** - 验证构建结果

### 开发构建流程
1. **变化检测** - 检查源码是否有变化
2. **增量编译** - 只编译变化的模块
3. **快速打包** - 跳过测试快速打包
4. **服务重启** - 自动重启相关服务

## 🎯 最佳实践

### 日常开发
```powershell
# 推荐使用主要构建脚本
.\build-simple.ps1 -SkipTests
```

### 完整测试
```powershell
# 使用主要构建脚本
.\build-simple.ps1 -Clean
```

### 生产部署
```powershell
# 构建并打包
.\build-simple.ps1 -Clean -Package
```

### 持续集成
```powershell
# CI环境推荐
.\build-simple.ps1 -Clean -SkipTests
```

## 📝 版本信息

- **创建日期**: 2025-08-04
- **版本**: 1.0
- **兼容性**: Windows 10/11 + PowerShell 5.0+
- **Maven版本**: 3.6+
- **Java版本**: JDK 8+
