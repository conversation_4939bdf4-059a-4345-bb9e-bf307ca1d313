package com.lld.im.service.group.model;

import com.lld.im.common.enums.GroupJoinThresholdEnum;
import lombok.Data;

/**
 * 群组进群门槛配置模型
 * @author: lld
 * @description: 群组进群门槛的配置信息
 */
@Data
public class GroupJoinThresholdConfig {
    
    /**
     * 门槛类型
     */
    private GroupJoinThresholdEnum type = GroupJoinThresholdEnum.NO_REQUIREMENT;
    
    /**
     * 门槛设置时间
     */
    private Long updateTime;
    
    /**
     * 门槛设置者
     */
    private String updatedBy;
    
    /**
     * 验证配置是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return type != null;
    }
    
    /**
     * 创建默认配置
     * @return 默认的门槛配置
     */
    public static GroupJoinThresholdConfig createDefault() {
        GroupJoinThresholdConfig config = new GroupJoinThresholdConfig();
        config.setType(GroupJoinThresholdEnum.NO_REQUIREMENT);
        config.setUpdateTime(System.currentTimeMillis());
        return config;
    }
}
