package com.lld.im.tcp.handler;

import com.lld.im.common.constant.Constants;
import com.lld.im.tcp.utils.SessionSocketHolder;
import com.lld.im.tcp.utils.AttributeKeys;
import com.lld.im.tcp.redis.RedisManager;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.AttributeKey;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Slf4j
public class HeartBeatHandler extends ChannelInboundHandlerAdapter {

    private Long heartBeatTime;

    public HeartBeatHandler(Long heartBeatTime) {
        this.heartBeatTime = heartBeatTime;
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {

        // 判断evt是否是IdleStateEvent（用于触发用户事件，包含 读空闲/写空闲/读写空闲 ）
        if (evt instanceof IdleStateEvent) {
            IdleStateEvent event = (IdleStateEvent)evt;		// 强制类型转换
            String userId = ctx.channel().attr(AttributeKeys.USER_ID).get();
            Boolean isGuest = ctx.channel().attr(AttributeKeys.IS_GUEST).get();

            if (event.state() == IdleState.READER_IDLE) {
                log.debug("📖 读空闲检测: userId={}, 用户类型={}, channel={}",
                         userId, isGuest != null && isGuest ? "游客" : "正式用户", ctx.channel().id());
            } else if (event.state() == IdleState.WRITER_IDLE) {
                log.debug("✍️ 写空闲检测: userId={}, 用户类型={}, channel={}",
                         userId, isGuest != null && isGuest ? "游客" : "正式用户", ctx.channel().id());
            } else if (event.state() == IdleState.ALL_IDLE) {
                Long lastReadTime = ctx.channel().attr(AttributeKeys.READ_TIME).get();
                long now = System.currentTimeMillis();

                if(lastReadTime != null && now - lastReadTime > heartBeatTime){
                    log.warn("💔 心跳超时，连接将被关闭: userId={}, 用户类型={}, 超时时间={}ms, channel={}",
                            userId, isGuest != null && isGuest ? "游客" : "正式用户",
                            (now - lastReadTime), ctx.channel().id());

                    SessionSocketHolder.offlineUserSession((NioSocketChannel) ctx.channel());

/*                    // 根据用户类型执行不同的离线处理逻辑，已使用实时断联和定时清除策略，不需要该逻辑
                    if (isGuest != null && isGuest) {
                        // 游客心跳超时处理
                        handleGuestHeartbeatTimeout((NioSocketChannel) ctx.channel());
                    } else {
                        // 正式用户心跳超时处理

                    }*/
                }
            }
        }
    }

    /**
     * 处理游客心跳超时
     * @param channel 超时的游客连接
     */
    private void handleGuestHeartbeatTimeout(NioSocketChannel channel) {
        try {
            String userId = channel.attr(AttributeKeys.USER_ID).get();
            Integer appId = channel.attr(AttributeKeys.APP_ID).get();

            log.info("🎭 游客心跳超时处理: userId={}, appId={}, channel={}", userId, appId, channel.id());

            // 移除游客连接
            SessionSocketHolder.removeGuestChannel(channel);

            // 从所有直播间中移除
            if (appId != null && userId != null) {
                SessionSocketHolder.removeUserFromAllRooms(appId, userId);
            }

            // 清理Redis中的游客标识
            if (appId != null && userId != null) {
                try {
                    RedissonClient redissonClient = RedisManager.getRedissonClient();
                    String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                    redissonClient.getBucket(guestKey).delete();
                } catch (Exception e) {
                    log.warn("清理游客Redis标识失败: userId={}", userId, e);
                }
            }

            log.info("✅ 游客心跳超时处理完成: userId={}", userId);

        } catch (Exception e) {
            log.error("❌ 处理游客心跳超时时异常", e);
        }
    }
}
