package com.lld.im.tcp.handler;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.common.enums.command.LiveRoomCommand;
import com.lld.im.tcp.redis.RedisManager;
import com.lld.im.tcp.utils.AttributeKeys;
import com.lld.im.tcp.utils.EmojiSafeJsonUtils;
import com.lld.im.tcp.utils.SessionSocketHolder;

import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 直播间消息处理器
 * 负责将直播间消息发送给直播间内的用户
 * 重构后使用标准格式，移除递归解析逻辑
 */
@Slf4j
public class LiveRoomMessageHandler {

    /**
     * 处理直播间消息
     * 重构后直接使用新格式解析，无需递归查找
     * 
     * @param message 消息内容
     */
    public static void handle(Message message) {
        try {
            String data = JSONObject.toJSONString(message.getMessagePack());
            log.info("处理直播间消息: {}", data);

            // 解析MessagePack对象
            JSONObject messagePackObj = JSON.parseObject(data);

            // 从MessagePack中获取真正的消息数据（在data字段中）
            JSONObject realMessageObj = messagePackObj.getJSONObject("data");
            if (realMessageObj == null) {
                log.error("MessagePack缺少data字段: {}", messagePackObj.toJSONString());
                return;
            }

            log.debug("真正的消息内容: {}", realMessageObj.toJSONString());

            // 从真正的消息对象中获取基础字段（在顶层）
            Integer appId = realMessageObj.getInteger("appId");
            Integer clientType = realMessageObj.getInteger("clientType");
            String imei = realMessageObj.getString("imei");
            Integer command = realMessageObj.getInteger("command");

            // 从真正的消息对象中获取业务字段（在data中）
            JSONObject dataObj = realMessageObj.getJSONObject("data");
            if (dataObj == null) {
                log.error("消息缺少业务data字段: {}", realMessageObj.toJSONString());
                return;
            }

            String roomId = dataObj.getString("roomId");
            log.debug("从业务data中提取roomId: {}, data内容: {}", roomId, dataObj.toJSONString());

            if (StringUtils.isEmpty(roomId)) {
                log.error("直播间消息缺少roomId字段，业务data内容: {}", dataObj.toJSONString());
                return;
            }

            String userId = dataObj.getString("userId");
            String action = dataObj.getString("action");

            // 根据消息类型记录不同的日志
            if (command == LiveRoomCommand.LIVE_ROOM_MSG.getCommand() && "chat".equals(action)) {
                log.info("处理系统通知消息: roomId={}, userId={}, content={}",
                        roomId, userId, dataObj.getString("content"));
            } else if (command == LiveRoomCommand.LIVE_ROOM_JOIN.getCommand() && "join".equals(action)) {
                log.info("处理结构化加入消息: roomId={}, userId={}, nickname={}",
                        roomId, userId, dataObj.getString("fromNickname"));
            } else if (command == LiveRoomCommand.LIVE_ROOM_LEAVE.getCommand() && "leave".equals(action)) {
                log.info("处理结构化离开消息: roomId={}, userId={}, reason={}",
                        roomId, userId, dataObj.getString("content"));
            } else {
                log.info("处理其他直播间消息: roomId={}, userId={}, action={}, command={}",
                        roomId, userId, action, command);
            }

            // 处理特定的直播间操作（仅处理结构化消息，不处理系统通知）
            handleRoomOperations(command, action, userId, roomId, appId);

            // 获取直播间内的所有用户
            Set<String> roomUsers = SessionSocketHolder.getRoomUsers(roomId);
            if (roomUsers == null || roomUsers.isEmpty()) {
                log.debug("🎬 直播间 {} 没有在线用户", roomId);
                return;
            }

            log.debug("🎬 直播间 {} 有 {} 个在线用户: {}", roomId, roomUsers.size(), roomUsers);

            // 获取发送者信息
            String fromId = dataObj.getString("userId");
            log.debug("📤 准备推送消息: fromId={}, command={}, roomUsers={}", fromId, command, roomUsers.size());

            // 向直播间内的所有用户发送消息（发送真正的消息内容）
            sendMessageToRoomUsers(roomUsers, fromId, appId, clientType, imei, command, realMessageObj.toJSONString());

            // 处理关闭直播间的消息 (command=5021, action="close")
            // 用于清理直播间的所有在线用户和本地缓存
            if (command == LiveRoomCommand.LIVE_ROOM_CLOSE.getCommand() && "close".equals(action)) {
                if (roomUsers != null && !roomUsers.isEmpty()) {
                    // 批量判断用户身份，提高性能
                    Map<String, Boolean> userGuestStatus = SessionSocketHolder.batchCheckGuestUsers(appId,
                            new ArrayList<>(roomUsers));

                    for (String user : roomUsers) {
                        boolean isGuest = userGuestStatus.getOrDefault(user, false);
                        SessionSocketHolder.removeUserFromRoom(roomId, user, isGuest);
                    }
                    log.info("🎬 直播间 {} 已关闭 - 清理了 {} 个在线用户", roomId, roomUsers.size());
                } else {
                    log.info("🎬 直播间 {} 已关闭 - 没有在线用户需要清理", roomId);
                }

                // 清空直播间本地缓存
                SessionSocketHolder.clearRoomCache(roomId);
            }
        } catch (Exception e) {
            log.error("处理直播间消息失败", e);
        }
    }

    /**
     * 处理直播间操作（加入、离开等）
     *
     * 消息类型说明：
     * 1. 系统通知消息 (command=5010, action="chat"): 用于聊天记录显示，不触发状态变更
     * 2. 结构化加入消息 (command=5012, action="join"): 用于用户状态管理，触发addUserToRoom
     * 3. 结构化离开消息 (command=5013, action="leave"): 用于用户状态管理，触发removeUserFromRoom
     *
     * @param command 命令类型
     * @param action  操作类型
     * @param userId  用户ID
     * @param roomId  直播间ID
     * @param appId   应用ID
     */
    private static void handleRoomOperations(Integer command, String action, String userId, String roomId,
            Integer appId) {
        if (command == null || userId == null || roomId == null) {
            return;
        }

        // 只处理结构化的加入直播间消息 (command=5012, action="join")
        // 用于客户端更新在线用户列表和用户状态管理
        if (command == LiveRoomCommand.LIVE_ROOM_JOIN.getCommand() && "join".equals(action)) {
            boolean isGuest = SessionSocketHolder.isUserGuest(appId, userId);
            SessionSocketHolder.addUserToRoom(roomId, userId, isGuest);
            log.info("🎬 用户 {} 加入直播间 {} - 更新在线用户列表 ({})", userId, roomId, isGuest ? "游客" : "正式用户");

            // 向新加入的用户推送直播间最近消息
            pushRecentMessagesToNewUser(roomId, userId, appId);
        }

        // 只处理结构化的离开直播间消息 (command=5013, action="leave")
        // 用于客户端更新在线用户列表和用户状态管理
        else if (command == LiveRoomCommand.LIVE_ROOM_LEAVE.getCommand() && "leave".equals(action)) {
            boolean isGuest = SessionSocketHolder.isUserGuest(appId, userId);
            SessionSocketHolder.removeUserFromRoom(roomId, userId, isGuest);
            log.info("🎬 用户 {} 离开直播间 {} - 更新在线用户列表 ({})", userId, roomId, isGuest ? "游客" : "正式用户");
        }

        // 处理创建直播间后主播自动加入的消息 (command=5020, action="create")
        // 用于客户端更新在线用户列表和用户状态管理
        else if (command == LiveRoomCommand.LIVE_ROOM_CREATE.getCommand() && "create".equals(action)) {
            boolean isGuest = SessionSocketHolder.isUserGuest(appId, userId);
            SessionSocketHolder.addUserToRoom(roomId, userId, isGuest);
            log.info("🎬 主播 {} 创建并加入直播间 {} - 更新在线用户列表 ({})", userId, roomId, isGuest ? "游客" : "正式用户");
        }

        else if (command == LiveRoomCommand.LIVE_ROOM_KICK.getCommand() && "kick".equals(action)) {
            boolean isGuest = SessionSocketHolder.isUserGuest(appId, userId);
            SessionSocketHolder.removeUserFromRoom(roomId, userId, isGuest);

            log.info("🎬 用户 {} 被踢出直播间 {} - 更新在线用户列表 ({})", userId, roomId, isGuest ? "游客" : "正式用户");
        }
    }

    /**
     * 向直播间用户发送消息
     *
     * @param roomUsers  直播间用户列表
     * @param fromId     发送者ID
     * @param appId      应用ID
     * @param clientType 客户端类型
     * @param imei       设备标识
     * @param command    命令类型
     * @param data       消息数据
     */
    private static void sendMessageToRoomUsers(Set<String> roomUsers, String fromId, Integer appId,
            Integer clientType, String imei, Integer command, String data) {
        log.debug("📡 开始向直播间用户推送消息: 总用户数={}, fromId={}, command={}", roomUsers.size(), fromId, command);

        // 实时清理模式：专注消息推送，依赖channelInactive处理失效用户
        for (String userId : roomUsers) {
            try {
                // 如果是消息发送者自己，且消息类型是聊天消息，且不是系统消息，则跳过发送者的当前终端（避免消息回环）
                if (userId.equals(fromId) &&
                        command != null && command == LiveRoomCommand.LIVE_ROOM_MSG.getCommand() &&
                        clientType != null && StringUtils.isNotEmpty(imei) &&
                        !"system".equals(fromId)) { // 系统消息不进行设备过滤

                    sendToUserExcludingSender(appId, userId, clientType, imei, data);
                } else {
                    // 对于其他用户，或系统消息，正常发送消息
                    log.debug("📤 向用户推送: userId={}", userId);
                    sendToUser(appId, userId, data);
                }

            } catch (Exception e) {
                log.error("❌ 向用户 {} 发送直播间消息失败", userId, e);
            }
        }

        log.debug("📡 直播间消息推送完成: 目标用户数={}", roomUsers.size());
    }

    /**
     * 发送消息给用户，排除发送者的当前终端
     * 
     * @return 是否成功发送消息（至少有一个有效连接）
     */
    private static boolean sendToUserExcludingSender(Integer appId, String userId, Integer senderClientType,
            String senderImei, String data) {
        List<NioSocketChannel> channels = SessionSocketHolder.get(appId, userId);

        boolean hasValidConnection = false;

        if (channels != null && !channels.isEmpty()) {
            for (NioSocketChannel channel : channels) {
                if (channel == null || !channel.isActive()) {
                    log.warn("⚠️ Channel已断开或无效: userId={}", userId);
                    continue;
                }

                // 获取终端信息
                Integer channelClientType = channel.attr(AttributeKeys.CLIENT_TYPE).get();
                String channelImei = channel.attr(AttributeKeys.IMEI).get();

                // 跳过发送消息的终端，其他终端正常发送（实现多端同步）
                if (senderClientType.equals(channelClientType) && senderImei.equals(channelImei)) {
                    log.debug("⏭️ 跳过发送者当前终端: userId={}, clientType={}, imei={}", userId, channelClientType,
                            channelImei);
                    continue;
                }
                boolean sent = sendToChannel(channel, data);
                if (sent) {
                    hasValidConnection = true;
                }
            }
        }

        return hasValidConnection;
    }

    /**
     * 发送消息给用户的所有终端
     * 
     * @return 是否成功发送消息（至少有一个有效连接）
     */
    private static boolean sendToUser(Integer appId, String userId, String data) {
        List<NioSocketChannel> channels = SessionSocketHolder.get(appId, userId);

        log.debug("🔍 查找用户Channel: appId={}, userId={}, 找到{}个Channel",
                appId, userId, channels != null ? channels.size() : 0);

        boolean hasValidConnection = false;

        if (channels != null && !channels.isEmpty()) {
            for (NioSocketChannel channel : channels) {
                // 检查Channel状态
                if (channel != null && channel.isActive()) {
                    boolean sent = sendToChannel(channel, data);
                    if (sent) {
                        hasValidConnection = true;
                    }
                }
            }
        }

        return hasValidConnection;
    }

    /**
     * 发送消息到指定通道
     * 
     * @return 是否成功发送（Channel有效且消息已提交发送）
     */
    private static boolean sendToChannel(NioSocketChannel channel, String data) {
        try {
            if (channel == null) {
                log.warn("❌ Channel为null，无法发送消息");
                return false;
            }

            if (!channel.isActive()) {
                log.warn("❌ Channel已断开，无法发送消息: channel={}", channel.id());
                return false;
            }

            log.debug("📡 发送消息到Channel: channel={}, dataLength={}",
                    channel.id(), data != null ? data.length() : 0);
            log.debug("📄 消息内容: {}", data);

            try {
                // 解析data为JSONObject以获取command
                JSONObject dataObj = JSON.parseObject(data);
                Integer command = dataObj.getInteger("command");

                // 构建MessagePack对象
                MessagePack<Object> messagePack = new MessagePack<>();
                messagePack.setCommand(command);
                messagePack.setData(dataObj.get("data"));

                log.debug("📦 构建MessagePack: command={}, hasData={}", command, messagePack.getData() != null);

                // 统一发送方式，不区分连接类型
                ChannelFuture future = channel.writeAndFlush(messagePack);
                future.addListener(new ChannelFutureListener() {
                    @Override
                    public void operationComplete(ChannelFuture future) throws Exception {
                        if (future.isSuccess()) {
                            log.debug("✅ 消息发送成功: channel={}, command={}", channel.id(), command);
                        } else {
                            log.error("❌ 消息发送失败: channel={}, command={}, cause={}",
                                    channel.id(), command, future.cause().getMessage());
                        }
                    }
                });
                return true; // 消息已提交发送
            } catch (Exception e) {
                log.error("❌ 构建MessagePack失败: channel={}, data={}", channel.id(), data, e);
                return false;
            }
        } catch (Exception e) {
            log.error("❌ 发送消息到Channel异常: channel={}", channel != null ? channel.id() : "null", e);
            return false;
        }
    }

    /**
     * 向新加入的用户推送直播间最近消息
     * 直接从Redis获取消息，避免服务间调用
     *
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param appId  应用ID
     */
    private static void pushRecentMessagesToNewUser(String roomId, String userId, Integer appId) {
        try {
            log.info("开始向新加入用户推送最近消息: roomId={}, userId={}, appId={}", roomId, userId, appId);

            // 获取用户Channel
            List<NioSocketChannel> userChannels = SessionSocketHolder.get(appId, userId);
            if (userChannels == null || userChannels.isEmpty()) {
                log.info("用户不在线，跳过消息推送: roomId={}, userId={}", roomId, userId);
                return;
            }

            // 直接从Redis获取直播间最近20条消息
            List<JSONObject> recentMessages = getRecentMessagesFromRedis(roomId, appId, 20);
            /*
             * if (recentMessages == null || recentMessages.isEmpty()) {
             * log.info("直播间暂无历史消息，跳过推送: roomId={}, userId={}", roomId, userId);
             * return;
             * }
             */

            // 构建欢迎消息数据，只保留必要属性
            JSONObject welcomeMessageData = new JSONObject();
            welcomeMessageData.put("roomId", roomId);
            welcomeMessageData.put("userId", userId);
            welcomeMessageData.put("messages", recentMessages);

            // 构建标准格式消息
            JSONObject standardMessage = new JSONObject();
            standardMessage.put("command", LiveRoomCommand.LIVE_ROOM_GET_MESSAGES.getCommand());
            standardMessage.put("appId", appId);
            standardMessage.put("data", welcomeMessageData);

            // 直接发送给用户
            // 使用emoji安全的JSON序列化方法
            sendToUser(appId, userId, EmojiSafeJsonUtils.toJSONStringWithEmoji(standardMessage));

            log.info("成功向新加入用户推送最近消息: roomId={}, userId={}, messageCount={}",
                    roomId, userId, recentMessages.size());

        } catch (Exception e) {
            // 推送消息失败不应影响加入直播间的主流程，只记录错误日志
            log.error("向新加入用户推送最近消息时发生异常: roomId={}, userId={}", roomId, userId, e);
        }
    }

    /**
     * 直接从Redis获取直播间最近消息
     * 使用与im-service相同的方式获取消息，避免序列化问题
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @param limit  消息数量限制
     * @return 消息列表（JSON对象格式）
     */
    private static List<JSONObject> getRecentMessagesFromRedis(String roomId, Integer appId, Integer limit) {
        try {
            // Redis键名格式：liveroom:{appId}:{roomId}:messages
            String redisKey = String.format("liveroom:%d:%s:messages", appId, roomId);

            // 使用Redisson获取最近消息，与im-service保持一致
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            RScoredSortedSet<String> messageSet = redissonClient.getScoredSortedSet(redisKey);

            // 获取最近的limit条消息（按分数倒序）
            Collection<String> messageJsonSet = messageSet.valueRangeReversed(0, limit - 1);

            if (messageJsonSet != null && !messageJsonSet.isEmpty()) {
                List<JSONObject> messageList = new ArrayList<>();

                // 解析每个消息JSON字符串
                for (String messageJson : messageJsonSet) {
                    try {
                        // 先尝试Base64解码，如果失败则说明是旧格式数据
                        String decodedJson = EmojiSafeJsonUtils.decodeFromRedisStorage(messageJson);

                        // 然后清理可能的序列化前缀字符
                        String cleanJson = cleanJsonString(decodedJson);
                        if (cleanJson != null && !cleanJson.isEmpty()) {
                            // 使用emoji安全的JSON解析方法
                            JSONObject msgObj = EmojiSafeJsonUtils.parseObjectWithEmoji(cleanJson);
                            if (msgObj != null) {
                                messageList.add(msgObj);
                            }
                        } else {
                            // 如果清理后为空，可能是Base64解码失败，尝试直接解析原始字符串
                            String originalCleanJson = cleanJsonString(messageJson);
                            if (originalCleanJson != null && !originalCleanJson.isEmpty()) {
                                JSONObject msgObj = EmojiSafeJsonUtils.parseObjectWithEmoji(originalCleanJson);
                                if (msgObj != null) {
                                    messageList.add(msgObj);
                                }
                            }
                        }
                    } catch (Exception parseEx) {
                        log.warn("解析消息JSON失败，跳过该消息: {}", messageJson, parseEx);
                    }
                }

                // 转换为正序（时间从早到晚）
                Collections.reverse(messageList);

                log.debug("从Redis获取直播间{}的消息，共{}条", roomId, messageList.size());
                return messageList;
            }

            log.debug("Redis中没有找到直播间{}的消息", roomId);
            return new ArrayList<>();

        } catch (Exception e) {
            log.error("从Redis获取直播间消息失败: roomId={}, appId={}", roomId, appId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 清理JSON字符串中的序列化前缀字符
     * 处理Redisson存储时可能添加的序列化标识符
     *
     * @param jsonString 原始JSON字符串
     * @return 清理后的JSON字符串
     */
    private static String cleanJsonString(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return jsonString;
        }

        // 查找第一个 '{' 字符的位置，这是JSON对象的开始
        int jsonStart = jsonString.indexOf('{');
        if (jsonStart > 0) {
            // 如果 '{' 前面有其他字符，则截取从 '{' 开始的部分
            String cleaned = jsonString.substring(jsonStart);
            log.debug("清理JSON字符串: 原始长度={}, 清理后长度={}", jsonString.length(), cleaned.length());
            return cleaned;
        } else if (jsonStart == 0) {
            // 如果 '{' 就在开头，直接返回原字符串
            return jsonString;
        } else {
            // 如果没有找到 '{'，说明不是有效的JSON对象
            log.warn("无效的JSON字符串，未找到开始字符: {}", jsonString);
            return null;
        }
    }

}