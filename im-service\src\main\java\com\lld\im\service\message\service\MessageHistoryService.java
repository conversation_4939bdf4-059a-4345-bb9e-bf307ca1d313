package com.lld.im.service.message.service;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.message.model.req.GetConversationMessagesReq;
import com.lld.im.service.message.model.req.SearchMessageReq;
import com.lld.im.service.message.model.resp.MessageHistoryResp;
import com.lld.im.service.message.model.resp.PageResult;
import com.lld.im.service.message.model.resp.SearchMessageResp;

import java.util.List;

/**
 * 消息历史记录服务接口
 * @description: 提供消息历史记录查询相关功能
 * @author: lld
 * @version: 1.0
 */
public interface MessageHistoryService {

    /**
     * 查询会话历史消息
     * @param req 查询请求参数
     * @return 分页查询结果
     */
    ResponseVO<PageResult<MessageHistoryResp>> getConversationMessages(GetConversationMessagesReq req);

    /**
     * 模糊搜索消息内容
     * @param req 搜索请求参数
     * @return 搜索结果列表
     */
    ResponseVO<List<SearchMessageResp>> searchMessages(SearchMessageReq req);

}