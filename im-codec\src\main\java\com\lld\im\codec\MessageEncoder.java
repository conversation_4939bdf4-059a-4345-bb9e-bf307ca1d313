package com.lld.im.codec;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.MessagePack;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 消息编码类，私有协议规则，前4位表示长度，接着command4位，后面是数据
 **/
public class MessageEncoder extends MessageToByteEncoder {

    private static final Logger logger = LoggerFactory.getLogger(MessageEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, Object msg, ByteBuf out) throws Exception {
        if(msg instanceof MessagePack){
            MessagePack msgBody = (MessagePack) msg;
            String s = JSONObject.toJSONString(msgBody.getData());
            // 明确指定UTF-8编码，解决中文乱码问题
            byte[] bytes = s.getBytes("UTF-8");

            logger.debug("编码消息: command={}, length={}, data={}",
                      msgBody.getCommand(), bytes.length, s);

            out.writeInt(msgBody.getCommand());
            out.writeInt(bytes.length);
            out.writeBytes(bytes);

            logger.debug("消息编码完成: {}", ctx.channel().remoteAddress());
        } else {
            logger.warn("不支持的消息类型: {}", msg.getClass().getName());
        }
    }

}
