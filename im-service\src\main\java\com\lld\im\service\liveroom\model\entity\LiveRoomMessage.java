package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间消息实体类
 */
@Data
@TableName("im_live_room_message")
public class LiveRoomMessage {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 发送者ID
     */
    private String fromId;

    /**
     * 发送者昵称
     */
    private String fromNickname;

    /**
     * 发送者头像
     */
    private String fromAvatar;

    /**
     * 消息类型 1-文本 2-图片 3-语音 4-视频 5-表情 6-礼物 7-点赞 8-系统消息 9-加入消息 10-离开消息
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息序列号
     */
    private Long sequence;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 发送时间
     */
    private Long sendTime;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 扩展字段，用于存储额外信息（JSON格式）
     */
    private String extra;
} 