package com.lld.im.service.message.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.GroupMemberRoleEnum;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import com.lld.im.service.conversation.dao.mapper.ImConversationSetMapper;
import com.lld.im.service.group.dao.ImGroupMessageHistoryEntity;
import com.lld.im.service.group.dao.ImGroupMemberEntity;
import com.lld.im.service.group.dao.mapper.ImGroupMessageHistoryMapper;
import com.lld.im.service.group.dao.mapper.ImGroupMemberMapper;
import com.lld.im.service.group.model.resp.GetRoleInGroupResp;
import com.lld.im.service.group.service.ImGroupMemberService;
import com.lld.im.service.message.dao.ImMessageBodyEntity;
import com.lld.im.service.message.dao.ImMessageHistoryEntity;
import com.lld.im.service.message.dao.mapper.ImMessageBodyMapper;
import com.lld.im.service.message.dao.mapper.ImMessageHistoryMapper;
import com.lld.im.service.message.model.req.GetConversationMessagesReq;
import com.lld.im.service.message.model.req.SearchMessageReq;
import com.lld.im.service.message.model.resp.MessageHistoryResp;
import com.lld.im.service.message.model.resp.PageResult;
import com.lld.im.service.message.model.resp.SearchMessageResp;
import com.lld.im.service.message.service.MessageHistoryService;
import com.lld.im.service.message.util.HighlightUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 消息历史记录服务实现
 * @description: 消息历史记录查询功能的具体实现
 * @author: lld
 * @version: 1.0
 */
@Slf4j
@Service
public class MessageHistoryServiceImpl implements MessageHistoryService {

    @Autowired
    private ImMessageHistoryMapper messageHistoryMapper;

    @Autowired
    private ImMessageBodyMapper messageBodyMapper;

    @Autowired
    private ImConversationSetMapper conversationSetMapper;

    @Autowired
    private ImGroupMessageHistoryMapper groupMessageHistoryMapper;

    @Autowired
    private ImGroupMemberMapper groupMemberMapper;

    @Autowired
    private ImGroupMemberService groupMemberService;

    @Override
    public ResponseVO<PageResult<MessageHistoryResp>> getConversationMessages(GetConversationMessagesReq req) {
        try {
            log.info("查询会话历史消息，会话ID：{}，用户ID：{}，页码：{}，每页大小：{}",
                    req.getConversationId(), req.getUserId(), req.getPageNum(), req.getPageSize());

            // 1. 解析会话类型
            Integer conversationType = parseConversationType(req.getConversationId());
            if (conversationType == null) {
                return ResponseVO.errorResponse(400, "会话ID格式错误");
            }

            // 2. 验证会话权限
            ResponseVO<Void> permissionCheck = checkConversationPermission(req, conversationType);
            if (!permissionCheck.isOk()) {
                return ResponseVO.errorResponse(permissionCheck.getCode(), permissionCheck.getMsg());
            }

            // 3. 根据会话类型查询消息历史
            PageResult<MessageHistoryResp> pageResult;
            if (conversationType == ConversationTypeEnum.P2P.getCode()) {
                // 单聊消息查询
                pageResult = queryP2PMessages(req);
            } else if (conversationType == ConversationTypeEnum.GROUP.getCode()) {
                // 群聊消息查询
                pageResult = queryGroupMessages(req);
            } else {
                return ResponseVO.errorResponse(400, "不支持的会话类型");
            }

            log.info("查询会话历史消息完成，总记录数：{}，当前页记录数：{}",
                    pageResult.getTotal(), pageResult.getList().size());

            return ResponseVO.successResponse(pageResult);

        } catch (Exception e) {
            log.error("查询会话历史消息异常，会话ID：{}，用户ID：{}", req.getConversationId(), req.getUserId(), e);
            return ResponseVO.errorResponse(500, "查询历史消息失败");
        }
    }

    /**
     * 解析会话类型
     * @param conversationId 会话ID，格式：0_user1_user2 或 1_user_group
     * @return 会话类型：0-单聊，1-群聊
     */
    private Integer parseConversationType(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return null;
        }

        String[] parts = conversationId.split("_");
        if (parts.length < 3) {
            return null;
        }

        try {
            return Integer.parseInt(parts[0]);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 从扩展字段中解析消息类型
     * @param extra 扩展字段JSON字符串
     * @return 消息类型：1=文本/表情包，2=图片，3=语音，4=视频，5=表情包，默认为1
     */
    private Integer parseMessageTypeFromExtra(String extra) {
        // 如果extra字段为空，返回默认值1（文本消息）
        if (StringUtils.isBlank(extra)) {
            log.debug("extra字段为空，使用默认消息类型：1（文本消息）");
            return 1;
        }

        try {
            // 解析JSON字符串
            JSONObject extraJson = JSONObject.parseObject(extra);

            // 提取messageType字段
            Object messageTypeObj = extraJson.get("messageType");
            if (messageTypeObj == null) {
                log.debug("extra字段中未找到messageType，使用默认值：1（文本消息）");
                return 1;
            }

            // 处理不同类型的messageType值
            Integer messageType;
            if (messageTypeObj instanceof Integer) {
                messageType = (Integer) messageTypeObj;
            } else if (messageTypeObj instanceof String) {
                try {
                    messageType = Integer.parseInt((String) messageTypeObj);
                } catch (NumberFormatException e) {
                    log.warn("messageType字符串格式无效：{}，使用默认值：1", messageTypeObj);
                    return 1;
                }
            } else {
                log.warn("messageType类型不支持：{}，使用默认值：1", messageTypeObj.getClass().getSimpleName());
                return 1;
            }

            // 验证messageType值的有效性
            if (messageType < 1 || messageType > 5) {
                log.warn("messageType值超出有效范围[1-5]：{}，使用默认值：1", messageType);
                return 1;
            }

            log.debug("成功解析messageType：{}", messageType);
            return messageType;

        } catch (Exception e) {
            log.warn("解析extra字段失败，extra={}，使用默认值：1（文本消息）", extra, e);
            return 1;
        }
    }

    /**
     * 验证会话权限
     */
    private ResponseVO<Void> checkConversationPermission(GetConversationMessagesReq req, Integer conversationType) {
        if (conversationType == ConversationTypeEnum.P2P.getCode()) {
            // 单聊权限验证：查询会话是否存在且用户有权限访问
            return checkP2PConversationPermission(req);
        } else if (conversationType == ConversationTypeEnum.GROUP.getCode()) {
            // 群聊权限验证：检查用户是否为群成员
            return checkGroupConversationPermission(req);
        } else {
            return ResponseVO.errorResponse(400, "不支持的会话类型");
        }
    }

    /**
     * 验证搜索消息的会话权限
     */
    private ResponseVO<Void> checkConversationPermission(SearchMessageReq req, Integer conversationType) {
        if (conversationType == ConversationTypeEnum.P2P.getCode()) {
            // 单聊权限验证：查询会话是否存在且用户有权限访问
            return checkP2PConversationPermissionForSearch(req);
        } else if (conversationType == ConversationTypeEnum.GROUP.getCode()) {
            // 群聊权限验证：检查用户是否为群成员
            return checkGroupConversationPermissionForSearch(req);
        } else {
            return ResponseVO.errorResponse(400, "不支持的会话类型");
        }
    }

    /**
     * 验证单聊会话权限
     */
    private ResponseVO<Void> checkP2PConversationPermission(GetConversationMessagesReq req) {
        LambdaQueryWrapper<ImConversationSetEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImConversationSetEntity::getConversationId, req.getConversationId())
               .eq(ImConversationSetEntity::getAppId, req.getAppId())
               .eq(ImConversationSetEntity::getFromId, req.getUserId());

        ImConversationSetEntity conversation = conversationSetMapper.selectOne(wrapper);
        if (conversation == null) {
            log.warn("用户{}无权限访问单聊会话{}或会话不存在", req.getUserId(), req.getConversationId());
            return ResponseVO.errorResponse(403, "无权限访问该会话或会话不存在");
        }

        return ResponseVO.successResponse();
    }

    /**
     * 验证群聊会话权限
     */
    private ResponseVO<Void> checkGroupConversationPermission(GetConversationMessagesReq req) {
        // 解析群组ID
        String[] parts = req.getConversationId().split("_");
        if (parts.length < 3) {
            return ResponseVO.errorResponse(400, "群聊会话ID格式错误");
        }

        String groupId = parts[2];

        // 检查用户是否为群成员
        ResponseVO<GetRoleInGroupResp> roleResp = groupMemberService.getRoleInGroupOne(groupId, req.getUserId(), req.getAppId());
        if (!roleResp.isOk()) {
            log.warn("用户{}不是群{}的成员，无权限访问群聊历史消息", req.getUserId(), groupId);
            return ResponseVO.errorResponse(403, "您不是该群的成员，无权限查看群聊历史消息");
        }

        // 检查用户是否已离开群聊
        GetRoleInGroupResp roleData = roleResp.getData();
        if (roleData.getRole() == GroupMemberRoleEnum.LEAVE.getCode()) {
            log.warn("用户{}已离开群{}，无权限访问群聊历史消息", req.getUserId(), groupId);
            return ResponseVO.errorResponse(403, "您已离开该群，无权限查看群聊历史消息");
        }

        return ResponseVO.successResponse();
    }

    /**
     * 验证搜索消息的单聊会话权限
     */
    private ResponseVO<Void> checkP2PConversationPermissionForSearch(SearchMessageReq req) {
        LambdaQueryWrapper<ImConversationSetEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ImConversationSetEntity::getConversationId, req.getConversationId())
               .eq(ImConversationSetEntity::getAppId, req.getAppId())
               .eq(ImConversationSetEntity::getFromId, req.getUserId());

        ImConversationSetEntity conversation = conversationSetMapper.selectOne(wrapper);
        if (conversation == null) {
            log.warn("用户{}无权限搜索单聊会话{}或会话不存在", req.getUserId(), req.getConversationId());
            return ResponseVO.errorResponse(403, "无权限搜索该会话或会话不存在");
        }

        return ResponseVO.successResponse();
    }

    /**
     * 验证搜索消息的群聊会话权限
     */
    private ResponseVO<Void> checkGroupConversationPermissionForSearch(SearchMessageReq req) {
        // 解析群组ID
        String[] parts = req.getConversationId().split("_");
        if (parts.length < 3) {
            return ResponseVO.errorResponse(400, "群聊会话ID格式错误");
        }

        String groupId = parts[2];

        // 检查用户是否为群成员
        ResponseVO<GetRoleInGroupResp> roleResp = groupMemberService.getRoleInGroupOne(groupId, req.getUserId(), req.getAppId());
        if (!roleResp.isOk()) {
            log.warn("用户{}不是群{}的成员，无权限搜索群聊消息", req.getUserId(), groupId);
            return ResponseVO.errorResponse(403, "您不是该群的成员，无权限搜索群聊消息");
        }

        // 检查用户是否已离开群聊
        GetRoleInGroupResp roleData = roleResp.getData();
        if (roleData.getRole() == GroupMemberRoleEnum.LEAVE.getCode()) {
            log.warn("用户{}已离开群{}，无权限搜索群聊消息", req.getUserId(), groupId);
            return ResponseVO.errorResponse(403, "您已离开该群，无权限搜索群聊消息");
        }

        return ResponseVO.successResponse();
    }

    /**
     * 查询单聊消息历史
     */
    private PageResult<MessageHistoryResp> queryP2PMessages(GetConversationMessagesReq req) {
        // 构建查询条件
        LambdaQueryWrapper<ImMessageHistoryEntity> queryWrapper = buildP2PQueryWrapper(req);

        // 分页查询消息历史
        Page<ImMessageHistoryEntity> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ImMessageHistoryEntity> messageHistoryPage = messageHistoryMapper.selectPage(page, queryWrapper);

        // 查询消息内容并组装结果
        List<MessageHistoryResp> messageList = buildP2PMessageHistoryResp(messageHistoryPage.getRecords(), req);

        // 构建分页结果
        return PageResult.of(
                messageList,
                messageHistoryPage.getTotal(),
                req.getPageNum(),
                req.getPageSize()
        );
    }

    /**
     * 查询群聊消息历史
     */
    private PageResult<MessageHistoryResp> queryGroupMessages(GetConversationMessagesReq req) {
        // 解析群组ID
        String[] parts = req.getConversationId().split("_");
        String groupId = parts[2];

        // 构建查询条件
        LambdaQueryWrapper<ImGroupMessageHistoryEntity> queryWrapper = buildGroupQueryWrapper(req, groupId);

        // 分页查询消息历史
        Page<ImGroupMessageHistoryEntity> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ImGroupMessageHistoryEntity> messageHistoryPage = groupMessageHistoryMapper.selectPage(page, queryWrapper);

        // 查询消息内容并组装结果
        List<MessageHistoryResp> messageList = buildGroupMessageHistoryResp(messageHistoryPage.getRecords(), req);

        // 构建分页结果
        return PageResult.of(
                messageList,
                messageHistoryPage.getTotal(),
                req.getPageNum(),
                req.getPageSize()
        );
    }

    /**
     * 构建单聊查询条件
     */
    private LambdaQueryWrapper<ImMessageHistoryEntity> buildP2PQueryWrapper(GetConversationMessagesReq req) {
        LambdaQueryWrapper<ImMessageHistoryEntity> wrapper = new LambdaQueryWrapper<>();

        // 基础查询条件
        wrapper.eq(ImMessageHistoryEntity::getAppId, req.getAppId())
               .eq(ImMessageHistoryEntity::getOwnerId, req.getUserId());

        // 根据会话ID解析fromId和toId
        String[] userIds = req.getConversationId().split("_");
        if (userIds.length == 2) {
            String user1 = userIds[0];
            String user2 = userIds[1];
            // 查询该用户参与的消息（作为发送者或接收者）
            wrapper.and(w -> w.and(w1 -> w1.eq(ImMessageHistoryEntity::getFromId, user1)
                                        .eq(ImMessageHistoryEntity::getToId, user2))
                             .or(w2 -> w2.eq(ImMessageHistoryEntity::getFromId, user2)
                                        .eq(ImMessageHistoryEntity::getToId, user1)));
        }

        // 时间范围过滤
        if (req.getStartTime() != null) {
            wrapper.ge(ImMessageHistoryEntity::getMessageTime, req.getStartTime());
        }
        if (req.getEndTime() != null) {
            wrapper.le(ImMessageHistoryEntity::getMessageTime, req.getEndTime());
        }

        // 序列号过滤（用于向前翻页）
        if (req.getBeforeSequence() != null) {
            wrapper.lt(ImMessageHistoryEntity::getSequence, req.getBeforeSequence());
        }

        // 排序
        if ("ASC".equalsIgnoreCase(req.getOrderBy())) {
            wrapper.orderByAsc(ImMessageHistoryEntity::getMessageTime, ImMessageHistoryEntity::getSequence);
        } else {
            wrapper.orderByDesc(ImMessageHistoryEntity::getMessageTime, ImMessageHistoryEntity::getSequence);
        }

        return wrapper;
    }

    /**
     * 构建群聊查询条件
     */
    private LambdaQueryWrapper<ImGroupMessageHistoryEntity> buildGroupQueryWrapper(GetConversationMessagesReq req, String groupId) {
        LambdaQueryWrapper<ImGroupMessageHistoryEntity> wrapper = new LambdaQueryWrapper<>();

        // 基础查询条件
        wrapper.eq(ImGroupMessageHistoryEntity::getAppId, req.getAppId())
               .eq(ImGroupMessageHistoryEntity::getGroupId, groupId);

        // 时间范围查询
        if (req.getStartTime() != null) {
            wrapper.ge(ImGroupMessageHistoryEntity::getCreateTime, req.getStartTime());
        }
        if (req.getEndTime() != null) {
            wrapper.le(ImGroupMessageHistoryEntity::getCreateTime, req.getEndTime());
        }

        // 序列号查询（查询指定序列号之前的消息）
        if (req.getBeforeSequence() != null) {
            wrapper.lt(ImGroupMessageHistoryEntity::getSequence, req.getBeforeSequence());
        }

        // 注意：群聊消息历史表没有messageType字段，如果需要按消息类型过滤，需要通过消息体表关联查询
        // 这里暂时跳过消息类型过滤，或者可以在后续处理中过滤

        // 排序
        String orderBy = StringUtils.isNotBlank(req.getOrderBy()) ? req.getOrderBy().toUpperCase() : "DESC";
        if ("ASC".equals(orderBy)) {
            wrapper.orderByAsc(ImGroupMessageHistoryEntity::getSequence);
        } else {
            wrapper.orderByDesc(ImGroupMessageHistoryEntity::getSequence);
        }

        return wrapper;
    }

    /**
     * 构建单聊消息历史响应列表
     */
    private List<MessageHistoryResp> buildP2PMessageHistoryResp(List<ImMessageHistoryEntity> messageHistoryList,
                                                               GetConversationMessagesReq req) {
        if (CollectionUtils.isEmpty(messageHistoryList)) {
            return new ArrayList<>();
        }

        // 批量查询消息内容
        List<Long> messageKeys = messageHistoryList.stream()
                .map(ImMessageHistoryEntity::getMessageKey)
                .collect(Collectors.toList());

        LambdaQueryWrapper<ImMessageBodyEntity> bodyWrapper = new LambdaQueryWrapper<>();
        bodyWrapper.in(ImMessageBodyEntity::getMessageKey, messageKeys)
                   .eq(ImMessageBodyEntity::getAppId, req.getAppId());

        List<ImMessageBodyEntity> messageBodyList = messageBodyMapper.selectList(bodyWrapper);
        // 修复：保留完整的ImMessageBodyEntity对象，而不是只提取messageBody字段
        Map<Long, ImMessageBodyEntity> messageBodyMap = messageBodyList.stream()
                .collect(Collectors.toMap(ImMessageBodyEntity::getMessageKey, body -> body));

        // 组装响应结果
        return messageHistoryList.stream().map(history -> {
            MessageHistoryResp resp = new MessageHistoryResp();
            BeanUtils.copyProperties(history, resp);
            resp.setConversationType(ConversationTypeEnum.P2P.getCode());
            resp.setConversationId(req.getConversationId());

            // 设置消息内容和扩展字段
            ImMessageBodyEntity messageBody = messageBodyMap.get(history.getMessageKey());
            if (messageBody != null) {
                resp.setMessageBody(messageBody.getMessageBody());
                resp.setExtra(messageBody.getExtra()); // 设置extra字段

                // 从extra字段获取messageType
                String messageType = messageBody.getExtra();
                resp.setMessageType(messageType);

                log.debug("单聊消息 messageKey={}, messageType={}, extra={}",
                         history.getMessageKey(), messageType, messageBody.getExtra());
            } else {
                // 如果没有找到消息体，设置默认值
                resp.setMessageType("1");
                resp.setExtra("1");
                log.warn("未找到消息体，messageKey={}", history.getMessageKey());
            }

            return resp;
        }).collect(Collectors.toList());
    }

    /**
     * 构建群聊消息历史响应列表
     */
    private List<MessageHistoryResp> buildGroupMessageHistoryResp(List<ImGroupMessageHistoryEntity> messageHistoryList,
                                                                 GetConversationMessagesReq req) {
        if (CollectionUtils.isEmpty(messageHistoryList)) {
            return new ArrayList<>();
        }

        // 批量查询消息内容
        List<Long> messageKeys = messageHistoryList.stream()
                .map(ImGroupMessageHistoryEntity::getMessageKey)
                .collect(Collectors.toList());

        LambdaQueryWrapper<ImMessageBodyEntity> bodyWrapper = new LambdaQueryWrapper<>();
        bodyWrapper.in(ImMessageBodyEntity::getMessageKey, messageKeys)
                   .eq(ImMessageBodyEntity::getAppId, req.getAppId());

        List<ImMessageBodyEntity> messageBodyList = messageBodyMapper.selectList(bodyWrapper);
        // 修复：保留完整的ImMessageBodyEntity对象，而不是只提取messageBody字段
        Map<Long, ImMessageBodyEntity> messageBodyMap = messageBodyList.stream()
                .collect(Collectors.toMap(ImMessageBodyEntity::getMessageKey, body -> body));

        // 组装响应结果
        return messageHistoryList.stream().map(history -> {
            MessageHistoryResp resp = new MessageHistoryResp();
            // 复制群聊消息字段到响应对象
            resp.setMessageKey(history.getMessageKey());
            resp.setFromId(history.getFromId());
            resp.setToId(history.getGroupId()); // 群聊中toId设置为群组ID
            resp.setMessageTime(history.getMessageTime());
            resp.setCreateTime(history.getCreateTime());
            resp.setSequence(history.getSequence());
            // 群聊消息的messageRandom是String类型，需要转换
            try {
                resp.setMessageRandom(Integer.parseInt(history.getMessageRandom()));
            } catch (NumberFormatException e) {
                resp.setMessageRandom(0); // 默认值
            }
            resp.setConversationId(req.getConversationId());
            resp.setConversationType(ConversationTypeEnum.GROUP.getCode());

            // 设置消息内容和扩展字段
            ImMessageBodyEntity messageBody = messageBodyMap.get(history.getMessageKey());
            if (messageBody != null) {
                resp.setMessageBody(messageBody.getMessageBody());
                resp.setExtra(messageBody.getExtra()); // 设置extra字段

                // 从extra字段获取messageType
                String messageType = messageBody.getExtra();
                resp.setMessageType(messageType);

                log.debug("群聊消息 messageKey={}, messageType={}, extra={}",
                         history.getMessageKey(), messageType, messageBody.getExtra());
            } else {
                // 如果没有找到消息体，设置默认值
                resp.setMessageType("1");
                resp.setExtra("1");
                log.warn("未找到消息体，messageKey={}", history.getMessageKey());
            }

            return resp;
        }).collect(Collectors.toList());
    }



    @Override
    public ResponseVO<List<SearchMessageResp>> searchMessages(SearchMessageReq req) {
        try {
            log.info("开始搜索会话消息，关键词：{}，用户ID：{}，会话ID：{}",
                    req.getKeyword(), req.getUserId(), req.getConversationId());

            // 参数验证
            if (StringUtils.isBlank(req.getKeyword()) || StringUtils.isBlank(req.getUserId())
                    || StringUtils.isBlank(req.getConversationId())) {
                return ResponseVO.errorResponse(400, "关键词、用户ID和会话ID不能为空");
            }

            // 1. 解析会话类型
            Integer conversationType = parseConversationType(req.getConversationId());
            if (conversationType == null) {
                return ResponseVO.errorResponse(400, "会话ID格式错误");
            }

            // 2. 验证会话权限
            ResponseVO<Void> permissionCheck = checkConversationPermission(req, conversationType);
            if (!permissionCheck.isOk()) {
                return ResponseVO.errorResponse(permissionCheck.getCode(), permissionCheck.getMsg());
            }

            // 3. 根据会话类型搜索消息
            List<SearchMessageResp> searchResults;
            if (conversationType == ConversationTypeEnum.P2P.getCode()) {
                // 单聊消息搜索
                searchResults = searchP2PMessages(req);
            } else if (conversationType == ConversationTypeEnum.GROUP.getCode()) {
                // 群聊消息搜索
                searchResults = searchGroupMessages(req);
            } else {
                return ResponseVO.errorResponse(400, "不支持的会话类型");
            }

            log.info("搜索会话消息完成，总记录数：{}", searchResults.size());

            return ResponseVO.successResponse(searchResults);

        } catch (Exception e) {
            log.error("搜索会话消息异常，关键词：{}，用户ID：{}，会话ID：{}",
                    req.getKeyword(), req.getUserId(), req.getConversationId(), e);
            return ResponseVO.errorResponse(500, "搜索消息失败");
        }
    }

    /**
     * 搜索单聊消息
     */
    private List<SearchMessageResp> searchP2PMessages(SearchMessageReq req) {
        // 解析会话ID获取对话双方的用户ID
        String[] parts = req.getConversationId().split("_");
        if (parts.length < 3) {
            return new ArrayList<>();
        }

        String userId1 = parts[1];
        String userId2 = parts[2];

        // 构建消息搜索条件 - 只搜索指定会话的消息
        LambdaQueryWrapper<ImMessageHistoryEntity> messageWrapper = new LambdaQueryWrapper<>();
        messageWrapper.eq(ImMessageHistoryEntity::getAppId, req.getAppId())
                     .eq(ImMessageHistoryEntity::getOwnerId, req.getUserId())
                     .and(wrapper -> wrapper
                         .and(w -> w.eq(ImMessageHistoryEntity::getFromId, userId1)
                                  .eq(ImMessageHistoryEntity::getToId, userId2))
                         .or(w -> w.eq(ImMessageHistoryEntity::getFromId, userId2)
                                 .eq(ImMessageHistoryEntity::getToId, userId1))
                     );

        // 添加时间范围条件
        if (req.getStartTime() != null) {
            messageWrapper.ge(ImMessageHistoryEntity::getCreateTime, req.getStartTime());
        }
        if (req.getEndTime() != null) {
            messageWrapper.le(ImMessageHistoryEntity::getCreateTime, req.getEndTime());
        }

        // 排序
        if ("ASC".equals(req.getOrderBy())) {
            messageWrapper.orderByAsc(ImMessageHistoryEntity::getCreateTime);
        } else {
            messageWrapper.orderByDesc(ImMessageHistoryEntity::getCreateTime);
        }

        // 3. 查询所有消息历史（不分页）
        List<ImMessageHistoryEntity> messageHistoryList = messageHistoryMapper.selectList(messageWrapper);

        // 4. 查询消息内容并过滤匹配关键词的消息
        return buildSearchResults(messageHistoryList, req, ConversationTypeEnum.P2P.getCode());
    }

    /**
     * 搜索群聊消息
     */
    private List<SearchMessageResp> searchGroupMessages(SearchMessageReq req) {
        // 解析会话ID获取群组ID
        String[] parts = req.getConversationId().split("_");
        if (parts.length < 3) {
            return new ArrayList<>();
        }

        String groupId = parts[2];

        // 构建群聊消息搜索条件 - 只搜索指定群组的消息
        LambdaQueryWrapper<ImGroupMessageHistoryEntity> messageWrapper = new LambdaQueryWrapper<>();
        messageWrapper.eq(ImGroupMessageHistoryEntity::getAppId, req.getAppId())
                     .eq(ImGroupMessageHistoryEntity::getGroupId, groupId);

        // 添加时间范围条件
        if (req.getStartTime() != null) {
            messageWrapper.ge(ImGroupMessageHistoryEntity::getCreateTime, req.getStartTime());
        }
        if (req.getEndTime() != null) {
            messageWrapper.le(ImGroupMessageHistoryEntity::getCreateTime, req.getEndTime());
        }

        // 排序
        if ("ASC".equals(req.getOrderBy())) {
            messageWrapper.orderByAsc(ImGroupMessageHistoryEntity::getCreateTime);
        } else {
            messageWrapper.orderByDesc(ImGroupMessageHistoryEntity::getCreateTime);
        }

        // 3. 查询所有群聊消息历史（不分页）
        List<ImGroupMessageHistoryEntity> messageHistoryList = groupMessageHistoryMapper.selectList(messageWrapper);

        // 4. 查询消息内容并过滤匹配关键词的消息
        return buildGroupSearchResults(messageHistoryList, req);
    }



    /**
     * 构建单聊搜索结果
     */
    private List<SearchMessageResp> buildSearchResults(List<ImMessageHistoryEntity> messageHistoryList, SearchMessageReq req, Integer conversationType) {
        if (CollectionUtils.isEmpty(messageHistoryList)) {
            return new ArrayList<>();
        }

        // 批量查询消息内容
        List<Long> messageKeys = messageHistoryList.stream()
                .map(ImMessageHistoryEntity::getMessageKey)
                .collect(Collectors.toList());

        LambdaQueryWrapper<ImMessageBodyEntity> bodyWrapper = new LambdaQueryWrapper<>();
        bodyWrapper.in(ImMessageBodyEntity::getMessageKey, messageKeys)
                   .eq(ImMessageBodyEntity::getAppId, req.getAppId())
                    .eq(ImMessageBodyEntity::getExtra, "1");

        List<ImMessageBodyEntity> messageBodyList = messageBodyMapper.selectList(bodyWrapper);
        Map<Long, ImMessageBodyEntity> messageBodyMap = messageBodyList.stream()
                .collect(Collectors.toMap(ImMessageBodyEntity::getMessageKey, body -> body));

        // 过滤包含关键词的消息并构建结果
        List<SearchMessageResp> results = new ArrayList<>();
        for (ImMessageHistoryEntity history : messageHistoryList) {
            ImMessageBodyEntity messageBody = messageBodyMap.get(history.getMessageKey());
            if (messageBody != null && StringUtils.isNotBlank(messageBody.getMessageBody())) {
                // 检查消息内容是否包含关键词（忽略大小写）
                if (HighlightUtil.containsKeyword(messageBody.getMessageBody(), req.getKeyword())) {
                    SearchMessageResp resp = new SearchMessageResp();

                    // 复制基础字段
                    BeanUtils.copyProperties(history, resp);

                    // 设置原始消息内容
                    resp.setMessageBody(messageBody.getMessageBody());
                    resp.setExtra(messageBody.getExtra());

                    // 设置高亮消息内容
                    if (Boolean.TRUE.equals(req.getEnableHtmlEscape())) {
                        resp.setHighlightedMessageBody(HighlightUtil.highlightTextWithHtmlEscape(
                                messageBody.getMessageBody(), req.getKeyword()));
                    } else {
                        resp.setHighlightedMessageBody(HighlightUtil.highlightText(
                                messageBody.getMessageBody(), req.getKeyword(),
                                req.getHighlightStartTag(), req.getHighlightEndTag()));
                    }

                    // 生成高亮片段列表
                    resp.setHighlightFragments(HighlightUtil.generateHighlightFragments(
                            messageBody.getMessageBody(), req.getKeyword(), req.getFragmentLength()));

                    // 从extra字段中获取消息类型
                    resp.setMessageType(messageBody.getExtra());

                    // 设置会话信息
                    if (conversationType == ConversationTypeEnum.P2P.getCode()) {
                        // 单聊：构建会话ID
                        String conversationId = "0_" + history.getFromId() + "_" + history.getToId();
                        resp.setConversationId(conversationId);
                    }
                    resp.setConversationType(conversationType);

                    results.add(resp);
                }
            }
        }

        return results;
    }

    /**
     * 构建群聊搜索结果
     */
    private List<SearchMessageResp> buildGroupSearchResults(List<ImGroupMessageHistoryEntity> messageHistoryList, SearchMessageReq req) {
        if (CollectionUtils.isEmpty(messageHistoryList)) {
            return new ArrayList<>();
        }

        // 批量查询消息内容
        List<Long> messageKeys = messageHistoryList.stream()
                .map(ImGroupMessageHistoryEntity::getMessageKey)
                .collect(Collectors.toList());

        LambdaQueryWrapper<ImMessageBodyEntity> bodyWrapper = new LambdaQueryWrapper<>();
        bodyWrapper.in(ImMessageBodyEntity::getMessageKey, messageKeys)
                   .eq(ImMessageBodyEntity::getAppId, req.getAppId())
                    .eq(ImMessageBodyEntity::getExtra, "1");

        List<ImMessageBodyEntity> messageBodyList = messageBodyMapper.selectList(bodyWrapper);
        Map<Long, ImMessageBodyEntity> messageBodyMap = messageBodyList.stream()
                .collect(Collectors.toMap(ImMessageBodyEntity::getMessageKey, body -> body));

        // 过滤包含关键词的消息并构建结果
        List<SearchMessageResp> results = new ArrayList<>();
        for (ImGroupMessageHistoryEntity history : messageHistoryList) {
            ImMessageBodyEntity messageBody = messageBodyMap.get(history.getMessageKey());
            if (messageBody != null && StringUtils.isNotBlank(messageBody.getMessageBody())) {
                // 检查消息内容是否包含关键词（忽略大小写）
                if (HighlightUtil.containsKeyword(messageBody.getMessageBody(), req.getKeyword())) {
                    SearchMessageResp resp = new SearchMessageResp();

                    // 复制群聊消息字段到响应对象
                    resp.setMessageKey(history.getMessageKey());
                    resp.setFromId(history.getFromId());
                    resp.setToId(history.getGroupId()); // 群聊中toId设置为群组ID
                    resp.setMessageTime(history.getMessageTime());
                    resp.setCreateTime(history.getCreateTime());
                    resp.setSequence(history.getSequence());

                    // 设置原始消息内容
                    resp.setMessageBody(messageBody.getMessageBody());
                    resp.setExtra(messageBody.getExtra());

                    // 设置高亮消息内容
                    if (Boolean.TRUE.equals(req.getEnableHtmlEscape())) {
                        resp.setHighlightedMessageBody(HighlightUtil.highlightTextWithHtmlEscape(
                                messageBody.getMessageBody(), req.getKeyword()));
                    } else {
                        resp.setHighlightedMessageBody(HighlightUtil.highlightText(
                                messageBody.getMessageBody(), req.getKeyword(),
                                req.getHighlightStartTag(), req.getHighlightEndTag()));
                    }

                    // 生成高亮片段列表
                    resp.setHighlightFragments(HighlightUtil.generateHighlightFragments(
                            messageBody.getMessageBody(), req.getKeyword(), req.getFragmentLength()));

                    // 从extra字段获取messageType
                    resp.setMessageType(messageBody.getExtra());

                    // 设置会话信息
                    String conversationId = "1_" + req.getUserId() + "_" + history.getGroupId();
                    resp.setConversationId(conversationId);
                    resp.setConversationType(ConversationTypeEnum.GROUP.getCode());

                    results.add(resp);
                }
            }
        }

        return results;
    }



}