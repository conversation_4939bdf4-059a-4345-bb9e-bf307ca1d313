package com.lld.im.tcp.utils;

import io.netty.util.AttributeKey;

/**
 * 统一的AttributeKey管理类
 * 确保所有地方都使用相同的AttributeKey实例，避免属性污染
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class AttributeKeys {
    
    // 使用newInstance()创建独立的AttributeKey实例，避免全局污染
    public static final AttributeKey<String> USER_ID = AttributeKey.newInstance("userId");
    public static final AttributeKey<Integer> APP_ID = AttributeKey.newInstance("appId");
    public static final AttributeKey<Integer> CLIENT_TYPE = AttributeKey.newInstance("clientType");
    public static final AttributeKey<String> IMEI = AttributeKey.newInstance("imei");
    public static final AttributeKey<String> CLIENT_IMEI = AttributeKey.newInstance("clientImei");
    public static final AttributeKey<Long> READ_TIME = AttributeKey.newInstance("readTime");
    public static final AttributeKey<Boolean> IS_GUEST = AttributeKey.newInstance("isGuest");
    
    // 私有构造函数，防止实例化
    private AttributeKeys() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
}
