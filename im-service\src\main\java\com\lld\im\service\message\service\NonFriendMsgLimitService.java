package com.lld.im.service.message.service;

import com.alibaba.fastjson.JSON;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.config.AppConfig;
import com.lld.im.common.enums.FriendShipErrorCode;
import com.lld.im.common.enums.FriendShipStatusEnum;
import com.lld.im.service.friendship.dao.ImFriendShipEntity;
import com.lld.im.service.friendship.model.req.GetRelationReq;
import com.lld.im.service.friendship.service.ImFriendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 非好友私信限制服务
 *
 * <AUTHOR>
 */
@Service
public class NonFriendMsgLimitService {

    private static final Logger logger = LoggerFactory.getLogger(NonFriendMsgLimitService.class);

    @Autowired
    private AppConfig appConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ImFriendService imFriendService;

    /**
     * Redis Key前缀
     */
    private static final String NON_FRIEND_MSG_LIMIT_PREFIX = "non_friend_msg_limit:";

    /**
     * 获取对方未回复时的最大消息数
     */
    private int getMaxCountBeforeReply() {
        Integer config = appConfig.getNonFriendMsgMaxCountBeforeReply();
        return (config != null && config > 0) ? config : 1;
    }

    /**
     * 获取对方回复后的最大消息数
     */
    private int getMaxCountAfterReply() {
        Integer config = appConfig.getNonFriendMsgMaxCountAfterReply();
        return (config != null && config > 0) ? config : 3;
    }

    /**
     * 检查非好友私信发送限制
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     * @return 检查结果
     */
    public ResponseVO checkNonFriendMsgLimit(String fromId, String toId, Integer appId) {
        try {
            logger.info("开始检查非好友私信限制: fromId={}, toId={}, appId={}", fromId, toId, appId);

            // 如果功能未开启，直接通过
            if (!appConfig.isEnableNonFriendMsgLimit()) {
                logger.info("非好友私信限制功能未开启，直接通过");
                return ResponseVO.successResponse();
            }

            // 检查是否为好友关系（好友不受限制）
            if (isFriend(fromId, toId, appId)) {
                logger.info("用户为好友关系，跳过私信限制: fromId={}, toId={}", fromId, toId);
                return ResponseVO.successResponse();
            }

            logger.info("用户非好友关系，开始检查私信限制: fromId={}, toId={}", fromId, toId);

            // 检查非好友私信限制
            ResponseVO result = checkEnhancedNonFriendLimit(fromId, toId, appId);
            logger.info("非好友私信限制检查结果: fromId={}, toId={}, result={}", fromId, toId, result.isOk());
            return result;

        } catch (Exception e) {
            logger.error("检查非好友私信限制时发生异常: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
            // 异常情况下允许发送，避免影响正常功能
            return ResponseVO.successResponse();
        }
    }

    /**
     * 记录非好友私信发送
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     */
    public void recordNonFriendMsg(String fromId, String toId, Integer appId) {
        // 如果功能未开启，直接返回
        if (!appConfig.isEnableNonFriendMsgLimit()) {
            return;
        }

        // 检查是否为好友关系
        if (isFriend(fromId, toId, appId)) {
            return;
        }

        // 记录发送状态（增强版）
        recordEnhancedMsgSendStatus(fromId, toId, appId);
    }

    /**
     * 记录对方回复消息（用于重置发送限制）
     *
     * @param fromId 回复方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     */
    public void recordNonFriendReply(String fromId, String toId, Integer appId) {
        // 如果功能未开启，直接返回
        if (!appConfig.isEnableNonFriendMsgLimit()) {
            return;
        }

        // 检查是否为好友关系
        if (isFriend(fromId, toId, appId)) {
            return;
        }

        // 记录回复状态，重置对方的发送限制
        recordReplyStatus(fromId, toId, appId);
    }

    /**
     * 检查两个用户是否为好友
     *
     * @param fromId 用户1
     * @param toId 用户2
     * @param appId 应用ID
     * @return 是否为好友
     */
    private boolean isFriend(String fromId, String toId, Integer appId) {
        try {
            logger.debug("开始检查好友关系: fromId={}, toId={}, appId={}", fromId, toId, appId);

            GetRelationReq fromReq = new GetRelationReq();
            fromReq.setFromId(fromId);
            fromReq.setToId(toId);
            fromReq.setAppId(appId);

            ResponseVO<ImFriendShipEntity> fromRelation = imFriendService.getRelation(fromReq);
            if (!fromRelation.isOk()) {
                logger.debug("查询好友关系失败(from->to): fromId={}, toId={}, code={}, msg={}",
                        fromId, toId, fromRelation.getCode(), fromRelation.getMsg());
                return false;
            }

            GetRelationReq toReq = new GetRelationReq();
            toReq.setFromId(toId);
            toReq.setToId(fromId);
            toReq.setAppId(appId);

            ResponseVO<ImFriendShipEntity> toRelation = imFriendService.getRelation(toReq);
            if (!toRelation.isOk()) {
                logger.debug("查询好友关系失败(to->from): fromId={}, toId={}, code={}, msg={}",
                        toId, fromId, toRelation.getCode(), toRelation.getMsg());
                return false;
            }

            // 任何一方添加了对方为好友就不限制（包括单向好友关系）
            boolean isFriend = fromRelation.getData().getStatus() == FriendShipStatusEnum.FRIEND_STATUS_NORMAL.getCode()
                    || toRelation.getData().getStatus() == FriendShipStatusEnum.FRIEND_STATUS_NORMAL.getCode();

            logger.debug("好友关系检查结果: fromId={}, toId={}, isFriend={} (单向或双向都算好友)", fromId, toId, isFriend);
            return isFriend;

        } catch (Exception e) {
            logger.warn("检查好友关系异常，默认为非好友: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
            return false;
        }
    }
    /**
     * 判断是否为回复消息
     * 只有当对方之前向当前用户发送过消息，且当前用户还没有回复过时，才算是回复消息
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     * @return 是否为回复消息
     */
    public boolean isReplyMessage(String fromId, String toId, Integer appId) {
        try {
            logger.info("开始判断是否为回复消息: fromId={}, toId={}", fromId, toId);

            // 检查对方是否向当前用户发送过消息
            String oppositeLimitKey = buildLimitKey(toId, fromId, appId);
            String oppositeLimitValue = stringRedisTemplate.opsForValue().get(oppositeLimitKey);

            logger.info("检查对方发送记录: oppositeLimitKey={}, hasRecord={}", oppositeLimitKey, oppositeLimitValue != null);

            if (oppositeLimitValue == null) {
                // 对方没有发送过消息，不算回复
                logger.info("对方未发送过消息，不算回复: fromId={}, toId={}", fromId, toId);
                return false;
            }

            // 检查当前用户是否已经回复过对方
            String currentLimitKey = buildLimitKey(fromId, toId, appId);
            String currentLimitValue = stringRedisTemplate.opsForValue().get(currentLimitKey);

            logger.info("检查当前用户发送记录: currentLimitKey={}, hasRecord={}", currentLimitKey, currentLimitValue != null);

            if (currentLimitValue != null) {
                Map<String, Object> currentLimitInfo = JSON.parseObject(currentLimitValue, Map.class);
                Integer currentMsgCount = (Integer) currentLimitInfo.getOrDefault("msgCount", 0);

                if (currentMsgCount > 0) {
                    // 当前用户已经发送过消息，不算首次回复
                    logger.info("用户已发送过消息，不算首次回复: fromId={}, toId={}, msgCount={}",
                            fromId, toId, currentMsgCount);
                    return false;
                }
            }

            // 对方发送过消息，且当前用户是首次回复
            logger.info("检测到首次回复消息: fromId={}, toId={}", fromId, toId);
            return true;

        } catch (Exception e) {
            logger.error("判断回复消息失败: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
            return false;
        }
    }





    /**
     * 增强版非好友私信限制检查
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     * @return 检查结果
     */
    private ResponseVO checkEnhancedNonFriendLimit(String fromId, String toId, Integer appId) {
        String limitKey = buildLimitKey(fromId, toId, appId);

        try {
            // 检查发送方的限制状态
            String limitValue = stringRedisTemplate.opsForValue().get(limitKey);

            Integer msgCount = 0;
            Boolean hasReplied = false;

            if (limitValue != null) {
                Map<String, Object> limitInfo = JSON.parseObject(limitValue, Map.class);
                msgCount = (Integer) limitInfo.getOrDefault("msgCount", 0);
                hasReplied = (Boolean) limitInfo.getOrDefault("hasReplied", false);
                logger.info("从Redis获取限制状态: fromId={}, toId={}, limitKey={}, msgCount={}, hasReplied={}",
                        fromId, toId, limitKey, msgCount, hasReplied);
            } else {
                logger.info("Redis中无限制记录: fromId={}, toId={}, limitKey={}", fromId, toId, limitKey);
            }

            // 根据对方是否回复过来确定限制数量
            int maxAllowedCount;
            if (hasReplied) {
                // 对方已回复过，使用回复后的限制数量
                maxAllowedCount = getMaxCountAfterReply();
            } else {
                // 对方未回复，使用回复前的限制数量
                maxAllowedCount = getMaxCountBeforeReply();
            }

            // 检查即将发送的消息是否会超过限制
            // 关键修复：检查 msgCount + 1 是否超过限制
            int nextMsgCount = msgCount + 1;
            int remainingCount = Math.max(0, maxAllowedCount - msgCount);
            String replyStatus = hasReplied ? "已回复" : "未回复";

            if (nextMsgCount > maxAllowedCount) {
                logger.warn("非好友私信发送超过限制: fromId={}, toId={}, 当前msgCount={}, 即将发送第{}条, 限制={}, 对方回复状态={}",
                        fromId, toId, msgCount, nextMsgCount, maxAllowedCount, replyStatus);

                // 构建限制详情数据
                Map<String, Object> limitDetails = new HashMap<>();
                limitDetails.put("isLimited", true);
                limitDetails.put("msgCount", msgCount);
                limitDetails.put("maxAllowedCount", maxAllowedCount);
                limitDetails.put("remainingCount", 0);
                limitDetails.put("hasReplied", hasReplied);
                return ResponseVO.successResponse(limitDetails);
            }

            // 未达到限制，返回当前状态信息
            Map<String, Object> statusDetails = new HashMap<>();
            statusDetails.put("isLimited", false);
            statusDetails.put("msgCount", msgCount);
            statusDetails.put("maxAllowedCount", maxAllowedCount);
            statusDetails.put("remainingCount", remainingCount);
            statusDetails.put("hasReplied", hasReplied);

            return ResponseVO.successResponse(statusDetails);
        } catch (Exception e) {
            logger.error("检查非好友私信限制失败: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
            // 异常情况下允许发送，避免影响正常功能
            return ResponseVO.successResponse();
        }
    }

    /**
     * 记录增强版消息发送状态
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     */
    private void recordEnhancedMsgSendStatus(String fromId, String toId, Integer appId) {
        String limitKey = buildLimitKey(fromId, toId, appId);

        try {
            String limitValue = stringRedisTemplate.opsForValue().get(limitKey);
            Map<String, Object> limitInfo;

            if (limitValue != null) {
                // 更新现有记录
                limitInfo = JSON.parseObject(limitValue, Map.class);
                Integer msgCount = (Integer) limitInfo.getOrDefault("msgCount", 0);
                limitInfo.put("msgCount", msgCount + 1);
                limitInfo.put("lastMsgTime", System.currentTimeMillis());
                // 保持hasReplied状态不变
            } else {
                // 创建新记录
                limitInfo = new HashMap<>();
                limitInfo.put("firstMsgTime", System.currentTimeMillis());
                limitInfo.put("msgCount", 1);
                limitInfo.put("lastMsgTime", System.currentTimeMillis());
                limitInfo.put("hasReplied", false); // 初始状态：对方未回复
            }

            // 保存到Redis，设置过期时间
            long expireSeconds = appConfig.getNonFriendMsgLimitExpireDays() * 24 * 60 * 60;
            stringRedisTemplate.opsForValue().set(limitKey, JSON.toJSONString(limitInfo),
                    expireSeconds, TimeUnit.SECONDS);

            logger.debug("记录增强版非好友私信发送状态: fromId={}, toId={}, msgCount={}, hasReplied={}",
                    fromId, toId, limitInfo.get("msgCount"), limitInfo.get("hasReplied"));
        } catch (Exception e) {
            logger.error("记录增强版非好友私信发送状态失败: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
        }
    }

    /**
     * 记录回复状态，重置对方的发送限制
     *
     * @param replyFromId 回复方ID
     * @param replyToId 接收回复的用户ID
     * @param appId 应用ID
     */
    private void recordReplyStatus(String replyFromId, String replyToId, Integer appId) {
        // 重置对方（replyToId）向回复方（replyFromId）发送消息的限制
        String targetLimitKey = buildLimitKey(replyToId, replyFromId, appId);

        try {
            String limitValue = stringRedisTemplate.opsForValue().get(targetLimitKey);
            if (limitValue != null) {
                Map<String, Object> limitInfo = JSON.parseObject(limitValue, Map.class);

                // 标记对方已回复，重置消息计数
                limitInfo.put("hasReplied", true);
                limitInfo.put("msgCount", 0); // 重置消息计数
                limitInfo.put("lastReplyTime", System.currentTimeMillis());

                // 保存到Redis，设置过期时间
                long expireSeconds = appConfig.getNonFriendMsgLimitExpireDays() * 24 * 60 * 60;
                stringRedisTemplate.opsForValue().set(targetLimitKey, JSON.toJSONString(limitInfo),
                        expireSeconds, TimeUnit.SECONDS);

                logger.debug("记录回复状态，重置发送限制: 回复方={}, 被重置用户={}, 新状态=已回复",
                        replyFromId, replyToId);
            }

            // 注意：不在这里记录回复方的发送状态，因为这会在主流程中通过recordNonFriendMsg完成

        } catch (Exception e) {
            logger.error("记录回复状态失败: replyFromId={}, replyToId={}, appId={}", replyFromId, replyToId, appId, e);
        }
    }

    /**
     * 构建Redis Key
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     * @return Redis Key
     */
    private String buildLimitKey(String fromId, String toId, Integer appId) {
        return NON_FRIEND_MSG_LIMIT_PREFIX + appId + ":" + fromId + ":" + toId;
    }

    /**
     * 清除非好友私信限制记录（用于测试或管理）
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     */
    public void clearNonFriendMsgLimit(String fromId, String toId, Integer appId) {
        String limitKey = buildLimitKey(fromId, toId, appId);
        try {
            stringRedisTemplate.delete(limitKey);
            logger.info("清除非好友私信限制记录: fromId={}, toId={}, appId={}", fromId, toId, appId);
        } catch (Exception e) {
            logger.error("清除非好友私信限制记录失败: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
        }
    }

    /**
     * 获取非好友私信限制状态（用于调试或管理）
     *
     * @param fromId 发送方ID
     * @param toId 接收方ID
     * @param appId 应用ID
     * @return 限制状态信息
     */
    public Map<String, Object> getNonFriendMsgLimitStatus(String fromId, String toId, Integer appId) {
        String limitKey = buildLimitKey(fromId, toId, appId);
        try {
            String limitValue = stringRedisTemplate.opsForValue().get(limitKey);
            if (limitValue != null) {
                return JSON.parseObject(limitValue, Map.class);
            }
        } catch (Exception e) {
            logger.error("获取非好友私信限制状态失败: fromId={}, toId={}, appId={}", fromId, toId, appId, e);
        }
        return null;
    }
}
