package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量群成员禁言请求
 */
@ApiModel(description = "批量群成员禁言请求模型")
@Data
public class BatchSpeakMemberReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要设置成员禁言的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "成员用户ID列表", required = true, example = "[\"user456\", \"user789\"]", notes = "要禁言的群成员用户ID列表")
    @NotEmpty(message = "{validation.member.ids.not.empty}")
    private List<String> memberIds;

    @ApiModelProperty(value = "禁言时间", required = true, example = "1640995200000", notes = "禁言截止时间戳，单位毫秒，0表示取消禁言")
    @NotNull(message = "{validation.speak.date.not.null}")
    private Long speakDate;

}
