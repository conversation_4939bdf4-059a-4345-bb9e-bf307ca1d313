package com.lld.im.service.message.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 查询会话历史消息请求
 * @description: 根据会话ID分页查询历史消息的请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "查询会话历史消息请求模型")
@Data
public class GetConversationMessagesReq extends RequestBase {

    @ApiModelProperty(value = "会话ID", required = true, example = "user1|user2", notes = "会话的唯一标识")
    @NotBlank(message = "会话ID不能为空")
    private String conversationId;

    @ApiModelProperty(value = "查询用户ID", required = true, example = "user123", notes = "发起查询的用户ID，用于权限验证")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "页码", example = "1", notes = "查询页码，从1开始")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "20", notes = "每页返回的消息数量，最大100")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "开始时间", example = "1640995200000", notes = "查询开始时间戳，单位毫秒（可选）")
    private Long startTime;

    @ApiModelProperty(value = "结束时间", example = "1640995200000", notes = "查询结束时间戳，单位毫秒（可选）")
    private Long endTime;

    @ApiModelProperty(value = "基准序列号", example = "1000", notes = "查询指定序列号之前的消息（可选），用于向前翻页")
    private Long beforeSequence;

    @ApiModelProperty(value = "排序方式", example = "DESC", notes = "排序方式：ASC-升序，DESC-降序，默认降序（最新消息在前）")
    private String orderBy = "DESC";

    @ApiModelProperty(value = "消息类型过滤", example = "1", notes = "过滤特定类型的消息（可选）：1-文本，2-图片，3-语音，4-视频等")
    private Integer messageType;

}