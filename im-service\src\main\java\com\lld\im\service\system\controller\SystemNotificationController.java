package com.lld.im.service.system.controller;

import com.lld.im.common.BaseErrorCode;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.interceptor.UserPermissionCheck;
import com.lld.im.service.system.model.req.SystemNotification;
import com.lld.im.service.system.model.resp.NotificationDetailResp;
import com.lld.im.service.system.model.resp.NotificationTypeLatestResp;
import com.lld.im.service.system.service.SystemNotificationService;
import com.lld.im.service.message.model.resp.PageResult;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 系统通知控制器
 */
@Api(tags = "系统通知管理", description = "系统通知的发送、查询和管理相关接口")
@RestController
@RequestMapping("/v1/system")
public class SystemNotificationController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SystemNotificationController.class);

    @Autowired
    private SystemNotificationService systemNotificationService;

    @ApiOperation(value = "发送系统通知", notes = "向指定用户或全体用户发送系统通知消息")
    @PostMapping("/notification/send")
    public ResponseVO sendSystemNotification(
            @ApiParam(value = "系统通知内容", required = true) @RequestBody SystemNotification notification) {
        Integer appId = getCurrentAppId();
        systemNotificationService.sendSystemNotification(appId, notification);
        return ResponseVO.successResponse();
    }

    @ApiOperation(value = "发送系统通知给普通用户（不包括游客）",
                  notes = "发送系统通知给普通用户（不包括游客）。支持两种模式：1）指定receiverIds进行精准推送；2）不指定receiverIds进行广播推送给所有普通用户")
    @PostMapping("/notification/sendToNormalUsers")
    public ResponseVO sendToNormalUsers(
            @ApiParam(value = "系统通知内容", required = true) @RequestBody SystemNotification notification) {
        Integer appId = getCurrentAppId();
        systemNotificationService.sendSystemNotificationToNormalUsers(appId, notification);
        return ResponseVO.successResponse();
    }


    @ApiOperation(value = "获取用户未读通知列表", notes = "分页获取指定用户的未读系统通知列表")
    @GetMapping("/notification/unread")
    @UserPermissionCheck("userId")
    public ResponseVO getUnreadNotifications(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId,
            @ApiParam(value = "上次同步的序列号", defaultValue = "0", example = "100") @RequestParam(value = "lastSequence", defaultValue = "0") Long lastSequence,
            @ApiParam(value = "每页数量限制", defaultValue = "20", example = "20") @RequestParam(value = "limit", defaultValue = "20") Integer limit) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.getUnreadNotifications(userId, appId, lastSequence, limit);
    }

    @ApiOperation(value = "同步系统通知列表", notes = "增量同步用户的系统通知数据，与会话同步保持一致的接口设计")
    @PostMapping("/notification/sync")
    public ResponseVO syncSystemNotifications(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req) {
        fillCommonParams(req);
        return systemNotificationService.syncSystemNotifications(req);
    }

    @ApiOperation(value = "标记通知已读", notes = "将指定的系统通知标记为已读状态")
    @PostMapping("/notification/read")
    @UserPermissionCheck("userId")
    public ResponseVO markNotificationRead(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId,
            @ApiParam(value = "通知ID列表", required = true) @RequestBody List<Long> notificationIds) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.markNotificationRead(userId, appId, notificationIds);
    }

    @ApiOperation(value = "获取未读通知数量", notes = "获取指定用户的未读系统通知总数量")
    @GetMapping("/notification/unread/count")
    @UserPermissionCheck("userId")
    public ResponseVO getUnreadCount(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.getUnreadCount(userId, appId);
    }

    @ApiOperation(value = "获取用户所有通知列表", notes = "分页获取指定用户的所有系统通知列表（包含已读和未读），按创建时间倒序排列")
    @GetMapping("/notification/all")
    @UserPermissionCheck("userId")
    public ResponseVO getAllNotifications(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId,
            @ApiParam(value = "页码", defaultValue = "1", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "20", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.getAllNotifications(userId, appId, pageNum, pageSize);
    }

    @ApiOperation(value = "获取按通知类型分组的最新通知", notes = "返回每种通知类型的最新一条记录，包含未读数量统计")
    @GetMapping("/notification/latest-by-type")
    @UserPermissionCheck("userId")
    public ResponseVO<List<NotificationTypeLatestResp>> getLatestNotificationsByType(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.getLatestNotificationsByType(userId, appId);
    }



    @ApiOperation(value = "按通知类型分页查询通知列表", notes = "分页获取指定用户指定类型的系统通知列表，按创建时间倒序排列")
    @GetMapping("/notification/by-type")
    @UserPermissionCheck("userId")
    public ResponseVO<PageResult<NotificationDetailResp>> getNotificationsByType(
            @ApiParam(value = "用户ID", required = true, example = "user123") @RequestParam("userId") String userId,
            @ApiParam(value = "通知类型", required = true, example = "1") @RequestParam("notificationType") Integer notificationType,
            @ApiParam(value = "页码", defaultValue = "1", example = "1") @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", defaultValue = "20", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {
        Integer appId = getCurrentAppId();
        return systemNotificationService.getNotificationsByType(userId, appId, notificationType, pageNum, pageSize);
    }

    @ApiOperation(value = "一键全部已读", notes = "将当前用户的所有未读系统通知标记为已读状态")
    @PutMapping("/notification/mark-all-read")
    public ResponseVO<Integer> markAllNotificationsRead() {
        Integer appId = getCurrentAppId();
        String userId = getCurrentIdentifier();
        return systemNotificationService.markAllNotificationsRead(userId, appId);
    }

    @ApiOperation(value = "分类已读", notes = "按通知类型批量标记已读，只处理指定类型的未读通知")
    @PutMapping("/notification/mark-read-by-type")
    public ResponseVO<Integer> markNotificationsByTypeRead(
            @ApiParam(value = "通知类型", required = true, example = "1") @RequestParam("notificationType") Integer notificationType) {
        Integer appId = getCurrentAppId();
        String userId = getCurrentIdentifier();
        return systemNotificationService.markNotificationsByTypeRead(userId, appId, notificationType);
    }
}