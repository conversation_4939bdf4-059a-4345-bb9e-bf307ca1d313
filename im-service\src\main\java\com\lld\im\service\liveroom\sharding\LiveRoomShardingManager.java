package com.lld.im.service.liveroom.sharding;

import com.lld.im.common.constant.Constants;
import com.lld.im.service.liveroom.dao.mapper.LiveRoomMapper;
import com.lld.im.service.liveroom.model.entity.LiveRoom;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 直播间分片管理器
 * 负责根据配置计算直播间分片
 */
@Component
public class LiveRoomShardingManager {

    @Autowired
    private LiveRoomShardingConfig shardingConfig;

    @Autowired
    private LiveRoomMapper liveRoomMapper;

    /**
     * 获取直播间所在的分片ID
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 分片ID
     */
    public int getShardId(String roomId, Integer appId) {
        // 如果未启用分片，直接返回0
        if (!shardingConfig.getEnabled()) {
            return 0;
        }

        int shardCount = shardingConfig.getShardCount();
        LiveRoomShardingConfig.ShardingStrategy strategy = shardingConfig.getShardingStrategyEnum();

        switch (strategy) {
            case HEAT:
                // 获取直播间信息，用于热度分片
                LiveRoom liveRoom = liveRoomMapper.selectByRoomId(roomId, appId);
                int onlineCount = liveRoom != null ? liveRoom.getMaxOnlineCount() : 0;
                return LiveRoomShardingUtil.getShardIdByHeat(roomId, onlineCount, shardCount);
            case RANGE:
                return LiveRoomShardingUtil.getShardIdByRange(roomId, shardCount);
            case ROOM_ID:
            default:
                return LiveRoomShardingUtil.getShardIdByRoomId(roomId, shardCount);
        }
    }

    /**
     * 获取直播间消息的路由键
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 路由键
     */
    public String getLiveRoomRoutingKey(String roomId, Integer appId) {
        int shardId = getShardId(roomId, appId);
        return Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + shardId;
    }

    /**
     * 获取直播间消息队列名称
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 队列名称
     */
    public String getLiveRoomQueueName(String roomId, Integer appId) {
        int shardId = getShardId(roomId, appId);
        return Constants.RabbitConstants.LiveRoomQueue + "." + shardId;
    }
} 