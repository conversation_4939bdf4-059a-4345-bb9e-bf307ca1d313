package com.lld.im.mq.starter.service;

import com.lld.im.mq.starter.model.ImLiveRoomMessage;
import com.lld.im.mq.starter.model.ImSystemNotificationMessage;

import java.util.List;

/**
 * IM消息发送接口
 * 
 * <AUTHOR>
 */
public interface ImMessageSender {
    
    /**
     * 发送系统通知给所有用户
     * 
     * @param message 系统通知消息
     */
    void sendSystemNotification(ImSystemNotificationMessage message);
    
    /**
     * 发送系统通知给普通用户
     * 
     * @param message 系统通知消息
     */
    void sendSystemNotificationToNormalUsers(ImSystemNotificationMessage message);
    
    /**
     * 发送系统通知给游客用户
     * 
     * @param message 系统通知消息
     */
    void sendSystemNotificationToGuests(ImSystemNotificationMessage message);
    
    /**
     * 发送直播间消息
     * 
     * @param message 直播间消息
     */
    void sendLiveRoomMessage(ImLiveRoomMessage message);
    
    /**
     * 批量发送系统通知
     * 
     * @param messages 系统通知消息列表
     */
    void sendBatchSystemNotification(List<ImSystemNotificationMessage> messages);
    
    /**
     * 批量发送直播间消息
     * 
     * @param messages 直播间消息列表
     */
    void sendBatchLiveRoomMessage(List<ImLiveRoomMessage> messages);
}
