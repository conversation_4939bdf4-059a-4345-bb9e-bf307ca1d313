package com.lld.im.service.group.model;

import com.lld.im.common.enums.GroupMemberRoleEnum;
import com.lld.im.common.enums.GroupSpeakPermissionEnum;
import lombok.Data;

import java.util.List;

/**
 * 群组发言权限配置模型
 * @author: lld
 * @description: 群组发言权限的配置信息
 */
@Data
public class GroupSpeakPermissionConfig {
    
    /**
     * 权限类型
     */
    private GroupSpeakPermissionEnum type = GroupSpeakPermissionEnum.ALL_MEMBERS;
    
    /**
     * 允许发言的成员列表（当type为SPECIFIED_MEMBERS时使用）
     */
    private List<String> allowedMembers;
    
    /**
     * 权限设置时间
     */
    private Long updateTime;
    
    /**
     * 权限设置者
     */
    private String updatedBy;
    
    /**
     * 检查用户是否有发言权限
     * @param userId 用户ID
     * @param memberRole 用户在群组中的角色
     * @return 是否有发言权限
     */
    public boolean hasPermission(String userId, Integer memberRole) {
        if (userId == null || memberRole == null) {
            return false;
        }
        
        switch (type) {
            case ALL_MEMBERS:
                return true;
            case MANAGERS_ONLY:
                return isManagerOrOwner(memberRole);
            case SPECIFIED_MEMBERS:
                return allowedMembers != null && allowedMembers.contains(userId);
            case NOBODY:
                return false;
            default:
                return true; // 默认允许
        }
    }
    
    /**
     * 判断是否为管理员或群主
     * @param role 用户角色
     * @return 是否为管理员或群主
     */
    private boolean isManagerOrOwner(Integer role) {
        return role != null && 
               (role.equals(GroupMemberRoleEnum.MAMAGER.getCode()) || 
                role.equals(GroupMemberRoleEnum.OWNER.getCode()));
    }
    
    /**
     * 验证配置是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        if (type == null) {
            return false;
        }
        
        // 如果是指定成员模式，必须有成员列表且不为空
        if (type == GroupSpeakPermissionEnum.SPECIFIED_MEMBERS) {
            return allowedMembers != null && !allowedMembers.isEmpty();
        }
        
        return true;
    }
    
    /**
     * 创建默认配置
     * @return 默认的权限配置
     */
    public static GroupSpeakPermissionConfig createDefault() {
        GroupSpeakPermissionConfig config = new GroupSpeakPermissionConfig();
        config.setType(GroupSpeakPermissionEnum.ALL_MEMBERS);
        config.setUpdateTime(System.currentTimeMillis());
        return config;
    }
}
