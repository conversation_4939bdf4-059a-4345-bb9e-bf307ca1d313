lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  redis:
    mode: single # 单机模式：single 哨兵模式：sentinel 集群模式：cluster
    database: 0
    password: 123456
    timeout: 3000 # 超时时间
    poolMinIdle: 8 #最小空闲数
    poolConnTimeout: 3000 # 连接超时时间(毫秒)
    poolSize: 10 # 连接池大小

    # 单机模式配置
    single:
      address: 127.0.0.1:6379

    # 集群模式配置（当mode=cluster时使用）
    # cluster:
    #   nodes:
    #     - ************:7000
    #     - ************:7001
    #     - ************:7000
    #     - ************:7001
    #     - ************:7000
    #     - ************:7001
    #   maxRedirects: 3
    #   scanInterval: 1000

    # 哨兵模式配置（当mode=sentinel时使用）
    # sentinel:
    #   masterName: mymaster
    #   sentinels:
    #     - ************:26379
    #     - ************:26379
    #     - ************:26379
    #   failoverTimeout: 2000
  rabbitmq:
    # 单机模式配置（向后兼容）
    host: 127.0.0.1
    port: 5672
    # 集群模式配置（如果配置了addresses，将使用集群模式）
    # addresses:
    #   - host: rabbit1
    #     port: 5672
    #   - host: rabbit2
    #     port: 5672
    #   - host: rabbit3
    #     port: 5672
    virtualHost: /
    userName: guest
    password: guest
    connectionTimeout: 5000
    requestedHeartbeat: 30
    networkRecoveryInterval: 5000
    automaticRecoveryEnabled: true

  nacosConfig:
    serverAddr: s12:8848
    namespace: im-system
    group: DEFAULT_GROUP
    username: nacos
    password: nacos
    connectTimeout: 3000
    readTimeout: 5000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId