package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 导入群成员请求
 */
@ApiModel(description = "导入群成员请求模型")
@Data
public class ImportGroupMemberReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要导入成员的群组唯一标识")
    @NotBlank(message = "群id不能为空")
    private String groupId;

    @ApiModelProperty(value = "群成员列表", notes = "要导入到群组的成员信息列表")
    private List<GroupMemberDto> members;

}
