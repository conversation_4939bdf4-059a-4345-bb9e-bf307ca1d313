package com.lld.im.codec.pack.system;

import lombok.Data;
import java.util.List;
import java.util.Map;

/**
 * 系统通知消息包
 */
@Data
public class SystemNotificationPack {

    /**
     * 通知ID
     */
    private Long notificationId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 通知类型
     */
    private Integer notificationType;

    /**
     * 扩展字段
     */
    private Map<String, Object> extra;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 接收者ID列表（用于精准推送）
     */
    private List<String> receiverIds;

    /**
     * 排除用户ID列表（用于排除指定用户）
     */
    private List<String> excludeUserIds;
}