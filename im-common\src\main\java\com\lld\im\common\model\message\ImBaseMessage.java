package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * IM消息基础模型
 *
 * <AUTHOR>
 */
@ApiModel(description = "IM消息基础模型")
@Data
public class ImBaseMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消息ID", example = "msg_123456", notes = "消息的唯一标识，如果不设置会自动生成UUID")
    private String messageId;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "时间戳", example = "1640995200000", notes = "消息时间戳，单位毫秒，如果不设置会自动生成当前时间")
    private Long timestamp;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\", \"priority\": \"high\"}", notes = "用于传递额外的业务数据")
    private Map<String, Object> extra;
}
