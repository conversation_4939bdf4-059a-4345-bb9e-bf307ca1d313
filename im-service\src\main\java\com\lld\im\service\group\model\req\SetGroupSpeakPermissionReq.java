package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 设置群组发言权限请求
 * @author: lld
 * @description: 设置群组发言权限的请求模型
 */
@ApiModel(description = "设置群组发言权限请求模型")
@Data
public class SetGroupSpeakPermissionReq extends RequestBase {
    
    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要设置权限的群组ID")
    @NotBlank(message = "群组ID不能为空")
    private String groupId;
    
    @ApiModelProperty(value = "权限类型", required = true, example = "1", 
                     notes = "0-所有人可发言 1-仅群主/管理员可发言 2-指定成员可发言 3-禁止发言")
    @NotNull(message = "权限类型不能为空")
    private Integer permissionType;
    
    @ApiModelProperty(value = "允许发言的成员列表", example = "[\"user123\", \"user456\"]", 
                     notes = "当权限类型为2时必填，指定允许发言的成员用户ID列表")
    private List<String> allowedMembers;

}
