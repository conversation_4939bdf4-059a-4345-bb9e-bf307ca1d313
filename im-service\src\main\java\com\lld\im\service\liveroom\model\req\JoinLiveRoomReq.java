package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 加入直播间请求
 */
@ApiModel(description = "加入直播间请求模型")
@Data
public class JoinLiveRoomReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要加入的直播间唯一标识")
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "加入直播间的用户唯一标识")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "用户昵称", example = "张三", notes = "用户在直播间显示的昵称")
    private String nickname;

    @ApiModelProperty(value = "用户头像URL", example = "https://example.com/avatar.jpg", notes = "用户头像图片地址")
    private String avatar;

    @ApiModelProperty(value = "用户角色", example = "4", notes = "1-主播 2-管理员 3-VIP用户 4-普通用户 5-游客")
    private Integer role;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    @NotNull(message = "应用ID不能为空")
    private Integer appId;

    private Boolean isGuest;
}