package com.lld.im.tcp.utils;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.pack.user.UserStatusChangeNotifyPack;
import com.lld.im.codec.proto.MessageHeader;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ImConnectStatusEnum;
import com.lld.im.common.enums.command.UserEventCommand;
import com.lld.im.common.model.UserClientDto;
import com.lld.im.common.model.UserSession;
import com.lld.im.tcp.publish.MqMessageProducer;
import com.lld.im.tcp.redis.RedisManager;
import io.netty.channel.socket.nio.NioSocketChannel;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.TimeUnit;

import java.util.concurrent.ConcurrentHashMap;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public class SessionSocketHolder {

    private static final Map<UserClientDto, NioSocketChannel> CHANNELS = new ConcurrentHashMap<>();
    private static final Map<String, NioSocketChannel> GUEST_CHANNELS = new ConcurrentHashMap<>();
    private static final Logger logger = LoggerFactory.getLogger(SessionSocketHolder.class);
    
    // 直播间用户映射表 roomId -> Set<userId>
    private static final Map<String, Set<String>> LIVE_ROOM_USERS = new ConcurrentHashMap<>();
    
    // 直播间游客映射表 roomId -> Set<userId>
    private static final Map<String, Set<String>> LIVE_ROOM_GUEST_USERS = new ConcurrentHashMap<>();

    public static void put(Integer appId,String userId,Integer clientType,
                           String imei
            ,NioSocketChannel channel){
        UserClientDto dto = new UserClientDto();
        dto.setImei(imei);
        dto.setAppId(appId);
        dto.setClientType(clientType);
        dto.setUserId(userId);
        CHANNELS.put(dto,channel);
    }

    public static NioSocketChannel get(Integer appId,String userId,
                                       Integer clientType,String imei){
        UserClientDto dto = new UserClientDto();
        dto.setImei(imei);
        dto.setAppId(appId);
        dto.setClientType(clientType);
        dto.setUserId(userId);
        return CHANNELS.get(dto);
    }

    public static List<NioSocketChannel> get(Integer appId , String id) {
        logger.debug("🔍 SessionSocketHolder查找用户Channel: appId={}, userId={}", appId, id);

        List<NioSocketChannel> channels = new ArrayList<>();

        // 检查是否为游客用户
        if (isUserGuest(appId, id)) {
            logger.debug("🎭 检测到游客用户，查找游客Channel: userId={}", id);

            // 查找游客Channel
            NioSocketChannel guestChannel = getGuestChannel(appId, id);
            if (guestChannel != null && guestChannel.isActive()) {
                channels.add(guestChannel);
                logger.debug("✅ 找到活跃游客Channel: userId={}, channel={}", id, guestChannel.id());
            } else {
                // 清理无效的游客Channel
                if (guestChannel != null) {
                    removeGuestChannel(guestChannel);
                }
            }
        } else {
            logger.debug("👤 检测到正式用户，查找正式用户Channel: userId={}", id);

            // 查找正式用户Channel
            Set<UserClientDto> channelInfos = CHANNELS.keySet();
            Set<UserClientDto> toRemove = new HashSet<>();

            channelInfos.forEach(channel ->{
                if(channel.getAppId().equals(appId) && id != null && id.equals(channel.getUserId())){
                    NioSocketChannel nioChannel = CHANNELS.get(channel);
                    if (nioChannel != null && nioChannel.isActive()) {
                        channels.add(nioChannel);
                        logger.debug("✅ 找到活跃正式用户Channel: userId={}, clientType={}, imei={}, channel={}",
                                 id, channel.getClientType(), channel.getImei(), nioChannel.id());
                    } else {
                        // 标记需要清理的无效Channel
                        toRemove.add(channel);
                    }
                }
            });

            // 清理无效的Channel
            if (!toRemove.isEmpty()) {
                logger.debug("🧹 清理无效正式用户Channel: userId={}, 清理数量={}", id, toRemove.size());
                toRemove.forEach(CHANNELS::remove);
            }
        }

        return channels;
    }

    public static void remove(Integer appId,String userId,Integer clientType,String imei){
        UserClientDto dto = new UserClientDto();
        dto.setAppId(appId);
        dto.setImei(imei);
        dto.setClientType(clientType);
        dto.setUserId(userId);
        CHANNELS.remove(dto);
    }



    public static void removeUserSession(NioSocketChannel nioSocketChannel){
        String userId = nioSocketChannel.attr(AttributeKeys.USER_ID).get();
        Integer appId = nioSocketChannel.attr(AttributeKeys.APP_ID).get();
        Integer clientType = nioSocketChannel.attr(AttributeKeys.CLIENT_TYPE).get();
        String imei = nioSocketChannel.attr(AttributeKeys.IMEI).get();

        SessionSocketHolder.remove(appId,userId,clientType,imei);
        RedissonClient redissonClient = RedisManager.getRedissonClient();
        RMap<Object, Object> map = redissonClient.getMap(appId +
                Constants.RedisConstants.UserSessionConstants + userId);
        map.remove(clientType+":"+imei);

        MessageHeader messageHeader = new MessageHeader();
        messageHeader.setAppId(appId);
        messageHeader.setImei(imei);
        messageHeader.setClientType(clientType);

        UserStatusChangeNotifyPack userStatusChangeNotifyPack = new UserStatusChangeNotifyPack();
        userStatusChangeNotifyPack.setAppId(appId);
        userStatusChangeNotifyPack.setUserId(userId);
        userStatusChangeNotifyPack.setStatus(ImConnectStatusEnum.OFFLINE_STATUS.getCode());
        MqMessageProducer.sendMessage(userStatusChangeNotifyPack,messageHeader, UserEventCommand.USER_ONLINE_STATUS_CHANGE.getCommand());

        // 用户断开连接时，从所有直播间中移除
        if (appId != null) {
            removeUserFromAllRooms(appId, userId);
        } else {
            removeUserFromAllRooms(userId); // 降级使用废弃方法
        }

        nioSocketChannel.close();
    }

    public static void offlineUserSession(NioSocketChannel nioSocketChannel){
        String userId = nioSocketChannel.attr(AttributeKeys.USER_ID).get();
        Integer appId = nioSocketChannel.attr(AttributeKeys.APP_ID).get();
        Integer clientType = nioSocketChannel.attr(AttributeKeys.CLIENT_TYPE).get();
        String imei = nioSocketChannel.attr(AttributeKeys.IMEI).get();
        SessionSocketHolder.remove(appId,userId,clientType,imei);
        RedissonClient redissonClient = RedisManager.getRedissonClient();
        RMap<String, String> map = redissonClient.getMap(appId +
                Constants.RedisConstants.UserSessionConstants + userId);
        String sessionStr = map.get(clientType.toString()+":" + imei);

        if(!StringUtils.isBlank(sessionStr)){
            UserSession userSession = JSONObject.parseObject(sessionStr, UserSession.class);
            userSession.setConnectState(ImConnectStatusEnum.OFFLINE_STATUS.getCode());
            map.put(clientType.toString()+":"+imei,JSONObject.toJSONString(userSession));
        }

        MessageHeader messageHeader = new MessageHeader();
        messageHeader.setAppId(appId);
        messageHeader.setImei(imei);
        messageHeader.setClientType(clientType);

        UserStatusChangeNotifyPack userStatusChangeNotifyPack = new UserStatusChangeNotifyPack();
        userStatusChangeNotifyPack.setAppId(appId);
        userStatusChangeNotifyPack.setUserId(userId);
        userStatusChangeNotifyPack.setStatus(ImConnectStatusEnum.OFFLINE_STATUS.getCode());
        MqMessageProducer.sendMessage(userStatusChangeNotifyPack,messageHeader, UserEventCommand.USER_ONLINE_STATUS_CHANGE.getCommand());

        // 用户离线时，从所有直播间中移除
       /* if (appId != null) {
            removeUserFromAllRooms(appId, userId);
        } else {
            removeUserFromAllRooms(userId); // 降级使用废弃方法
        }*/

        nioSocketChannel.close();
    }

    /**
     * 保存游客Channel连接
     * @param appId 应用ID
     * @param guestId 游客ID
     * @param channel 连接通道
     */
    public static void putGuestChannel(Integer appId, String guestId, NioSocketChannel channel) {
        String key = appId + ":" + guestId;
        GUEST_CHANNELS.put(key, channel);
        logger.info("游客连接已保存: {}", key);
    }

    /**
     * 获取游客Channel连接
     * @param appId 应用ID
     * @param guestId 游客ID
     * @return 连接通道
     */
    public static NioSocketChannel getGuestChannel(Integer appId, String guestId) {
        String key = appId + ":" + guestId;
        return GUEST_CHANNELS.get(key);
    }

    /**
     * 移除游客Channel连接
     * @param channel 连接通道
     */
    public static void removeGuestChannel(NioSocketChannel channel) {
        for (Map.Entry<String, NioSocketChannel> entry : GUEST_CHANNELS.entrySet()) {
            if (entry.getValue() == channel) {
                logger.info("游客连接已移除: {}", entry.getKey());
                GUEST_CHANNELS.remove(entry.getKey());
                break;
            }
        }
        channel.close();
    }

    /**
     * 根据用户ID移除游客Channel连接
     * @param userId 用户ID
     * @param appId 应用ID
     */
    public static void removeGuestChannelByUserId(String userId, Integer appId) {
        if (StringUtils.isEmpty(userId) || appId == null) {
            return;
        }

        // 构建与存储时相同的key格式
        String key = appId + ":" + userId;

        // 查找并移除该用户的游客连接
        NioSocketChannel removedChannel = GUEST_CHANNELS.remove(key);
        if (removedChannel != null) {
            logger.debug("🧹 根据用户ID移除游客连接: key={}", key);
        }
    }

    /**
     * 获取所有普通用户通道
     * @return 所有普通用户通道的集合
     */
    public static Collection<NioSocketChannel> getAllChannels() {
        return CHANNELS.values();
    }

    /**
     * 获取所有游客通道连接
     * @return 所有游客通道的集合
     */
    public static Collection<NioSocketChannel> getAllGuestChannels() {
        return GUEST_CHANNELS.values();
    }

    /**
     * 将用户添加到直播间
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param isGuest 是否为游客
     */
    public static void addUserToRoom(String roomId, String userId, boolean isGuest) {
        if (StringUtils.isEmpty(roomId) || StringUtils.isEmpty(userId)) {
            return;
        }
        
        try {
            // 使用Redis存储直播间用户关系
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            
            String redisKey;
            Map<String, Set<String>> targetMap;
            
            if (isGuest) {
                // 游客处理
                redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomGuestSuffix;
                targetMap = LIVE_ROOM_GUEST_USERS;
            } else {
                // 普通用户处理
                redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomUserSuffix;
                targetMap = LIVE_ROOM_USERS;
            }
            
            RSet<String> roomUsers = redissonClient.getSet(redisKey);
            roomUsers.add(userId);

            // 设置在线用户列表过期时间为12小时，避免内存泄漏
            //roomUsers.expire(12, TimeUnit.HOURS);

            // 同时在本地缓存中保存一份
            targetMap.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet()).add(userId);
            
            if (isGuest) {
                logger.info("🎭 游客加入直播间: roomId={}, userId={}, Redis键={}", roomId, userId, redisKey);
            } else {
                logger.info("👤 正式用户加入直播间: roomId={}, userId={}, Redis键={}", roomId, userId, redisKey);
            }
        } catch (Exception e) {
            logger.error("将用户添加到直播间失败", e);
        }
    }
    

    
    /**
     * 从直播间移除用户
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param isGuest 是否为游客
     */
    public static void removeUserFromRoom(String roomId, String userId, boolean isGuest) {
        if (StringUtils.isEmpty(roomId) || StringUtils.isEmpty(userId)) {
            return;
        }
        
        try {
            // 从Redis中移除
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            
            String redisKey;
            Map<String, Set<String>> targetMap;
            
            if (isGuest|| Objects.isNull(isGuest)) {
                // 游客处理
                redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomGuestSuffix;
                targetMap = LIVE_ROOM_GUEST_USERS;
            } else {
                // 普通用户处理
                redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomUserSuffix;
                targetMap = LIVE_ROOM_USERS;
            }
            
            RSet<String> redisRoomUsers = redissonClient.getSet(redisKey);
            redisRoomUsers.remove(userId);

            // 同时从本地缓存中移除
            Set<String> localRoomUsers = targetMap.get(roomId);
            if (localRoomUsers != null) {
                localRoomUsers.remove(userId);

                // 如果直播间没有用户了，移除直播间
                if (localRoomUsers.isEmpty()) {
                    targetMap.remove(roomId);
                }
            }

            logger.info("用户 {} 已离开直播间 {}, 游客状态: {}", userId, roomId, isGuest);
        } catch (Exception e) {
            logger.error("将用户从直播间移除失败", e);
        }
    }
    
    /**
     * 从直播间移除用户（兼容原有调用，但建议使用带isGuest参数的版本）
     * 注意：由于缺少appId参数，游客判断可能不够准确
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @deprecated 建议使用 removeUserFromRoom(String roomId, String userId, boolean isGuest) 方法
     */
    @Deprecated
    public static void removeUserFromRoom(String roomId, String userId) {
        // 使用废弃的方法进行游客判断，并记录警告
        boolean isGuest = isUserGuest(userId);
        logger.warn("⚠️ 使用了不推荐的removeUserFromRoom方法，建议传入isGuest参数: roomId={}, userId={}, 推测isGuest={}",
                   roomId, userId, isGuest);
        removeUserFromRoom(roomId, userId, isGuest);
    }
    
    /**
     * 获取直播间内的所有用户（包括普通用户和游客）
     * @param roomId 直播间ID
     * @return 用户ID集合
     */
    public static Set<String> getRoomUsers(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            logger.warn("⚠️ 获取直播间用户列表参数无效: roomId={}", roomId);
            return new HashSet<>();
        }

        Set<String> allUsers = new HashSet<>();

        // 获取普通用户
        Set<String> normalUsers = getRoomNormalUsers(roomId);
        if (normalUsers != null && !normalUsers.isEmpty()) {
            allUsers.addAll(normalUsers);
            logger.debug("👤 直播间普通用户: roomId={}, 用户数={}, 用户列表={}", roomId, normalUsers.size(), normalUsers);
        }

        // 获取游客
        Set<String> guestUsers = getRoomGuestUsers(roomId);
        if (guestUsers != null && !guestUsers.isEmpty()) {
            allUsers.addAll(guestUsers);
            logger.debug("🎭 直播间游客用户: roomId={}, 游客数={}, 游客列表={}", roomId, guestUsers.size(), guestUsers);
        }

        logger.debug("🎬 直播间总用户: roomId={}, 总用户数={}, 普通用户数={}, 游客数={}",
                   roomId, allUsers.size(),
                   normalUsers != null ? normalUsers.size() : 0,
                   guestUsers != null ? guestUsers.size() : 0);

        return allUsers;
    }
    
    /**
     * 获取直播间内的普通用户
     * @param roomId 直播间ID
     * @return 普通用户ID集合
     */
    public static Set<String> getRoomNormalUsers(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return new HashSet<>();
        }
        
        // 优先从本地缓存获取
        Set<String> localUsers = LIVE_ROOM_USERS.get(roomId);
        if (localUsers != null && !localUsers.isEmpty()) {
            return new HashSet<>(localUsers);
        }
        
        // 本地缓存没有，从Redis获取
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            String redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomUserSuffix;
            RSet<String> roomUsers = redissonClient.getSet(redisKey);
            Set<String> users = roomUsers.readAll();
            
            // 更新本地缓存
            if (!users.isEmpty()) {
                LIVE_ROOM_USERS.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet()).addAll(users);
            }
            
            return users;
        } catch (Exception e) {
            logger.error("获取直播间普通用户失败", e);
            return new HashSet<>();
        }
    }
    
    /**
     * 获取直播间内的游客
     * @param roomId 直播间ID
     * @return 游客ID集合
     */
    public static Set<String> getRoomGuestUsers(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return new HashSet<>();
        }
        
        // 优先从本地缓存获取
        Set<String> localUsers = LIVE_ROOM_GUEST_USERS.get(roomId);
        if (localUsers != null && !localUsers.isEmpty()) {
            return new HashSet<>(localUsers);
        }
        
        // 本地缓存没有，从Redis获取
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            String redisKey = Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomGuestSuffix;
            RSet<String> roomUsers = redissonClient.getSet(redisKey);
            Set<String> users = roomUsers.readAll();
            
            // 更新本地缓存
            if (!users.isEmpty()) {
                LIVE_ROOM_GUEST_USERS.computeIfAbsent(roomId, k -> ConcurrentHashMap.newKeySet()).addAll(users);
            }
            
            return users;
        } catch (Exception e) {
            logger.error("获取直播间游客失败", e);
            return new HashSet<>();
        }
    }
    
    /**
     * 判断用户是否为游客（不推荐使用，缺少appId参数导致准确性不足）
     * @param userId 用户ID
     * @return 是否为游客
     * @deprecated 建议使用 isUserGuest(Integer appId, String userId) 方法以获得更准确的结果
     */
    @Deprecated
    public static boolean isUserGuest(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return false;
        }

        // 1. 首先通过用户ID前缀快速判断
        if (userId.startsWith("guest_")) {
            return true;
        }

        // 2. 检查本地游客连接缓存（精确匹配）
        for (Map.Entry<String, NioSocketChannel> entry : GUEST_CHANNELS.entrySet()) {
            String key = entry.getKey();
            // 键格式为 "appId:userId"，需要精确匹配userId部分
            if (key.contains(":") && key.substring(key.indexOf(":") + 1).equals(userId)) {
                return true;
            }
        }

        logger.warn("⚠️ 使用了不推荐的游客判断方法，建议传入appId参数: userId={}", userId);
        return false;
    }

    /**
     * 判断用户是否为游客（推荐使用的准确方法）
     * 采用多层判断策略确保准确性：
     * 1. 用户ID前缀快速判断
     * 2. 本地连接缓存检查
     * 3. Redis权威数据查询
     *
     * @param appId 应用ID
     * @param userId 用户ID
     * @return 是否为游客
     */
    public static boolean isUserGuest(Integer appId, String userId) {
        if (appId == null || StringUtils.isEmpty(userId)) {
            logger.debug("游客判断参数无效: appId={}, userId={}", appId, userId);
            return false;
        }

        // 1. 首先通过用户ID前缀快速判断（最高效）
        if (userId.startsWith("guest_")) {
            logger.debug("🎭 通过ID前缀识别游客: userId={}", userId);
            return true;
        }

        // 2. 检查本地游客连接缓存（中等效率）
        String localKey = appId + ":" + userId;
        if (GUEST_CHANNELS.containsKey(localKey)) {
            logger.debug("🎭 通过本地缓存识别游客: appId={}, userId={}", appId, userId);
            return true;
        }

        // 3. 查询Redis中的游客标识（权威数据源，但效率较低）
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient != null) {
                String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                boolean isGuest = redissonClient.getBucket(guestKey).isExists();
                if (isGuest) {
                    logger.debug("🎭 通过Redis识别游客: appId={}, userId={}", appId, userId);
                }
                return isGuest;
            }
        } catch (Exception e) {
            logger.warn("⚠️ 查询Redis游客标识失败: appId={}, userId={}, error={}", appId, userId, e.getMessage());
        }

        logger.debug("👤 确认为正式用户: appId={}, userId={}", appId, userId);
        return false;
    }

    /**
     * 批量判断用户是否为游客（性能优化版本）
     * 适用于需要同时判断多个用户身份的场景
     *
     * @param appId 应用ID
     * @param userIds 用户ID列表
     * @return 用户ID到游客状态的映射
     */
    public static Map<String, Boolean> batchCheckGuestUsers(Integer appId, List<String> userIds) {
        Map<String, Boolean> result = new HashMap<>();

        if (appId == null || userIds == null || userIds.isEmpty()) {
            logger.debug("批量游客判断参数无效: appId={}, userIds={}", appId, userIds);
            return result;
        }

        List<String> needRedisCheck = new ArrayList<>();

        // 第一轮：前缀判断和本地缓存检查
        for (String userId : userIds) {
            if (StringUtils.isEmpty(userId)) {
                result.put(userId, false);
                continue;
            }

            // 1. 用户ID前缀快速判断
            if (userId.startsWith("guest_")) {
                result.put(userId, true);
                continue;
            }

            // 2. 检查本地游客连接缓存
            String localKey = appId + ":" + userId;
            if (GUEST_CHANNELS.containsKey(localKey)) {
                result.put(userId, true);
                continue;
            }

            // 需要Redis查询的用户
            needRedisCheck.add(userId);
        }

        // 第二轮：批量Redis查询（如果有需要）
        if (!needRedisCheck.isEmpty()) {
            try {
                RedissonClient redissonClient = RedisManager.getRedissonClient();
                if (redissonClient != null) {
                    for (String userId : needRedisCheck) {
                        String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                        boolean isGuest = redissonClient.getBucket(guestKey).isExists();
                        result.put(userId, isGuest);
                    }
                } else {
                    // Redis不可用时，默认为正式用户
                    for (String userId : needRedisCheck) {
                        result.put(userId, false);
                    }
                }
            } catch (Exception e) {
                logger.warn("⚠️ 批量查询Redis游客标识失败: appId={}, userIds={}, error={}",
                           appId, needRedisCheck, e.getMessage());
                // 异常时默认为正式用户
                for (String userId : needRedisCheck) {
                    result.put(userId, false);
                }
            }
        }

        logger.debug("🎭 批量游客判断完成: appId={}, 总数={}, 游客数={}",
                    appId, userIds.size(), result.values().stream().mapToInt(b -> b ? 1 : 0).sum());

        return result;
    }

    /**
     * 从所有直播间中移除用户
     * 注意：由于缺少appId参数，游客判断可能不够准确，建议使用 removeUserFromAllRooms(Integer appId, String userId)
     * @param userId 用户ID
     * @deprecated 建议使用 removeUserFromAllRooms(Integer appId, String userId) 方法以获得更准确的结果
     */
    @Deprecated
    public static void removeUserFromAllRooms(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return;
        }

        try {
            Set<String> allRooms = new HashSet<>();
            int totalRemoved = 0;

            // 收集用户所在的所有直播间
            for (Map.Entry<String, Set<String>> entry : LIVE_ROOM_USERS.entrySet()) {
                if (entry.getValue().contains(userId)) {
                    allRooms.add(entry.getKey());
                }
            }

            for (Map.Entry<String, Set<String>> entry : LIVE_ROOM_GUEST_USERS.entrySet()) {
                if (entry.getValue().contains(userId)) {
                    allRooms.add(entry.getKey());
                }
            }

            // 对每个直播间，使用废弃方法移除用户（保持向后兼容）
            for (String roomId : allRooms) {
                removeUserFromRoom(roomId, userId); // 调用废弃的兼容方法
                totalRemoved++;
            }

            if (totalRemoved > 0) {
                logger.warn("⚠️ 使用了不推荐的removeUserFromAllRooms方法: userId={}, 移除了{}个直播间",
                           userId, totalRemoved);
            }
        } catch (Exception e) {
            logger.error("❌ 从所有直播间移除用户失败: userId={}", userId, e);
        }
    }

    /**
     * 从所有直播间中移除用户（推荐使用的准确版本）
     * @param appId 应用ID
     * @param userId 用户ID
     */
    public static void removeUserFromAllRooms(Integer appId, String userId) {
        if (appId == null || StringUtils.isEmpty(userId)) {
            logger.debug("removeUserFromAllRooms参数无效: appId={}, userId={}", appId, userId);
            return;
        }

        try {
            // 先判断用户身份
            boolean isGuest = isUserGuest(appId, userId);
            Set<String> allRooms = new HashSet<>();
            int totalRemoved = 0;

            // 收集用户所在的所有直播间
            for (Map.Entry<String, Set<String>> entry : LIVE_ROOM_USERS.entrySet()) {
                if (entry.getValue().contains(userId)) {
                    allRooms.add(entry.getKey());
                }
            }

            for (Map.Entry<String, Set<String>> entry : LIVE_ROOM_GUEST_USERS.entrySet()) {
                if (entry.getValue().contains(userId)) {
                    allRooms.add(entry.getKey());
                }
            }

            // 使用准确的用户身份移除
            for (String roomId : allRooms) {
                removeUserFromRoom(roomId, userId, isGuest);
                totalRemoved++;
            }

            if (totalRemoved > 0) {
                logger.info("🧹 用户 {} 已从所有直播间移除: 移除了{}个直播间, 用户类型={}",
                           userId, totalRemoved, isGuest ? "游客" : "正式用户");
            }
        } catch (Exception e) {
            logger.error("❌ 从所有直播间移除用户失败: appId={}, userId={}", appId, userId, e);
        }
    }

    /**
     * 主动清理所有无效连接
     * 定期调用此方法可以清理掉已断开但未及时清理的连接
     */
    public static void cleanupInactiveConnections() {
        logger.info("🧹 开始清理无效连接");

        try {
            // 清理正式用户的无效连接
            int cleanedRegularUsers = cleanupInactiveRegularConnections();

            // 清理游客的无效连接
            int cleanedGuestUsers = cleanupInactiveGuestConnections();

            logger.info("✅ 无效连接清理完成: 正式用户{}个, 游客{}个", cleanedRegularUsers, cleanedGuestUsers);

        } catch (Exception e) {
            logger.error("❌ 清理无效连接时异常", e);
        }
    }

    /**
     * 清理正式用户的无效连接
     * @return 清理的连接数量
     */
    private static int cleanupInactiveRegularConnections() {
        int cleanedCount = 0;
        Set<UserClientDto> toRemove = new HashSet<>();

        for (Map.Entry<UserClientDto, NioSocketChannel> entry : CHANNELS.entrySet()) {
            NioSocketChannel channel = entry.getValue();
            if (channel == null || !channel.isActive()) {
                toRemove.add(entry.getKey());
                cleanedCount++;

                if (channel != null) {
                    logger.debug("🧹 清理无效正式用户连接: userId={}, channel={}",
                               entry.getKey().getUserId(), channel.id());
                }
            }
        }

        // 批量移除无效连接
        toRemove.forEach(CHANNELS::remove);

        return cleanedCount;
    }

    /**
     * 清理游客的无效连接
     * @return 清理的连接数量
     */
    private static int cleanupInactiveGuestConnections() {
        int cleanedCount = 0;
        Set<String> toRemove = new HashSet<>();

        for (Map.Entry<String, NioSocketChannel> entry : GUEST_CHANNELS.entrySet()) {
            NioSocketChannel channel = entry.getValue();
            if (channel == null || !channel.isActive()) {
                toRemove.add(entry.getKey());
                cleanedCount++;


            }
        }

        // 批量移除无效连接
        toRemove.forEach(GUEST_CHANNELS::remove);

        return cleanedCount;
    }

    /**
     * 获取所有正式用户直播间ID列表
     * @return 直播间ID集合
     */
    public static Set<String> getAllRegularUserRooms() {
        return new HashSet<>(LIVE_ROOM_USERS.keySet());
    }

    /**
     * 获取所有游客直播间ID列表
     * @return 直播间ID集合
     */
    public static Set<String> getAllGuestUserRooms() {
        return new HashSet<>(LIVE_ROOM_GUEST_USERS.keySet());
    }


    /**
     * 清空指定直播间的所有本地缓存
     * 用于直播间关闭时清理本地缓存数据
     * @param roomId 直播间ID
     */
    public static void clearRoomCache(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return;
        }

        try {
            // 清理正式用户缓存
            Set<String> normalUsers = LIVE_ROOM_USERS.remove(roomId);

            // 清理游客缓存
            Set<String> guestUsers = LIVE_ROOM_GUEST_USERS.remove(roomId);

            int normalUserCount = normalUsers != null ? normalUsers.size() : 0;
            int guestUserCount = guestUsers != null ? guestUsers.size() : 0;

            logger.info("清空直播间本地缓存: roomId={}, 清理了{}个正式用户, {}个游客",
                    roomId, normalUserCount, guestUserCount);

        } catch (Exception e) {
            logger.error("清空直播间本地缓存失败: roomId={}", roomId, e);
        }
    }
}
