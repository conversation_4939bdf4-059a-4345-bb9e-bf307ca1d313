package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间统计实体类
 */
@Data
@TableName("im_live_room_stats")
public class LiveRoomStats {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 当前在线人数
     */
    private Integer onlineCount;

    /**
     * 峰值在线人数
     */
    private Integer peakOnlineCount;

    /**
     * 累计观看人数
     */
    private Integer totalViewerCount;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 礼物数
     */
    private Integer giftCount;

    /**
     * 礼物总价值(金币)
     */
    private Long giftValue;

    /**
     * 直播开始时间
     */
    private Date startTime;

    /**
     * 直播结束时间
     */
    private Date endTime;

    /**
     * 直播时长(秒)
     */
    private Integer duration;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 