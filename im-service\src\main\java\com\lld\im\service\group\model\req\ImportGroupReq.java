package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 导入群组请求
 */
@ApiModel(description = "导入群组请求模型")
@Data
public class ImportGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", example = "group123", notes = "群组唯一标识，不填则系统自动生成")
    private String groupId;

    @ApiModelProperty(value = "群主用户ID", example = "owner123", notes = "群组创建者的用户ID")
    private String ownerId;

    @ApiModelProperty(value = "群组类型", example = "1", notes = "1-私有群(类似微信) 2-公开群(类似QQ)")
    private Integer groupType;

    @ApiModelProperty(value = "群组名称", required = true, example = "技术交流群", notes = "群组的显示名称")
    @NotBlank(message = "{validation.group.name.not.blank}")
    private String groupName;

    @ApiModelProperty(value = "全员禁言状态", example = "0", notes = "0-不禁言 1-全员禁言")
    private Integer mute;

    @ApiModelProperty(value = "加入群权限", example = "0", notes = "0-所有人可加入 1-群成员可拉人 2-群管理员或群主可拉人")
    private Integer applyJoinType;

    @ApiModelProperty(value = "群简介", example = "这是一个技术交流群，欢迎大家分享技术心得")
    private String introduction;

    @ApiModelProperty(value = "群公告", example = "请大家遵守群规，文明交流")
    private String notification;

    @ApiModelProperty(value = "群头像URL", example = "https://example.com/group-avatar.jpg")
    private String photo;

    @ApiModelProperty(value = "最大成员数量", example = "500", notes = "群组成员上限")
    private Integer MaxMemberCount;

    @ApiModelProperty(value = "创建时间", example = "1640995200000", notes = "群组创建时间戳")
    private Long createTime;

    @ApiModelProperty(value = "扩展字段", example = "{\"tag\": \"tech\", \"level\": \"high\"}")
    private String extra;

}
