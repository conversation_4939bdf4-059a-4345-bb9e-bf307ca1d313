package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 基于会话关系获取用户信息请求
 * 
 * <AUTHOR>
 */
@ApiModel(description = "基于会话关系获取用户信息请求模型")
@Data
public class GetUserInfoByConversationReq extends RequestBase {

    @ApiModelProperty(value = "请求用户ID", required = true, example = "user123", notes = "发起请求的用户ID")
    @NotEmpty(message = "请求用户ID不能为空")
    private String requestUserId;

    @ApiModelProperty(value = "目标用户ID列表", required = true, example = "[\"user456\", \"user789\"]", notes = "要获取信息的目标用户ID列表")
    @NotEmpty(message = "目标用户ID列表不能为空")
    private List<String> targetUserIds;

    @ApiModelProperty(value = "是否包含好友关系", example = "true", notes = "是否包含好友关系的用户信息，默认为true")
    private Boolean includeFriends = true;

    @ApiModelProperty(value = "是否包含会话关系", example = "true", notes = "是否包含存在会话关系的用户信息，默认为true")
    private Boolean includeConversations = true;
}
