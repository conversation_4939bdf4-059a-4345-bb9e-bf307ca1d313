package com.lld.im.common.enums.command;


public enum CommandType {

    USER("4"),

    FRIEND("3"),

    GROUP("2"),

    MESSAGE("1"),
    
    LIVE_ROOM("5"),

    ;

    private String commandType;

    public String getCommandType() {
        return commandType;
    }

    CommandType(String commandType) {
        this.commandType = commandType;
    }

    public static CommandType getCommandType(String commandSub) {
        switch (commandSub) {
            case "1": return MESSAGE;
            case "2": return GROUP;
            case "3": return FRIEND;
            case "4": return USER;
            case "5": return LIVE_ROOM;
            default: return null;
        }
    }

}
