package com.lld.message.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;

/**
 * RabbitMQ连接监听器
 * 用于监控和记录RabbitMQ集群连接状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RabbitMQConnectionMonitor {

    @Autowired
    private ConnectionFactory connectionFactory;

    @Value("${spring.rabbitmq.addresses:}")
    private String addresses;

    @Value("${spring.rabbitmq.host:}")
    private String host;

    @Value("${spring.rabbitmq.port:5672}")
    private int port;

    @Value("${spring.rabbitmq.username:}")
    private String username;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${spring.rabbitmq.connection-timeout:30000}")
    private int connectionTimeout;

    @Value("${spring.rabbitmq.requested-heartbeat:30}")
    private int requestedHeartbeat;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 应用启动完成后检查RabbitMQ连接状态
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logRabbitMQConnectionInfo();
    }

    /**
     * 记录RabbitMQ连接信息
     */
    private void logRabbitMQConnectionInfo() {
        Instant startTime = Instant.now();
        
        try {
            log.info("=== IM-MESSAGE-STORE RabbitMQ 连接信息检查开始 ===");
            log.info("当前环境: {}", activeProfile);
            
            // 判断连接模式
            boolean isClusterMode = addresses != null && !addresses.trim().isEmpty();
            
            if (isClusterMode) {
                logClusterConnectionInfo();
            } else {
                logSingleConnectionInfo();
            }
            
            // 记录通用配置
            logCommonConfiguration();
            
            // 测试连接
            testRabbitMQConnection(startTime);
            
        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("RabbitMQ连接检查失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage(), e);
        }
    }

    /**
     * 记录集群连接信息
     */
    private void logClusterConnectionInfo() {
        log.info("RabbitMQ模式: 集群模式");
        log.info("集群地址: {}", addresses);
        
        // 解析集群节点
        String[] nodeAddresses = addresses.split(",");
        log.info("集群节点数量: {}", nodeAddresses.length);
        
        for (int i = 0; i < nodeAddresses.length; i++) {
            String nodeAddress = nodeAddresses[i].trim();
            log.info("  - 节点{}: {}", i + 1, nodeAddress);
        }
    }

    /**
     * 记录单机连接信息
     */
    private void logSingleConnectionInfo() {
        log.info("RabbitMQ模式: 单机模式");
        log.info("服务器地址: {}:{}", host, port);
    }

    /**
     * 记录通用配置信息
     */
    private void logCommonConfiguration() {
        log.info("用户名: {}", username);
        log.info("虚拟主机: {}", virtualHost);
        log.info("连接工厂类型: {}", connectionFactory.getClass().getSimpleName());
    }

    /**
     * 测试RabbitMQ连接
     */
    private void testRabbitMQConnection(Instant startTime) {
        try {
            log.info("开始测试RabbitMQ连接...");

            // 创建连接测试
            com.rabbitmq.client.Connection connection = connectionFactory.createConnection().getDelegate();

            if (connection != null && connection.isOpen()) {
                log.info("连接状态: 已连接");

                // 获取连接的详细信息
                if (connection.getAddress() != null) {
                    log.info("实际连接地址: {}:{}",
                        connection.getAddress().getHostAddress(),
                        connection.getPort());
                }

                connection.close();

                Duration duration = Duration.between(startTime, Instant.now());
                log.info("RabbitMQ连接测试成功 - 总耗时: {}ms", duration.toMillis());
            } else {
                log.warn("RabbitMQ连接状态异常");
            }

            log.info("=== IM-MESSAGE-STORE RabbitMQ 连接信息检查完成 ===");

        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("RabbitMQ连接测试失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage());
            log.warn("请检查RabbitMQ服务状态和网络连接");
            log.info("=== IM-MESSAGE-STORE RabbitMQ 连接信息检查完成 ===");
        }
    }
}
