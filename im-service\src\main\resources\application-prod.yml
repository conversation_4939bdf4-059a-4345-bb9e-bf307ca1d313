# 生产环境配置 - 集群模式
# 适用于生产环境，使用 Redis 集群和 RabbitMQ 集群

spring:
  # === MySQL 数据源配置 ===
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: root
    url: ************************************************************************************************************************************************************************
    username: root

    # HikariCP 连接池配置 - 生产环境
    hikari:
      pool-name: IMServiceProdHikariCP
      minimum-idle: 10                   # 生产环境较大的最小空闲连接数
      maximum-pool-size: 50              # 生产环境较大的最大连接池大小
      connection-timeout: 30000          # 连接超时时间（30秒）
      idle-timeout: 600000               # 空闲超时时间（10分钟）
      max-lifetime: 1800000              # 连接最大生命周期（30分钟）
      connection-test-query: SELECT 1    # 连接测试查询
      leak-detection-threshold: 60000    # 连接泄漏检测阈值（60秒）
      register-mbeans: true              # 启用 JMX 监控

  # === Redis 集群配置 (Redisson) ===
  redis:
    # 集群节点配置
    cluster:
      nodes: localhost:7000,localhost:7001,localhost:7002
      max-redirects: 5
    password: 123456
    timeout: 3000

    # Redisson 特定配置
    redisson:
      # 是否启用 NAT 映射（Docker 容器环境需要启用）
      enable-nat-map: true
      # NAT 映射配置（容器内部主机名 -> 宿主机地址）
      nat-map: redis-node-1:6379=127.0.0.1:7000,redis-node-2:6379=127.0.0.1:7001,redis-node-3:6379=127.0.0.1:7002


  # === RabbitMQ 集群配置 ===
  rabbitmq:
    # 集群模式配置
    addresses: localhost:5672,localhost:5673,localhost:5674
    username: admin
    password: admin123
    virtual-host: /

    # 生产环境连接配置 - 更长的超时时间
    connection-timeout: 60000
    requested-heartbeat: 60

    # 发布者配置
    publisher-confirm-type: correlated
    publisher-returns: true

    # 消费者配置 - 生产环境更高的并发
    listener:
      simple:
        concurrency: 10
        max-concurrency: 20
        acknowledge-mode: MANUAL
        prefetch: 5
        retry:
          enabled: true
          max-attempts: 5
          initial-interval: 2000
          multiplier: 3.0
          max-interval: 30000
        default-requeue-rejected: false

    # 模板配置
    template:
      mandatory: true
      receive-timeout: 10000
      reply-timeout: 10000
      retry:
        enabled: true
        max-attempts: 5
        initial-interval: 2000

    # 缓存配置 - 生产环境更大的缓存
    cache:
      connection:
        mode: channel
        # 注意：当 mode 为 channel 时，不能配置 connection.size
      channel:
        size: 100
        checkout-timeout: 10000


# 生产环境监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true

# 生产环境性能配置
server:
  tomcat:
    threads:
      max: 200
      min-spare: 10
    connection-timeout: 20000
    max-connections: 8192
