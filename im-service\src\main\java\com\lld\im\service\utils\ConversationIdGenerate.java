package com.lld.im.service.utils;


/**
 * @author: <PERSON><PERSON><PERSON>
 **/
public class ConversationIdGenerate {

    //A|B
    //B A
    public static String generateP2PId(String fromId,String toId){
        // 先检查是否相等
        if (fromId.equals(toId)) {
           throw new IllegalArgumentException("fromId 和 toId 不能相同：" + fromId);
        }
        
        int i = fromId.compareTo(toId);
        if(i < 0){
            return toId+"|"+fromId;
        }else if(i > 0){
            return fromId+"|"+toId;
        }

        throw new RuntimeException("生成会话ID失败");
    }
}
