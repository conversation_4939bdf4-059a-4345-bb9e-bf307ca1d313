# IM系统选择性调试启动指南

## 🎯 功能概述

现在您可以选择启动指定的调试服务，而不必启动所有服务。这样可以：
- 节省系统资源
- 加快启动速度
- 专注于特定服务的调试
- 避免不必要的服务冲突

## 🚀 使用方法

### 方法1: 命令行参数指定服务

```powershell
# 启动单个服务
.\quick-debug-all.ps1 im-service

# 启动多个指定服务
.\quick-debug-all.ps1 im-service im-tcp

# 启动所有服务
.\quick-debug-all.ps1 -All
```

### 方法2: 交互式选择

```powershell
# 进入交互式选择模式
.\quick-debug-all.ps1 -Interactive

# 或者直接运行（默认进入交互模式）
.\quick-debug-all.ps1
```

交互式选择界面：
```
Available Services:
==================
1. TCP Connection Service (im-tcp) - Debug Port: 5005
2. Business Service (im-service) - Debug Port: 5006
3. Message Store Service (im-message-store) - Debug Port: 5007
4. All Services
0. Exit

Select services (1-4, or 0 to exit, multiple choices separated by comma): 1,2
```

### 方法3: VS Code任务

按 `Ctrl+Shift+P` → `Tasks: Run Task` → 选择：

- **Interactive Debug Service Selection** - 交互式选择服务
- **Quick Start All Debug Services** - 启动所有服务
- **Quick Start Core Services (Service + TCP)** - 启动核心服务
- **Quick Start Business Service Only** - 只启动业务服务

## 📊 可用服务列表

| 服务名称 | 参数名 | 调试端口 | 说明 | VS Code配置 |
|----------|--------|----------|------|-------------|
| TCP连接服务 | `im-tcp` | 5005 | WebSocket连接处理 | `Attach to IM-TCP` |
| 核心业务服务 | `im-service` | 5006 | 主要业务逻辑 | `Attach to IM-Service` |
| 消息存储服务 | `im-message-store` | 5007 | 消息持久化 | `Attach to IM-Message-Store` |

## 🎮 实际使用示例

### 场景1: 只调试业务逻辑
```powershell
# 只启动业务服务
.\quick-debug-all.ps1 im-service

# 然后在VS Code中按F5，选择 "Attach to IM-Service"
```

### 场景2: 调试消息流程
```powershell
# 启动TCP和业务服务
.\quick-debug-all.ps1 im-tcp im-service

# 在VS Code中分别连接两个调试器
```

### 场景3: 完整系统调试
```powershell
# 启动所有服务
.\quick-debug-all.ps1 -All

# 或使用交互模式选择所有服务
.\quick-debug-all.ps1 -Interactive
# 然后选择 "4" (All Services)
```

## 🔧 等效的直接Java命令

脚本执行的实际Java命令：

```bash
# TCP连接服务 (端口5005)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml

# 核心业务服务 (端口5006)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar

# 消息存储服务 (端口5007)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5007 -jar im-message-store/target/im-message-store.jar
```

## 📋 脚本输出示例

### 成功启动示例
```
Quick Debug Services - Selective Batch Launcher
===============================================

Mode: Start specified services: im-service, im-tcp
Services to start: im-service, im-tcp

Checking JAR files...
✅ Business Service JAR found
✅ TCP Connection Service JAR found

Starting selected debug services...

1. Starting Business Service (Debug Port: 5006)...
   Command: java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar
   ✅ Business Service started successfully (PID: 12345)

2. Starting TCP Connection Service (Debug Port: 5005)...
   Command: java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml
   ✅ TCP Connection Service started successfully (PID: 12346)

========================================
        Startup Summary
========================================

✅ Business Service - PID: 12345, Debug Port: 5006
✅ TCP Connection Service - PID: 12346, Debug Port: 5005

Summary: 2 of 2 services started successfully

🎯 VS Code Debug Connection:
   1. Press F5 in VS Code
   2. Select debug configuration:
      - 'Attach to IM-Service' (port 5006)
      - 'Attach to IM-TCP' (port 5005)

🛑 Stop all services: .\stop-im-en.ps1 -Force

🎉 Selected services are running in background. Ready for debugging!
========================================
```

## 🛠️ 故障排除

### 常见问题

#### 1. 服务启动失败
**症状**: "Service failed to start"
**可能原因**:
- JAR文件损坏或不存在
- 端口被占用
- Java环境问题
- 配置文件缺失

**解决方案**:
```powershell
# 重新构建项目
.\build.ps1

# 检查端口占用
netstat -ano | findstr ":5006"

# 停止占用端口的进程
.\stop-im-en.ps1 -Force

# 检查Java版本
java -version
```

#### 2. 交互模式无响应
**解决方案**: 使用命令行参数模式
```powershell
.\quick-debug-all.ps1 im-service
```

#### 3. VS Code无法连接调试器
**检查项**:
- 服务是否成功启动
- 调试端口是否正确
- VS Code调试配置是否正确

## 💡 最佳实践

### 开发调试建议

1. **单服务调试**（推荐新手）:
   ```powershell
   .\quick-debug-all.ps1 im-service
   ```

2. **核心功能调试**:
   ```powershell
   .\quick-debug-all.ps1 im-service im-tcp
   ```

3. **完整流程调试**:
   ```powershell
   .\quick-debug-all.ps1 -All
   ```

### 性能优化

- **按需启动**: 只启动需要调试的服务
- **顺序调试**: 先调试单个服务，再调试服务间交互
- **资源监控**: 监控内存和CPU使用情况

### 团队协作

- **统一脚本**: 团队成员使用相同的启动脚本
- **文档化**: 记录常用的服务组合
- **配置共享**: 同步VS Code调试配置

## 🔄 快速命令参考

```powershell
# 查看帮助
.\quick-debug-all.ps1 -Help

# 交互式选择
.\quick-debug-all.ps1 -Interactive

# 启动所有服务
.\quick-debug-all.ps1 -All

# 启动单个服务
.\quick-debug-all.ps1 im-service

# 启动多个服务
.\quick-debug-all.ps1 im-service im-tcp im-message-store

# 停止所有服务
.\stop-im-en.ps1 -Force

# 检查运行状态
Get-Process java
netstat -ano | findstr ":500"
```

## 📝 总结

现在您有了完整的选择性调试启动功能：

1. ✅ **命令行参数模式** - 快速指定服务启动
2. ✅ **交互式选择模式** - 用户友好的菜单选择
3. ✅ **VS Code任务集成** - 一键启动常用组合
4. ✅ **直接Java命令** - 透明的底层实现
5. ✅ **完整的错误处理** - 详细的状态反馈

这样您就可以根据具体的调试需求，灵活选择启动哪些服务，提高开发效率！🎉
