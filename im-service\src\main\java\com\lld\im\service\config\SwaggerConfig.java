package com.lld.im.service.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.*;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spi.service.contexts.SecurityContext;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Swagger API文档配置
 * 
 * @description: 配置Swagger API文档生成，支持在线访问和测试API接口
 * @author: IM System
 * @version: 1.0
 */
@Configuration
@EnableSwagger2
@Profile({"dev", "test", "prod"}) // 在开发、测试和生产环境启用
public class SwaggerConfig {

    /**
     * 创建API文档
     */
    @Bean
    public Docket createRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
                .apiInfo(apiInfo())
                .select()
                .apis(RequestHandlerSelectors.basePackage("com.lld.im.service"))
                .paths(PathSelectors.any())
                .build()
                .securitySchemes(securitySchemes())
                .securityContexts(securityContexts());
    }

    /**
     * API文档基本信息
     */
    private ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("IM即时通讯系统 API文档")
                .description("IM即时通讯系统提供完整的即时通讯功能，包括用户管理、好友关系、群组管理、消息收发、会话管理等核心功能")
                .version("1.0.0")
                .contact(new Contact("IM System Team", "", ""))
                .license("Apache License 2.0")
                .licenseUrl("http://www.apache.org/licenses/LICENSE-2.0")
                .build();
    }

    /**
     * 安全配置方案
     */
    private List<SecurityScheme> securitySchemes() {
        return Arrays.asList(
                new ApiKey("X-App-Id", "X-App-Id", "header"),
                new ApiKey("X-Identifier", "X-Identifier", "header"),
                new ApiKey("X-User-Sign", "X-User-Sign", "header"),
                new ApiKey("X-Client-Type", "X-Client-Type", "header"),
                new ApiKey("X-Imei", "X-Imei", "header")
        );
    }

    /**
     * 安全上下文配置
     */
    private List<SecurityContext> securityContexts() {
        return Collections.singletonList(
                SecurityContext.builder()
                        .securityReferences(defaultAuth())
                        .forPaths(PathSelectors.regex("^(?!.*(/login|/checkSend)).*$"))
                        .build()
        );
    }

    /**
     * 默认的安全引用
     */
    private List<SecurityReference> defaultAuth() {
        AuthorizationScope authorizationScope = new AuthorizationScope("global", "accessEverything");
        AuthorizationScope[] authorizationScopes = new AuthorizationScope[1];
        authorizationScopes[0] = authorizationScope;
        return Arrays.asList(
                new SecurityReference("X-App-Id", authorizationScopes),
                new SecurityReference("X-Identifier", authorizationScopes),
                new SecurityReference("X-User-Sign", authorizationScopes),
                new SecurityReference("X-Client-Type", authorizationScopes),
                new SecurityReference("X-Imei", authorizationScopes)
        );
    }
}
