package com.lld.im.service.friendship.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.friendship.model.req.ApproverFriendRequestReq;
import com.lld.im.service.friendship.model.req.GetFriendShipRequestReq;
import com.lld.im.service.friendship.model.req.ReadFriendShipRequestReq;
import com.lld.im.service.friendship.service.ImFriendShipRequestService;
import com.lld.im.service.interceptor.UserPermissionCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "好友请求管理", description = "好友申请的审批、查询和状态管理相关接口")
@RestController
@RequestMapping("v1/friendshipRequest")
public class ImFriendShipRequestController extends BaseController {

    @Autowired
    ImFriendShipRequestService imFriendShipRequestService;

    @ApiOperation(value = "审批好友请求", notes = "同意或拒绝好友申请")
    @PutMapping("/approveFriendRequest")
    public ResponseVO approveFriendRequest(
            @ApiParam(value = "审批好友请求参数", required = true) @RequestBody @Validated ApproverFriendRequestReq req){
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return imFriendShipRequestService.approverFriendRequest(req);
    }

    @ApiOperation(value = "获取好友请求列表", notes = "获取用户收到的好友申请列表")
    @PostMapping("/getFriendRequest")
    @UserPermissionCheck("fromId")
    public ResponseVO getFriendRequest(
            @ApiParam(value = "获取好友请求参数", required = true) @RequestBody @Validated GetFriendShipRequestReq req){
        fillCommonParams(req);
        return imFriendShipRequestService.getFriendRequest(req.getFromId(),req.getAppId());
    }

    @ApiOperation(value = "标记好友请求已读", notes = "将好友申请标记为已读状态")
    @PutMapping("/readFriendShipRequestReq")
    @UserPermissionCheck("fromId")
    public ResponseVO readFriendShipRequestReq(
            @ApiParam(value = "标记已读请求参数", required = true) @RequestBody @Validated ReadFriendShipRequestReq req){
        fillCommonParams(req);
        return imFriendShipRequestService.readFriendShipRequestReq(req);
    }


}
