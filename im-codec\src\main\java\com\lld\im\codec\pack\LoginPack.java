package com.lld.im.codec.pack;

import lombok.Data;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Data
public class LoginPack {

    private String userId;

    private Integer appId;

    private String deviceId;

    private Boolean isGuest;

    /**
     * 用户昵称（可选）
     * 客户端可以传递昵称，如果为空则服务端查询用户服务获取
     */
    private String nickname;

    public Boolean getIsGuest() {
        return isGuest;
    }

    public void setIsGuest(Boolean isGuest) {
        this.isGuest = isGuest;
    }

}
