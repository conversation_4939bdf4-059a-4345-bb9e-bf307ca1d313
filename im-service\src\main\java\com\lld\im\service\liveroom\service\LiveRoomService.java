package com.lld.im.service.liveroom.service;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.liveroom.model.req.CreateLiveRoomReq;
import com.lld.im.service.liveroom.model.req.JoinLiveRoomReq;
import com.lld.im.service.liveroom.model.req.SendLiveRoomMsgReq;
import com.lld.im.service.liveroom.model.req.UpdateAnnouncementReq;
import com.lld.im.service.liveroom.model.req.KickUserReq;
import com.lld.im.service.liveroom.model.req.CloseLiveRoomReq;
import com.lld.im.service.liveroom.model.req.MuteUserReq;
import com.lld.im.service.liveroom.model.req.MuteAllReq;
import com.lld.im.service.liveroom.model.req.LeaveLiveRoomReq;
import com.lld.im.service.liveroom.model.req.CheckLiveRoomExistsReq;
import com.lld.im.service.liveroom.model.resp.LiveRoomMsgResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomUserResp;
import com.lld.im.service.liveroom.model.resp.LiveRoomExistsResp;

import java.util.List;
import java.util.Map;

/**
 * 直播间服务接口
 */
public interface LiveRoomService {

    /**
     * 创建直播间
     *
     * @param req 创建直播间请求
     * @return 直播间ID
     */
    ResponseVO<String> createLiveRoom(CreateLiveRoomReq req);

    /**
     * 加入直播间
     *
     * @param req 加入直播间请求
     * @return 直播间信息
     */
    ResponseVO<LiveRoomResp> joinLiveRoom(JoinLiveRoomReq req);



    /**
     * 离开直播间
     *
     * @param roomId     直播间ID
     * @param userId     用户ID
     * @param appId      应用ID
     * @param clientType 终端类型
     * @param imei       终端IMEI
     * @return 操作结果
     */
    ResponseVO<Void> leaveLiveRoom(String roomId, String userId, Integer appId, Integer clientType, String imei);

    /**
     * 离开直播间（新版本）
     *
     * @param req 离开直播间请求
     * @return 操作结果
     */
    ResponseVO<Void> leaveLiveRoom(LeaveLiveRoomReq req);

    /**
     * 发送直播间消息
     *
     * @param req 发送消息请求
     * @return 消息ID
     */
    ResponseVO<String> sendMessage(SendLiveRoomMsgReq req);

    /**
     * 获取直播间信息
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 直播间信息
     */
    ResponseVO<LiveRoomResp> getLiveRoomInfo(String roomId, Integer appId);

    /**
     * 获取直播间最近消息
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @param limit  消息数量
     * @return 消息列表
     */
    ResponseVO<List<LiveRoomMsgResp>> getRecentMessages(String roomId, Integer appId, Integer limit);

    /**
     * 禁言/解禁用户
     *
     * @param req 禁言用户请求
     * @return 操作结果
     */
    ResponseVO<Void> muteUser(MuteUserReq req);

    /**
     * 全员禁言/解禁
     *
     * @param req 全员禁言请求
     * @return 操作结果
     */
    ResponseVO<Void> muteAll(MuteAllReq req);

    /**
     * 踢出用户
     *
     * @param req 踢出用户请求
     * @return 操作结果
     */
    ResponseVO<Void> kickUser(KickUserReq req);

    /**
     * 更新直播间公告
     *
     * @param req 更新公告请求
     * @return 操作结果
     */
    ResponseVO<Void> updateAnnouncement(UpdateAnnouncementReq req);

    /**
     * 关闭直播间
     *
     * @param req 关闭直播间请求
     * @return 操作结果
     */
    ResponseVO<Void> closeLiveRoom(CloseLiveRoomReq req);

    /**
     * 获取单个直播间的在线用户列表
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 在线用户列表
     */
    ResponseVO<List<LiveRoomUserResp>> getLiveRoomOnlineUsers(String roomId, Integer appId);

    /**
     * 批量获取多个直播间的用户列表信息
     *
     * @param roomIds 直播间ID列表
     * @param appId   应用ID
     * @return 直播间ID与用户列表的映射
     */
    ResponseVO<Map<String, List<LiveRoomUserResp>>> batchGetLiveRoomUsers(List<String> roomIds, Integer appId);

    /**
     * 批量检查直播间是否存在
     *
     * @param req 批量检查请求
     * @return 直播间存在状态列表
     */
    ResponseVO<List<LiveRoomExistsResp>> checkLiveRoomExists(CheckLiveRoomExistsReq req);
}