package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间实体类
 */
@Data
@TableName("im_live_room")
public class LiveRoom {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 直播间封面
     */
    private String roomCover;

    /**
     * 直播间状态 0-未开播 1-直播中 2-已结束
     */
    private Integer status;

    /**
     * 主播ID
     */
    private String anchorId;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 直播间公告
     */
    private String announcement;

    /**
     * 是否开启禁言 0-否 1-是
     */
    private Integer muteAll;

    /**
     * 是否需要审核 0-否 1-是
     */
    private Integer needReview;

    /**
     * 最大在线人数
     */
    private Integer maxOnlineCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 