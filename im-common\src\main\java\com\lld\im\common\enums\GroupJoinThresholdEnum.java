package com.lld.im.common.enums;

/**
 * 群组进群门槛枚举
 * @author: lld
 * @description: 定义群组中不同的进群门槛级别
 */
public enum GroupJoinThresholdEnum {
    
    /**
     * 无要求（默认）
     */
    NO_REQUIREMENT(1, "无要求"),
    
    /**
     * 关注我
     */
    FOLLOW_ME(2, "关注我"),
    
    /**
     * 关注我超过7天
     */
    FOLLOW_ME_7_DAYS(3, "关注我超过7天"),
    
    /**
     * 关注我超过30天
     */
    FOLLOW_ME_30_DAYS(4, "关注我超过30天");
    
    private final int code;
    private final String description;
    
    GroupJoinThresholdEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据code获取枚举值
     * @param code 门槛代码
     * @return 对应的枚举值，如果不存在则返回NO_REQUIREMENT
     */
    public static GroupJoinThresholdEnum getByCode(int code) {
        for (GroupJoinThresholdEnum threshold : values()) {
            if (threshold.getCode() == code) {
                return threshold;
            }
        }
        return NO_REQUIREMENT; // 默认返回无要求
    }
    
    /**
     * 验证门槛代码是否有效
     * @param code 门槛代码
     * @return 是否有效
     */
    public static boolean isValidCode(int code) {
        for (GroupJoinThresholdEnum threshold : values()) {
            if (threshold.getCode() == code) {
                return true;
            }
        }
        return false;
    }
}
