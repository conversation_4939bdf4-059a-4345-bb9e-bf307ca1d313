package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 添加好友分组请求
 */
@ApiModel(description = "添加好友分组请求模型")
@Data
public class AddFriendShipGroupReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起创建分组操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    public String fromId;

    @ApiModelProperty(value = "分组名称", required = true, example = "同事", notes = "要创建的好友分组名称")
    @NotBlank(message = "分组名称不能为空")
    private String groupName;

    @ApiModelProperty(value = "初始成员用户ID列表", example = "[\"user456\", \"user789\"]", notes = "创建分组时要添加的初始成员用户ID列表，可为空")
    private List<String> toIds;

}
