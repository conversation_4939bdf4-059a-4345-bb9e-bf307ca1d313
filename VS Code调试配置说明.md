# VS Code IM系统调试配置说明

## 📁 已创建的配置文件

已为您创建了完整的VS Code调试配置文件：

- **`.vscode/launch.json`** - 调试启动配置
- **`.vscode/tasks.json`** - 构建和启动任务
- **`.vscode/settings.json`** - 项目设置（已更新）

## 🚀 调试配置说明

### launch.json 配置项

#### 1. 远程调试配置（推荐）
```json
"Attach to IM-TCP"           # 连接到TCP服务 (端口5005)
"Attach to IM-Service"       # 连接到业务服务 (端口5006)  
"Attach to IM-Message-Store" # 连接到消息存储服务 (端口5007)
```

#### 2. 本地启动配置
```json
"Launch IM-TCP (Local)"           # 本地启动TCP服务
"Launch IM-Service (Local)"       # 本地启动业务服务
"Launch IM-Message-Store (Local)" # 本地启动消息存储服务
```

#### 3. 组合配置
```json
"Debug All IM Services (Attach)"  # 连接所有服务调试
"Launch All IM Services (Local)"  # 本地启动所有服务
```

### tasks.json 任务说明

#### 构建任务
- **Build IM System** - 构建整个IM系统
- **Quick Build** - 快速构建（跳过测试）

#### 调试启动任务
- **Start All IM Debug Services** - 启动所有调试服务
- **Start IM-TCP Debug** - 启动TCP调试服务
- **Start IM-Service Debug** - 启动业务调试服务
- **Start IM-Message-Store Debug** - 启动消息存储调试服务

#### 检查任务
- **Check IM-TCP Debug Port** - 检查TCP调试端口
- **Check IM-Service Debug Port** - 检查业务调试端口
- **Check IM-Message-Store Debug Port** - 检查消息存储调试端口

#### 管理任务
- **Stop All IM Services** - 停止所有IM服务

## 🎯 使用方法

### 方法1: 使用远程调试（推荐）

#### 步骤1: 启动调试服务
在VS Code终端中运行：
```powershell
# 启动所有服务
.\debug-simple.ps1 -NoSuspend

# 或启动单个服务
.\debug-simple.ps1 im-service -NoSuspend
```

#### 步骤2: 连接调试器
1. 按 `F5` 或点击 **Run and Debug**
2. 选择对应的调试配置：
   - `Attach to IM-Service`
   - `Attach to IM-TCP`
   - `Attach to IM-Message-Store`
3. 点击绿色播放按钮开始调试

#### 步骤3: 设置断点并调试
- 在代码中设置断点
- 触发相关功能
- 开始调试

### 方法2: 使用本地启动

#### 直接在VS Code中启动服务
1. 按 `F5` 选择：
   - `Launch IM-Service (Local)`
   - `Launch IM-TCP (Local)`
   - `Launch IM-Message-Store (Local)`
2. 服务将在VS Code集成终端中启动
3. 自动进入调试模式

### 方法3: 使用任务面板

#### 通过命令面板
1. 按 `Ctrl+Shift+P`
2. 输入 `Tasks: Run Task`
3. 选择需要的任务：
   - `Build IM System`
   - `Start All IM Debug Services`
   - `Stop All IM Services`

## 🔧 调试功能特性

### 自动化功能
- **预启动检查**: 自动检查调试端口是否可用
- **依赖构建**: 调试前自动构建项目
- **端口验证**: 连接前验证服务是否运行

### 调试增强
- **代码镜头**: 在方法上显示运行/调试按钮
- **变量查看**: 优化的变量显示设置
- **源码下载**: 自动下载Maven依赖源码
- **导入整理**: 保存时自动整理导入

### 项目优化
- **文件排除**: 排除target、logs等目录
- **文件嵌套**: 相关文件自动分组显示
- **终端配置**: 默认使用PowerShell并绕过执行策略

## 📊 调试端口对照表

| 服务 | 调试端口 | VS Code配置名称 | 主类 |
|------|----------|----------------|------|
| im-tcp | 5005 | Attach to IM-TCP | com.lld.im.tcp.Starter |
| im-service | 5006 | Attach to IM-Service | com.lld.im.service.Application |
| im-message-store | 5007 | Attach to IM-Message-Store | com.lld.message.Application |

## 🎮 快捷键

### 调试快捷键
- `F5` - 开始调试
- `Ctrl+F5` - 开始执行（不调试）
- `Shift+F5` - 停止调试
- `Ctrl+Shift+F5` - 重启调试
- `F9` - 切换断点
- `F10` - 单步跳过
- `F11` - 单步进入
- `Shift+F11` - 单步跳出

### 任务快捷键
- `Ctrl+Shift+P` - 命令面板
- `Ctrl+Shift+\`` - 新建终端
- `Ctrl+\`` - 切换终端

## 🐛 故障排除

### 常见问题

#### 1. 调试器无法连接
**症状**: "Failed to connect to remote VM"
**解决方案**:
```powershell
# 检查服务是否运行
netstat -ano | findstr ":5006"

# 重新启动调试服务
.\debug-simple.ps1 im-service -NoSuspend
```

#### 2. 端口被占用
**症状**: "Address already in use"
**解决方案**:
```powershell
# 停止所有服务
.\stop-im-en.ps1 -Force

# 重新启动
.\debug-simple.ps1 -NoSuspend
```

#### 3. 构建失败
**症状**: 任务执行失败
**解决方案**:
```powershell
# 手动构建
.\build.ps1 -SkipTests

# 或使用VS Code任务
Ctrl+Shift+P → Tasks: Run Task → Build IM System
```

#### 4. Java扩展问题
**解决方案**:
1. 确保安装了 **Extension Pack for Java**
2. 重新加载窗口: `Ctrl+Shift+P` → `Developer: Reload Window`
3. 清理工作区: `Ctrl+Shift+P` → `Java: Reload Projects`

## 📝 最佳实践

### 调试工作流
1. **构建项目**: 使用 `Build IM System` 任务
2. **启动服务**: 使用 `Start All IM Debug Services` 任务
3. **连接调试**: 选择对应的 `Attach to` 配置
4. **设置断点**: 在关键代码位置设置断点
5. **触发功能**: 通过API或前端触发相关功能
6. **调试分析**: 查看变量、调用栈等信息
7. **停止服务**: 使用 `Stop All IM Services` 任务

### 性能优化
- 使用 `-NoSuspend` 模式避免等待调试器连接
- 只启动需要调试的服务
- 调试完成后及时停止服务释放资源

### 团队协作
- 统一使用相同的调试端口配置
- 共享 `.vscode` 配置文件
- 文档化调试流程和注意事项

## 🔄 配置更新

如需修改调试配置：

1. **修改端口**: 编辑 `launch.json` 中的 `port` 字段
2. **添加服务**: 在 `launch.json` 中添加新的配置项
3. **修改任务**: 编辑 `tasks.json` 中的任务定义
4. **更新设置**: 修改 `settings.json` 中的项目设置

配置修改后，重新加载VS Code窗口使配置生效。
