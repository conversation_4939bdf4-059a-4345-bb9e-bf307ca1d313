package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 更新群组信息请求
 */
@ApiModel(description = "更新群组信息请求模型")
@Data
public class UpdateGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要更新的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "群组的显示名称")
    private String groupName;

    @ApiModelProperty(value = "全员禁言状态", example = "0", notes = "0-不禁言 1-全员禁言")
    private Integer mute;

    @ApiModelProperty(value = "加入群权限", example = "0", notes = "0-所有人可加入 1-群成员可拉人 2-群管理员或群主可拉人")
    private Integer applyJoinType;

    @ApiModelProperty(value = "群简介", example = "这是一个技术交流群，欢迎大家分享技术心得")
    private String introduction;

    @ApiModelProperty(value = "群公告", example = "请大家遵守群规，文明交流")
    private String notification;

    @ApiModelProperty(value = "群头像URL", example = "https://example.com/group-avatar.jpg")
    private String photo;

    @ApiModelProperty(value = "群成员上限", example = "500", notes = "群组最大成员数量")
    private Integer maxMemberCount;

    @ApiModelProperty(value = "扩展字段", example = "{\"tag\": \"tech\", \"level\": \"high\"}")
    private String extra;

}
