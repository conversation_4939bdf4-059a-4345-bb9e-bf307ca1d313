package com.lld.im.service.group.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户群组申请记录DTO
 * @description: 用户群组申请记录数据传输对象
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户群组申请记录数据传输对象")
@Data
public class UserApplyDto {

    @ApiModelProperty(value = "申请ID", example = "123456", notes = "群申请记录的唯一标识")
    private Long applyId;

    @ApiModelProperty(value = "群组ID", example = "group123", notes = "申请加入的群组唯一标识")
    private String groupId;

    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "申请加入的群组名称")
    private String groupName;

    @ApiModelProperty(value = "群组头像", example = "https://example.com/group-avatar.jpg", notes = "申请加入的群组头像URL")
    private String groupAvatar;

    @ApiModelProperty(value = "申请状态", example = "0", notes = "申请状态：0-待审批 1-已同意 2-已拒绝 3-已撤销")
    private Integer applyStatus;

    @ApiModelProperty(value = "申请状态描述", example = "待审批", notes = "申请状态的文字描述")
    private String applyStatusDesc;

    @ApiModelProperty(value = "申请理由", example = "希望加入技术交流群", notes = "用户申请加群的理由说明")
    private String applyReason;

    @ApiModelProperty(value = "申请时间", example = "1672531200000", notes = "申请提交的时间戳，单位毫秒")
    private Long applyTime;

    @ApiModelProperty(value = "审批人用户ID", example = "admin123", notes = "处理申请的群主或管理员用户ID")
    private String approverId;

    @ApiModelProperty(value = "审批人昵称", example = "群主", notes = "处理申请的群主或管理员昵称")
    private String approverNickname;

    @ApiModelProperty(value = "审批时间", example = "1672531400000", notes = "申请处理的时间戳，单位毫秒")
    private Long approveTime;

    @ApiModelProperty(value = "拒绝理由", example = "群已满员", notes = "拒绝申请的理由说明")
    private String rejectReason;
}
