package com.lld.im.service.group.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.service.GroupMessageService;
import com.lld.im.service.group.service.ImGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 群组管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "群组管理", description = "群组创建、管理和消息发送相关接口")
@RestController
@RequestMapping("v1/group")
public class ImGroupController extends BaseController {

    @Autowired
    ImGroupService groupService;

    @Autowired
    GroupMessageService groupMessageService;

    @ApiOperation(value = "导入群组", notes = "批量导入群组信息到系统中")
    @PostMapping("/importGroup")
    public ResponseVO importGroup(
            @ApiParam(value = "导入群组请求参数", required = true) @RequestBody @Validated ImportGroupReq req)  {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return groupService.importGroup(req);
    }

    @ApiOperation(value = "创建群组", notes = "创建一个新的群组")
    @PostMapping("/createGroup")
    public ResponseVO createGroup(
            @ApiParam(value = "创建群组请求参数", required = true) @RequestBody @Validated CreateGroupReq req)  {
        fillCommonParams(req);
        return groupService.createGroup(req);
    }

    @ApiOperation(value = "获取群组信息", notes = "根据群组ID获取群组的详细信息")
    @PostMapping("/getGroupInfo")
    public ResponseVO getGroupInfo(
            @ApiParam(value = "获取群组信息请求参数", required = true) @RequestBody @Validated GetGroupReq req)  {
        fillCommonParams(req);
        return groupService.getGroup(req);
    }

    @ApiOperation(value = "更新群组信息", notes = "更新群组的基本信息，如群名称、群公告等")
    @PutMapping("/update")
    public ResponseVO update(
            @ApiParam(value = "更新群组请求参数", required = true) @RequestBody @Validated UpdateGroupReq req)  {
        fillCommonParams(req);
        return groupService.updateBaseGroupInfo(req);
    }

    @ApiOperation(value = "获取已加入群组", notes = "获取用户已加入的所有群组列表")
    @PostMapping("/getJoinedGroup")
    public ResponseVO getJoinedGroup(
            @ApiParam(value = "获取已加入群组请求参数", required = true) @RequestBody @Validated GetJoinedGroupReq req)  {
        fillCommonParams(req);
        return groupService.getJoinedGroup(req);
    }


    @ApiOperation(value = "解散群组", notes = "解散指定的群组，只有群主可以执行此操作")
    @DeleteMapping("/destroyGroup")
    public ResponseVO destroyGroup(
            @ApiParam(value = "解散群组请求参数", required = true) @RequestBody @Validated DestroyGroupReq req)  {
        fillCommonParams(req);
        return groupService.destroyGroup(req);
    }

    @ApiOperation(value = "转让群组", notes = "将群主身份转让给其他群成员")
    @PutMapping("/transferGroup")
    public ResponseVO transferGroup(
            @ApiParam(value = "转让群组请求参数", required = true) @RequestBody @Validated TransferGroupReq req)  {
        fillCommonParams(req);
        return groupService.transferGroup(req);
    }

    @ApiOperation(value = "群组禁言设置", notes = "设置群组的全员禁言状态")
    @PutMapping("/forbidSendMessage")
    public ResponseVO forbidSendMessage(
            @ApiParam(value = "群组禁言请求参数", required = true) @RequestBody @Validated MuteGroupReq req)  {
        fillCommonParams(req);
        return groupService.muteGroup(req);
    }

    @ApiOperation(value = "发送群组消息", notes = "在群组中发送消息")
    @PostMapping("/sendMessage")
    public ResponseVO sendMessage(
            @ApiParam(value = "发送群组消息请求参数", required = true) @RequestBody @Validated SendGroupMessageReq req)  {
        fillCommonParams(req);
        return ResponseVO.successResponse(groupMessageService.send(req));
    }

/*    @ApiOperation(value = "同步已加入群组", notes = "增量同步用户已加入的群组列表数据")
    @PostMapping("/syncJoinedGroup")
    public ResponseVO syncJoinedGroup(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req)  {
        fillCommonParams(req);
        return groupService.syncJoinedGroupList(req);
    }*/

    @ApiOperation(value = "同步已加入群组V2", notes = "增量同步用户群组列表数据（包含退出群组信息），修复离线用户上线后无法移除已退出群组的问题")
    @PostMapping("/syncJoinedGroup")
    public ResponseVO syncJoinedGroupV2(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req)  {
        fillCommonParams(req);
        return groupService.syncJoinedGroupListV2(req);
    }

    @ApiOperation(value = "设置群组进群门槛", notes = "设置群组的进群门槛配置，支持无要求、关注我、关注我超过7天、关注我超过30天四种模式")
    @PostMapping("/joinThreshold/set")
    public ResponseVO setGroupJoinThreshold(
            @ApiParam(value = "设置门槛请求参数", required = true) @RequestBody @Validated SetGroupJoinThresholdReq req) {
        fillCommonParams(req);
        return groupService.setGroupJoinThreshold(req);
    }

    @ApiOperation(value = "获取群组进群门槛配置", notes = "查询指定群组的进群门槛配置信息")
    @GetMapping("/joinThreshold/get")
    public ResponseVO getGroupJoinThreshold(
            @ApiParam(value = "群组ID", required = true, example = "group123") @RequestParam String groupId) {
        Integer appId = getCurrentAppId();
        return groupService.getGroupJoinThreshold(groupId, appId);
    }

    @ApiOperation(value = "获取用户创建的群组列表", notes = "获取指定目标用户创建的所有群组列表，支持分页查询")
    @GetMapping("/getTargetUserGroup")
    public ResponseVO getGroupsCreatedByUser(
            @ApiParam(value = "目标用户ID", required = true, example = "user123") @RequestParam String targetUserId,
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam(value = "每页大小", example = "20") @RequestParam(defaultValue = "20") Integer pageSize) {

        // 获取当前登录用户和应用ID
        String currentUserId = getCurrentIdentifier();
        Integer appId = getCurrentAppId();

        return groupService.getGroupsCreatedByUser(targetUserId, appId, pageNum, pageSize, currentUserId);
    }

}
