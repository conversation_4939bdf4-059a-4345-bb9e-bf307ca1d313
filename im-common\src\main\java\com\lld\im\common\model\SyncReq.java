package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据同步请求
 * @author: Chackylee
 * @description: 客户端数据同步请求模型
 **/
@ApiModel(description = "数据同步请求模型")
@Data
public class SyncReq extends RequestBase {

    @ApiModelProperty(value = "客户端最大序列号", example = "1000", notes = "客户端当前拥有的最大数据序列号，用于增量同步")
    private Long lastSequence;

    @ApiModelProperty(value = "单次拉取数量限制", example = "100", notes = "一次同步请求最多拉取的数据条数")
    private Integer maxLimit;

}
