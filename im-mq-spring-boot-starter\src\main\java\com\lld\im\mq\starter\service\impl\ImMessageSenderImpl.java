package com.lld.im.mq.starter.service.impl;

import com.lld.im.mq.starter.config.ImMqProperties;
import com.lld.im.mq.starter.exception.ImMessageException;
import com.lld.im.mq.starter.model.ImLiveRoomMessage;
import com.lld.im.mq.starter.model.ImSystemNotificationMessage;
import com.lld.im.mq.starter.service.ImMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * IM消息发送服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ImMessageSenderImpl implements ImMessageSender {
    
    @Autowired
    private RabbitTemplate rabbitTemplate;
    
    @Autowired
    private ImMqProperties imMqProperties;
    
    @Autowired
    private Validator validator;
    
    @Override
    public void sendSystemNotification(ImSystemNotificationMessage message) {
        message.setTargetType(ImSystemNotificationMessage.TargetType.ALL_USERS);
        sendNotificationMessage(message);
    }
    
    @Override
    public void sendSystemNotificationToNormalUsers(ImSystemNotificationMessage message) {
        message.setTargetType(ImSystemNotificationMessage.TargetType.NORMAL_USERS);
        sendNotificationMessage(message);
    }
    
    @Override
    public void sendSystemNotificationToGuests(ImSystemNotificationMessage message) {
        message.setTargetType(ImSystemNotificationMessage.TargetType.GUEST_USERS);
        sendNotificationMessage(message);
    }
    
    @Override
    public void sendLiveRoomMessage(ImLiveRoomMessage message) {
        try {
            // 参数验证
            validateMessage(message);
            
            // 预处理消息
            preprocessMessage(message);
            
            // 构建路由键
            String routingKey = buildLiveRoomRoutingKey(message);
            
            // 发送消息
            CorrelationData correlationData = new CorrelationData(message.getMessageId());
            rabbitTemplate.convertAndSend(
                imMqProperties.getLiveroom().getExchange(), 
                routingKey, 
                message, 
                correlationData
            );
            
            log.info("Send live room message success, messageId: {}, roomId: {}, routingKey: {}", 
                    message.getMessageId(), message.getRoomId(), routingKey);
                    
        } catch (Exception e) {
            log.error("Send live room message failed, messageId: {}, roomId: {}", 
                    message.getMessageId(), message.getRoomId(), e);
            throw new ImMessageException("发送直播间消息失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public void sendBatchSystemNotification(List<ImSystemNotificationMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            log.warn("Batch system notification messages is empty");
            return;
        }
        
        for (ImSystemNotificationMessage message : messages) {
            try {
                sendSystemNotification(message);
            } catch (Exception e) {
                log.error("Send batch system notification failed, messageId: {}", 
                        message.getMessageId(), e);
            }
        }
    }
    
    @Override
    public void sendBatchLiveRoomMessage(List<ImLiveRoomMessage> messages) {
        if (messages == null || messages.isEmpty()) {
            log.warn("Batch live room messages is empty");
            return;
        }
        
        for (ImLiveRoomMessage message : messages) {
            try {
                sendLiveRoomMessage(message);
            } catch (Exception e) {
                log.error("Send batch live room message failed, messageId: {}, roomId: {}", 
                        message.getMessageId(), message.getRoomId(), e);
            }
        }
    }
    
    /**
     * 发送系统通知消息的通用方法
     */
    private void sendNotificationMessage(ImSystemNotificationMessage message) {
        try {
            // 参数验证
            validateMessage(message);
            
            // 预处理消息
            preprocessMessage(message);
            
            // 构建路由键
            String routingKey = buildNotificationRoutingKey(message);
            
            // 发送消息
            CorrelationData correlationData = new CorrelationData(message.getMessageId());
            rabbitTemplate.convertAndSend(
                imMqProperties.getNotification().getExchange(), 
                routingKey, 
                message, 
                correlationData
            );
            
            log.info("Send system notification success, messageId: {}, targetType: {}, routingKey: {}", 
                    message.getMessageId(), message.getTargetType(), routingKey);
                    
        } catch (Exception e) {
            log.error("Send system notification failed, messageId: {}, targetType: {}", 
                    message.getMessageId(), message.getTargetType(), e);
            throw new ImMessageException("发送系统通知失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 消息预处理
     */
    private void preprocessMessage(Object message) {
        if (message instanceof ImSystemNotificationMessage) {
            ImSystemNotificationMessage notificationMessage = (ImSystemNotificationMessage) message;
            if (notificationMessage.getMessageId() == null) {
                notificationMessage.setMessageId(UUID.randomUUID().toString());
            }
            if (notificationMessage.getTimestamp() == null) {
                notificationMessage.setTimestamp(System.currentTimeMillis());
            }
        } else if (message instanceof ImLiveRoomMessage) {
            ImLiveRoomMessage liveRoomMessage = (ImLiveRoomMessage) message;
            if (liveRoomMessage.getMessageId() == null) {
                liveRoomMessage.setMessageId(UUID.randomUUID().toString());
            }
            if (liveRoomMessage.getTimestamp() == null) {
                liveRoomMessage.setTimestamp(System.currentTimeMillis());
            }
        }
    }
    
    /**
     * 构建系统通知路由键
     */
    private String buildNotificationRoutingKey(ImSystemNotificationMessage message) {
        return imMqProperties.getNotification().getRoutingKey();
    }
    
    /**
     * 构建直播间消息路由键
     */
    private String buildLiveRoomRoutingKey(ImLiveRoomMessage message) {
        return imMqProperties.getLiveroom().getRoutingKey();
    }
    
    /**
     * 消息参数验证
     */
    private void validateMessage(Object message) {
        Set<ConstraintViolation<Object>> violations = validator.validate(message);
        if (!violations.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (ConstraintViolation<Object> violation : violations) {
                sb.append(violation.getMessage()).append("; ");
            }
            throw new ImMessageException("消息参数验证失败: " + sb.toString());
        }
    }
}
