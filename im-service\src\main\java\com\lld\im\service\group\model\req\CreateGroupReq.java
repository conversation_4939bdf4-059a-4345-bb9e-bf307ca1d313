package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @description: 创建群组请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "创建群组请求模型")
@Data
public class CreateGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", example = "group123", notes = "可选，不填则系统自动生成")
    private String groupId;

    @ApiModelProperty(value = "群主用户ID", required = true, example = "user123")
    private String ownerId;

    @ApiModelProperty(value = "群组类型", required = true, example = "1", notes = "1-私有群(类似微信) 2-公开群(类似QQ)")
    private Integer groupType;

    @ApiModelProperty(value = "群组名称", required = true, example = "技术交流群")
    private String groupName;

    @ApiModelProperty(value = "全员禁言状态", example = "0", notes = "0-不禁言 1-全员禁言")
    private Integer mute;

    @ApiModelProperty(value = "加入群权限", example = "0", notes = "0-所有人可加入 1-群成员可拉人 2-群管理员或群主可拉人")
    private Integer applyJoinType;

    @ApiModelProperty(value = "群简介", example = "这是一个技术交流群")
    private String introduction;

    @ApiModelProperty(value = "群公告", example = "欢迎大家积极交流技术问题")
    private String notification;

    @ApiModelProperty(value = "群头像URL", example = "https://example.com/group-avatar.jpg")
    private String photo;

    @ApiModelProperty(value = "最大成员数量", example = "500")
    private Integer MaxMemberCount;

    @ApiModelProperty(value = "初始群成员列表", notes = "创建群组时的初始成员")
    private List<GroupMemberDto> member;

    @ApiModelProperty(value = "扩展字段", example = "{\"tag\": \"tech\"}")
    private String extra;

}
