package com.lld.im.service.user.controller;

import com.lld.im.common.ClientType;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.route.RouteHandle;
import com.lld.im.common.route.RouteInfo;
import com.lld.im.common.utils.RouteInfoParseUtil;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.user.model.req.*;
import com.lld.im.service.user.service.ImUserService;
import com.lld.im.service.user.service.ImUserStatusService;
import com.lld.im.service.utils.NacosKit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 用户管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "用户管理", description = "用户账号管理相关接口")
@RestController
@RequestMapping("v1/user")
public class ImUserController extends BaseController {
    @Autowired
    ImUserService imUserService;

    @Autowired
    RouteHandle routeHandle;

    @Autowired
    ImUserStatusService imUserStatusService;

    @Autowired
    NacosKit nacosKit;

    @ApiOperation(value = "导入用户", notes = "批量导入用户信息到系统中")
    @PostMapping("/importUser")
    public ResponseVO importUser(
            @ApiParam(value = "导入用户请求参数", required = true) @RequestBody ImportUserReq req) {
        fillCommonParams(req);
        return imUserService.importUser(req);
    }

    @ApiOperation(value = "删除用户", notes = "从系统中删除指定用户")
    @DeleteMapping("/deleteUser")
    public ResponseVO deleteUser(
            @ApiParam(value = "删除用户请求参数", required = true) @RequestBody @Validated DeleteUserReq req) {
        fillCommonParams(req);
        return imUserService.deleteUser(req);
    }

    @ApiOperation(value = "用户登录", notes = "用户登录接口，返回负载均衡后的IM服务器地址")
    @PostMapping("/login")
    public ResponseVO login(
            @ApiParam(value = "用户登录请求参数", required = true) @RequestBody @Validated LoginReq req) {

        ResponseVO login = imUserService.login(req);
        if (login.isOk()) {
            List<String> allNode = new ArrayList<>();
            if (req.getClientType() == ClientType.WEB.getCode()) {
                allNode = nacosKit.getAllWebNode();
            } else {
                allNode = nacosKit.getAllTcpNode();
            }
            String s = routeHandle.routeServer(allNode, req
                    .getUserId());
            RouteInfo parse = RouteInfoParseUtil.parse(s);
            return ResponseVO.successResponse(parse);
        }

        return ResponseVO.errorResponse();
    }

    @ApiOperation(value = "获取用户序列号", notes = "获取用户的消息序列号，用于消息同步")
    @PostMapping("/getUserSequence")
    public ResponseVO getUserSequence(
            @ApiParam(value = "获取用户序列号请求参数", required = true) @RequestBody @Validated GetUserSequenceReq req) {
        fillCommonParams(req);
        return imUserService.getUserSequence(req);
    }

    @ApiOperation(value = "订阅用户在线状态", notes = "订阅指定用户的在线状态变化通知")
    @PostMapping("/subscribeUserOnlineStatus")
    public ResponseVO subscribeUserOnlineStatus(
            @ApiParam(value = "订阅用户在线状态请求参数", required = true) @RequestBody @Validated SubscribeUserOnlineStatusReq req) {
        fillCommonParams(req);
        imUserStatusService.subscribeUserOnlineStatus(req);
        return ResponseVO.successResponse();
    }

    @ApiOperation(value = "设置用户自定义状态", notes = "设置用户的自定义在线状态")
    @PutMapping("/setUserCustomerStatus")
    public ResponseVO setUserCustomerStatus(
            @ApiParam(value = "设置用户状态请求参数", required = true) @RequestBody @Validated SetUserCustomerStatusReq req) {
        fillCommonParams(req);
        imUserStatusService.setUserCustomerStatus(req);
        return ResponseVO.successResponse();
    }

    @ApiOperation(value = "查询好友在线状态", notes = "查询用户好友列表的在线状态")
    @PostMapping("/queryFriendOnlineStatus")
    public ResponseVO queryFriendOnlineStatus(
            @ApiParam(value = "查询好友在线状态请求参数", required = true) @RequestBody @Validated PullFriendOnlineStatusReq req) {
        fillCommonParams(req);
        return ResponseVO.successResponse(imUserStatusService.queryFriendOnlineStatus(req));
    }

    @ApiOperation(value = "查询用户在线状态", notes = "查询指定用户列表的在线状态")
    @PostMapping("/queryUserOnlineStatus")
    public ResponseVO queryUserOnlineStatus(
            @ApiParam(value = "查询用户在线状态请求参数", required = true) @RequestBody @Validated PullUserOnlineStatusReq req) {
        fillCommonParams(req);
        return ResponseVO.successResponse(imUserStatusService.queryUserOnlineStatus(req));
    }



}
