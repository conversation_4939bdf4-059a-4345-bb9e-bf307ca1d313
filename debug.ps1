# IM System Debug Launcher with Log Output
# Starts debug services with visible log output in VS Code Terminal

param(
    [string[]]$Services = @(),          # Specify services to start
    [switch]$All,                       # Start all services
    [switch]$Help                       # Show help
)

if ($Help) {
    Write-Host "IM System Debug Launcher with Log Output" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\debug.ps1                    # Interactive service selection" -ForegroundColor White
    Write-Host "  .\debug.ps1 -All               # Start all services with logs" -ForegroundColor White
    Write-Host "  .\debug.ps1 im-service         # Start specific service with logs" -ForegroundColor White
    Write-Host "  .\debug.ps1 im-service im-tcp  # Start multiple services with logs" -ForegroundColor White
    Write-Host ""
    Write-Host "Features:" -ForegroundColor Yellow
    Write-Host "  - Real-time log output in VS Code Terminal" -ForegroundColor White
    Write-Host "  - Each service runs in separate terminal tab" -ForegroundColor White
    Write-Host "  - Easy to monitor service status" -ForegroundColor White
    Write-Host "  - Debug ports ready for VS Code attachment" -ForegroundColor White
    Write-Host ""
    Write-Host "Available Services:" -ForegroundColor Yellow
    Write-Host "  im-tcp           - TCP Connection Service (Port 5005)" -ForegroundColor Blue
    Write-Host "  im-service       - Business Service (Port 5006)" -ForegroundColor Green
    Write-Host "  im-message-store - Message Store Service (Port 5007)" -ForegroundColor Magenta
    Write-Host ""
    exit 0
}

# Service definitions
$availableServices = @{
    "im-tcp" = @{
        name = "TCP Connection Service"
        jar = "im-tcp/target/im-tcp.jar"
        config = "im-tcp/src/main/resources/config-docker-cluster.yml"
        port = 5005
        color = "Blue"
    }
    "im-service" = @{
        name = "Business Service"
        jar = "im-service/target/im-service.jar"
        config = ""
        port = 5006
        color = "Green"
    }
    "im-message-store" = @{
        name = "Message Store Service"
        jar = "im-message-store/target/im-message-store.jar"
        config = ""
        port = 5007
        color = "Magenta"
    }
}

function Show-ServiceMenu {
    Write-Host ""
    Write-Host "Available Services:" -ForegroundColor Yellow
    Write-Host "==================" -ForegroundColor Cyan
    Write-Host "1. TCP Connection Service (im-tcp) - Debug Port: 5005" -ForegroundColor Blue
    Write-Host "2. Business Service (im-service) - Debug Port: 5006" -ForegroundColor Green
    Write-Host "3. Message Store Service (im-message-store) - Debug Port: 5007" -ForegroundColor Magenta
    Write-Host "4. All Services" -ForegroundColor White
    Write-Host "0. Exit" -ForegroundColor Red
    Write-Host ""
}

function Get-UserSelection {
    $selectedServices = @()
    
    do {
        Show-ServiceMenu
        $choice = Read-Host "Select services (1-4, or 0 to exit, multiple choices separated by comma)"
        
        if ($choice -eq "0") {
            Write-Host "Exiting..." -ForegroundColor Yellow
            exit 0
        }
        
        $choices = $choice -split "," | ForEach-Object { $_.Trim() }
        
        foreach ($c in $choices) {
            switch ($c) {
                "1" { 
                    if ($selectedServices -notcontains "im-tcp") {
                        $selectedServices += "im-tcp"
                        Write-Host "Added: TCP Connection Service" -ForegroundColor Blue
                    }
                }
                "2" { 
                    if ($selectedServices -notcontains "im-service") {
                        $selectedServices += "im-service"
                        Write-Host "Added: Business Service" -ForegroundColor Green
                    }
                }
                "3" { 
                    if ($selectedServices -notcontains "im-message-store") {
                        $selectedServices += "im-message-store"
                        Write-Host "Added: Message Store Service" -ForegroundColor Magenta
                    }
                }
                "4" { 
                    $selectedServices = @("im-tcp", "im-service", "im-message-store")
                    Write-Host "Added: All Services" -ForegroundColor White
                    break
                }
                default {
                    Write-Host "Invalid choice: $c" -ForegroundColor Red
                }
            }
        }
        
        if ($selectedServices.Count -gt 0) {
            Write-Host ""
            Write-Host "Selected services: $($selectedServices -join ', ')" -ForegroundColor Yellow
            $confirm = Read-Host "Continue with these services? (y/n)"
            if ($confirm -eq "y" -or $confirm -eq "Y" -or $confirm -eq "") {
                break
            } else {
                $selectedServices = @()
            }
        }
        
    } while ($true)
    
    return $selectedServices
}

function Start-ServiceWithLogs {
    param(
        [string]$ServiceKey,
        [hashtable]$ServiceConfig
    )
    
    Write-Host "Starting $($ServiceConfig.name) with log output..." -ForegroundColor $ServiceConfig.color
    
    # Check JAR file
    if (-not (Test-Path $ServiceConfig.jar)) {
        Write-Host "❌ JAR not found: $($ServiceConfig.jar)" -ForegroundColor Red
        Write-Host "Please run: .\build.ps1" -ForegroundColor Yellow
        return $false
    }
    
    # Build Java command
    $javaArgs = @(
        "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=$($ServiceConfig.port)",
        "-jar",
        $ServiceConfig.jar
    )
    
    if ($ServiceConfig.config -and (Test-Path $ServiceConfig.config)) {
        $javaArgs += $ServiceConfig.config
    }
    
    $command = "java $($javaArgs -join ' ')"
    Write-Host "Command: $command" -ForegroundColor Cyan
    Write-Host "Debug Port: $($ServiceConfig.port)" -ForegroundColor Green
    Write-Host ""
    Write-Host "Service will start with log output below..." -ForegroundColor Yellow
    Write-Host "Press Ctrl+C to stop this service" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor $ServiceConfig.color
    
    try {
        # Start the service in foreground with log output
        & java $javaArgs
    }
    catch {
        Write-Host "❌ Failed to start $($ServiceConfig.name): $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    Write-Host "========================================" -ForegroundColor $ServiceConfig.color
    Write-Host "$($ServiceConfig.name) stopped" -ForegroundColor Yellow
    return $true
}

# Main execution
Write-Host "IM System Debug Launcher with Log Output" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan
Write-Host ""

# Determine which services to start
$servicesToStart = @()

if ($All) {
    $servicesToStart = @("im-tcp", "im-service", "im-message-store")
    Write-Host "Mode: Start all services with log output" -ForegroundColor White
} elseif ($Services.Count -gt 0) {
    $servicesToStart = $Services
    Write-Host "Mode: Start specified services with log output: $($Services -join ', ')" -ForegroundColor White
} else {
    Write-Host "Mode: Interactive service selection" -ForegroundColor White
    $servicesToStart = Get-UserSelection
}

# Validate selected services
$validServices = @()
foreach ($service in $servicesToStart) {
    if ($availableServices.ContainsKey($service)) {
        $validServices += $service
    } else {
        Write-Host "Warning: Unknown service '$service' ignored" -ForegroundColor Yellow
    }
}

if ($validServices.Count -eq 0) {
    Write-Host "No valid services selected. Exiting." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Services to start: $($validServices -join ', ')" -ForegroundColor White
Write-Host ""

if ($validServices.Count -eq 1) {
    # Single service - start with logs in current terminal
    $serviceKey = $validServices[0]
    $serviceConfig = $availableServices[$serviceKey]
    
    Write-Host "Starting single service with log output in current terminal..." -ForegroundColor Yellow
    Write-Host ""
    
    Start-ServiceWithLogs -ServiceKey $serviceKey -ServiceConfig $serviceConfig
} else {
    # Multiple services - provide instructions for separate terminals
    Write-Host "Multiple services selected. For best log viewing experience:" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Option 1: Start each service in separate VS Code terminals:" -ForegroundColor Cyan
    
    foreach ($serviceKey in $validServices) {
        $serviceConfig = $availableServices[$serviceKey]
        Write-Host "  Terminal $($validServices.IndexOf($serviceKey) + 1): .\debug.ps1 $serviceKey" -ForegroundColor $serviceConfig.color
    }
    
    Write-Host ""
    Write-Host "Option 2: Use background mode (no logs):" -ForegroundColor Cyan
    Write-Host "  .\quick-debug-all.ps1 $($validServices -join ' ')" -ForegroundColor White
    Write-Host ""
    Write-Host "Option 3: Start first service now (others manually):" -ForegroundColor Cyan
    
    $firstService = $validServices[0]
    $firstConfig = $availableServices[$firstService]
    
    $choice = Read-Host "Start $($firstConfig.name) now? (y/n)"
    if ($choice -eq "y" -or $choice -eq "Y") {
        Write-Host ""
        Start-ServiceWithLogs -ServiceKey $firstService -ServiceConfig $firstConfig
    } else {
        Write-Host "Use the commands above to start services manually." -ForegroundColor Yellow
    }
}
