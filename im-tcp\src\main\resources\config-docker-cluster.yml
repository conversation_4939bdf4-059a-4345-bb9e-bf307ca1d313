lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  # Redis集群模式配置 - Docker集群环境
  redis:
    mode: cluster # 使用集群模式
    database: 0
    password: 123456
    timeout: 3000
    poolMinIdle: 8
    poolConnTimeout: 3000
    poolSize: 10

    cluster:
      nodes:
        - localhost:7000    # redis-node-1 外部端口 (主节点，槽位: 0-5460)
        - localhost:7001    # redis-node-2 外部端口 (主节点，槽位: 5461-10922)
        - localhost:7002    # redis-node-3 外部端口 (主节点，槽位: 10923-16383)
      maxRedirects: 3      # 集群重定向最大次数
      scanInterval: 1000   # 集群节点扫描间隔（毫秒）

  # RabbitMQ集群模式配置 - Docker集群环境
  rabbitmq:
    # 集群模式配置
    addresses:
      - host: localhost
        port: 5672      # rabbitmq-node-1 (rabbit@rabbit1)
      - host: localhost
        port: 5673      # rabbitmq-node-2 (rabbit@rabbit2)
      - host: localhost
        port: 5674      # rabbitmq-node-3 (rabbit@rabbit3)
    virtualHost: /
    userName: admin
    password: admin123
    connectionTimeout: 5000
    requestedHeartbeat: 30
    networkRecoveryInterval: 5000
    automaticRecoveryEnabled: true

  # Nacos集群配置 - 如果需要使用Nacos
  nacosConfig:
    serverAddr: s12:8848
    namespace: im-system
    group: DEFAULT_GROUP
    username: nacos
    password: nacos
    connectTimeout: 3000
    readTimeout: 5000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 4
    strategy: roomId

# 集群环境说明
# Redis Cluster:
#   - 3个主节点，无副本配置
#   - 数据自动分片到3个节点
#   - 支持故障转移和高可用
#   - 管理命令: redis-cli -c -h localhost -p 7000 -a 123456
#
# RabbitMQ Cluster:
#   - 3个disc节点集群
#   - 所有节点都可以处理消息
#   - 管理界面: http://localhost:15672 (admin/admin123)
#   - 高可用策略已配置，队列会在所有节点复制
#
# 健康检查:
#   - Redis: docker exec redis-node-1 redis-cli -a 123456 cluster info
#   - RabbitMQ: docker exec rabbitmq-node-1 rabbitmqctl cluster_status
