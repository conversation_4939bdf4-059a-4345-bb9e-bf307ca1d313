package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 获取群内角色请求
 */
@ApiModel(description = "获取群内角色请求模型")
@Data
public class GetRoleInGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要查询角色的群组唯一标识")
    private String groupId;

    @ApiModelProperty(value = "成员用户ID列表", example = "[\"user123\", \"user456\"]", notes = "要查询角色的群成员用户ID列表")
    private List<String> memberId;
}
