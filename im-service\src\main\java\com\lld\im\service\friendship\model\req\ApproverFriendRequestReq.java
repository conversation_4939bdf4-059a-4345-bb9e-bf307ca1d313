package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 审批好友请求
 */
@ApiModel(description = "审批好友请求模型")
@Data
public class ApproverFriendRequestReq extends RequestBase {

    @ApiModelProperty(value = "好友请求ID", required = true, example = "123456", notes = "要审批的好友请求记录ID")
    private Long id;

    @ApiModelProperty(value = "审批状态", required = true, example = "1", notes = "1-同意 2-拒绝")
    private Integer status;
}
