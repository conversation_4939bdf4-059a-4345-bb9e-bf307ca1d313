
-- 系统通知表
CREATE TABLE IF NOT EXISTS `im_system_notification` (
    `notification_id` bigint(20) NOT NULL COMMENT '通知ID',
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `notification_type` int(11) NOT NULL COMMENT '通知类型',
    `title` varchar(255) NOT NULL COMMENT '通知标题',
    `content` text NOT NULL COMMENT '通知内容',
    `extra` json DEFAULT NULL COMMENT '扩展字段',
    `create_time` bigint(20) NOT NULL COMMENT '创建时间',
    `del_flag` tinyint(4) DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`notification_id`),
    KEY `idx_app_id` (`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 用户系统通知关系表
CREATE TABLE IF NOT EXISTS `im_user_system_notification` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `app_id` int(11) NOT NULL COMMENT '应用ID',
    `user_id` varchar(64) NOT NULL COMMENT '用户ID',
    `notification_id` bigint(20) NOT NULL COMMENT '通知ID',
    `sequence` bigint(20) NOT NULL COMMENT '序列号',
    `read_status` tinyint(4) DEFAULT '0' COMMENT '读取状态',
    `read_time` bigint(20) DEFAULT NULL COMMENT '读取时间',
    `create_time` bigint(20) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_notification` (`app_id`,`user_id`,`notification_id`),
    KEY `idx_user_sequence` (`app_id`,`user_id`,`sequence`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4; 