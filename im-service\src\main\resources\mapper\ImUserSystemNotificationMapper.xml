<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lld.im.service.system.dao.mapper.ImUserSystemNotificationMapper">


    <!-- 按通知类型分页查询用户通知列表 -->
    <select id="selectNotificationsByType" resultType="com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity">
        SELECT
            un.id,
            un.app_id,
            un.user_id,
            un.notification_id,
            un.sequence,
            un.read_status,
            un.read_time,
            un.create_time
        FROM im_user_system_notification un
        INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
        WHERE un.app_id = #{appId}
            AND un.user_id = #{userId}
            AND sn.notification_type = #{notificationType}
            AND sn.del_flag = 0
        ORDER BY un.create_time DESC
    </select>

    <!-- 查询用户指定类型的未读通知列表 -->
    <select id="selectUnreadNotificationsByType" resultType="com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity">
        SELECT
            un.id,
            un.app_id,
            un.user_id,
            un.notification_id,
            un.sequence,
            un.read_status,
            un.read_time,
            un.create_time
        FROM im_user_system_notification un
        INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
        WHERE un.app_id = #{appId}
            AND un.user_id = #{userId}
            AND un.read_status = 0
            AND sn.notification_type = #{notificationType}
            AND sn.del_flag = 0
        ORDER BY un.create_time DESC
    </select>

    <!-- ==================== 优化查询方法 ==================== -->

    <!-- 获取用户有通知记录的所有通知类型列表（优化版本） -->
    <select id="selectUserNotificationTypes" resultType="java.lang.Integer">
        SELECT DISTINCT sn.notification_type
        FROM im_user_system_notification un
        INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
        WHERE un.app_id = #{appId}
          AND un.user_id = #{userId}
          AND sn.del_flag = 0
        ORDER BY sn.notification_type ASC
    </select>

    <!-- 获取指定类型的最新通知记录（优化版本） -->
    <select id="selectLatestNotificationByType" resultType="com.lld.im.service.system.model.dto.NotificationTypeLatestDto">
        SELECT
            n.notification_id,
            n.app_id,
            n.notification_type,
            n.title,
            n.content,
            n.extra,
            n.create_time,
            COALESCE(un.read_status, 0) as read_status,
            un.read_time,
            un.sequence
        FROM im_system_notification n
        LEFT JOIN im_user_system_notification un
          ON n.notification_id = un.notification_id
          AND un.app_id = #{appId}
          AND un.user_id = #{userId}
        WHERE n.app_id = #{appId}
          AND n.notification_type = #{notificationType}
          AND n.del_flag = 0
        ORDER BY n.create_time DESC, n.notification_id DESC
        LIMIT 1
    </select>

    <!-- 获取指定类型的未读数量（优化版本） -->
    <select id="selectUnreadCountByTypeOptimized" resultType="java.lang.Integer">
        SELECT COUNT(*) as unread_count
        FROM im_user_system_notification un
        INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
        WHERE un.app_id = #{appId}
          AND un.user_id = #{userId}
          AND un.read_status = 0
          AND sn.notification_type = #{notificationType}
          AND sn.del_flag = 0
    </select>

    <!-- ==================== 增量同步查询方法 ==================== -->

    <!-- 增量同步查询通知列表 - 支持条件过滤 -->
    <select id="selectNotificationsForSync" resultType="com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity">
        SELECT
            un.id,
            un.app_id,
            un.user_id,
            un.notification_id,
            un.sequence,
            un.read_status,
            un.read_time,
            un.create_time
        FROM im_user_system_notification un
        INNER JOIN im_system_notification sn ON un.notification_id = sn.notification_id
        WHERE un.app_id = #{appId}
          AND un.user_id = #{userId}
          AND un.sequence > #{lastSequence}
          <if test="onlyUnread != null and onlyUnread == true">
          AND un.read_status = 0
          </if>
          AND sn.del_flag = 0
        ORDER BY un.sequence ASC
        LIMIT #{limit}
    </select>

    <!-- 批量查询通知详情（用于增量同步） -->
    <select id="selectNotificationDetailsByIds" resultType="com.lld.im.service.system.model.dto.NotificationTypeLatestDto">
        SELECT
            n.notification_id,
            n.app_id,
            n.notification_type,
            n.title,
            n.content,
            n.extra,
            n.create_time,
            n.read_status ,
            null as read_time,
            n.notification_id as sequence
        FROM im_system_notification n
        WHERE n.notification_id IN
        <foreach collection="notificationIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND n.del_flag = 0
        ORDER BY n.create_time DESC
    </select>

</mapper>