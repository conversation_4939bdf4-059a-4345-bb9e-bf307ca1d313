-- =====================================================
-- 修复表情包消息存储的字符集问题
-- 将相关表的字符集从utf8修改为utf8mb4以支持emoji表情
-- =====================================================

-- 1. 修改im_message_body表字符集
-- 这是存储消息内容的核心表，需要支持emoji表情
ALTER TABLE `im_message_body` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 修改im_message_body表的具体字段字符集
ALTER TABLE `im_message_body` 
MODIFY COLUMN `message_body` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '消息内容，支持emoji表情',
MODIFY COLUMN `security_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展字段，支持emoji表情';

-- 3. 修改im_message_history表字符集（私聊消息历史）
ALTER TABLE `im_message_history` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_message_history` 
MODIFY COLUMN `from_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'from_id',
MODIFY COLUMN `to_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'to_id',
MODIFY COLUMN `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'owner_id',
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

-- 4. 修改im_group_message_history表字符集（群聊消息历史）
ALTER TABLE `im_group_message_history` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_group_message_history` 
MODIFY COLUMN `from_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'from_id',
MODIFY COLUMN `group_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'group_id',
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

-- 5. 修改im_conversation_set表字符集（会话表）
ALTER TABLE `im_conversation_set` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_conversation_set` 
MODIFY COLUMN `conversation_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `from_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `to_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- 6. 修改im_user_data表字符集（用户数据表）
ALTER TABLE `im_user_data` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_user_data` 
MODIFY COLUMN `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '昵称，支持emoji',
MODIFY COLUMN `location` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `birth_day` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `user_sex` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `self_signature` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '个性签名，支持emoji',
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

-- 7. 修改im_group表字符集（群组表）
ALTER TABLE `im_group` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_group` 
MODIFY COLUMN `group_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `group_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '群名称，支持emoji',
MODIFY COLUMN `owner_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `introduction` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '群介绍，支持emoji',
MODIFY COLUMN `notification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '群公告，支持emoji',
MODIFY COLUMN `photo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

-- 8. 修改im_group_member表字符集（群成员表）
ALTER TABLE `im_group_member` 
CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE `im_group_member` 
MODIFY COLUMN `group_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `member_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
MODIFY COLUMN `alias` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '群内昵称，支持emoji',
MODIFY COLUMN `extra` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL;

-- 9. 如果存在直播间消息表，也需要修改（该表已经是utf8mb4）
-- im_live_room_message表在创建时已经使用utf8mb4，无需修改

-- 10. 验证修改结果
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM 
    information_schema.COLUMNS 
WHERE 
    TABLE_SCHEMA = 'im-core' 
    AND TABLE_NAME IN (
        'im_message_body',
        'im_message_history', 
        'im_group_message_history',
        'im_conversation_set',
        'im_user_data',
        'im_group',
        'im_group_member'
    )
    AND CHARACTER_SET_NAME IS NOT NULL
ORDER BY 
    TABLE_NAME, COLUMN_NAME;

-- 执行完成后的说明：
-- 1. 所有相关表的字符集已从utf8修改为utf8mb4
-- 2. 现在可以正常存储emoji表情和其他4字节UTF-8字符
-- 3. 建议重启应用服务以确保连接池使用新的字符集设置
-- 4. 可以通过发送表情包消息来验证修复效果

