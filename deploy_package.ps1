# Parse command line parameters
param(
    [string[]]$Modules = @(),  # Modules to package (empty = all modules)
    [switch]$Help              # Show help information
)

# IM System Deployment Package Creation Script (PowerShell Version)
# Set console encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# Show help information
if ($Help) {
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "        IM System Deployment Script" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\deploy_package.ps1                    # Package all modules" -ForegroundColor White
    Write-Host "  .\deploy_package.ps1 im-service         # Package only im-service" -ForegroundColor White
    Write-Host "  .\deploy_package.ps1 im-tcp im-service  # Package im-tcp and im-service" -ForegroundColor White
    Write-Host "  .\deploy_package.ps1 -Help              # Show this help" -ForegroundColor White
    Write-Host ""
    Write-Host "Available modules:" -ForegroundColor Yellow
    Write-Host "  - im-tcp" -ForegroundColor White
    Write-Host "  - im-service" -ForegroundColor White
    Write-Host "  - im-message-store" -ForegroundColor White
    Write-Host ""
    exit 0
}

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "        IM System Deployment Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Set base paths
$WORKSPACE_PATH = "D:\workSpace\sst-jim"
$DEPLOY_PATH = "D:\deploy_package"
$ZIP_TOOL = "D:\appurl\7-Zip\7z.exe"
$ZIP_PASSWORD = "sport"

# SCP upload configuration
$SCP_ENABLED = $true  # Set to $false to disable SCP upload
$SCP_USER = "eric"
$SCP_HOST = "***************"
$SCP_REMOTE_PATH = "/home/<USER>/work"

# Generate timestamp (Format: YYYYMMDDHHMM)
$now = Get-Date
$TIMESTAMP = $now.ToString("yyyyMMddHHmm")
$PACKAGE_DIR = Join-Path $DEPLOY_PATH $TIMESTAMP
$ZIP_FILE = Join-Path $DEPLOY_PATH "$TIMESTAMP.zip"

Write-Host "Timestamp: $TIMESTAMP" -ForegroundColor Yellow
Write-Host "Package Dir: $PACKAGE_DIR" -ForegroundColor Yellow
Write-Host "ZIP File: $ZIP_FILE" -ForegroundColor Yellow
Write-Host ""

# Define all available JAR files
$allJarFiles = @{
    "im-tcp" = @{
        "jar" = "im-tcp.jar"
        "path" = "$WORKSPACE_PATH\im-tcp\target\im-tcp.jar"
    }
    "im-service" = @{
        "jar" = "im-service.jar"
        "path" = "$WORKSPACE_PATH\im-service\target\im-service.jar"
    }
    "im-message-store" = @{
        "jar" = "im-message-store.jar"
        "path" = "$WORKSPACE_PATH\im-message-store\target\im-message-store.jar"
    }
}

# Filter JAR files based on parameters
if ($Modules.Count -eq 0) {
    # No parameters provided, package all modules
    $jarFiles = @{}
    foreach ($module in $allJarFiles.Keys) {
        $jarFiles[$allJarFiles[$module].jar] = $allJarFiles[$module].path
    }
    Write-Host "Mode: Package ALL modules" -ForegroundColor Green
} else {
    # Parameters provided, package only specified modules
    $jarFiles = @{}
    $validModules = @()
    $invalidModules = @()

    foreach ($module in $Modules) {
        if ($allJarFiles.ContainsKey($module)) {
            $jarFiles[$allJarFiles[$module].jar] = $allJarFiles[$module].path
            $validModules += $module
        } else {
            $invalidModules += $module
        }
    }

    if ($invalidModules.Count -gt 0) {
        Write-Host "X Warning: Invalid modules specified: $($invalidModules -join ', ')" -ForegroundColor Red
        Write-Host "Available modules: $($allJarFiles.Keys -join ', ')" -ForegroundColor Yellow
    }

    if ($validModules.Count -eq 0) {
        Write-Host "X Error: No valid modules specified!" -ForegroundColor Red
        Write-Host "Available modules: $($allJarFiles.Keys -join ', ')" -ForegroundColor Yellow
        exit 1
    }

    Write-Host "Mode: Package SELECTED modules ($($validModules -join ', '))" -ForegroundColor Green
}

Write-Host "Modules to package: $($jarFiles.Keys -join ', ')" -ForegroundColor Yellow

try {
    # [1/7] Check source files
    Write-Host "[1/7] Checking source files..." -ForegroundColor Green
    $missingFiles = @()

    foreach ($jar in $jarFiles.GetEnumerator()) {
        if (-not (Test-Path $jar.Value)) {
            Write-Host "X Error: $($jar.Key) does not exist" -ForegroundColor Red
            $missingFiles += $jar.Key
        }
    }

    if ($missingFiles.Count -gt 0) {
        Write-Host ""
        Write-Host "X The following source files do not exist, please compile the project first!" -ForegroundColor Red
        $missingFiles | ForEach-Object { Write-Host "   - $_" -ForegroundColor Red }
        Read-Host "Press any key to exit"
        exit 1
    }

    Write-Host "√ All source files check passed" -ForegroundColor Green
    Write-Host ""
    
    # [2/7] Create target directory
    Write-Host "[2/7] Creating target directory..." -ForegroundColor Green

    if (Test-Path $PACKAGE_DIR) {
        Write-Host "Removing existing directory: $PACKAGE_DIR" -ForegroundColor Yellow
        Remove-Item $PACKAGE_DIR -Recurse -Force
    }

    New-Item -ItemType Directory -Path $PACKAGE_DIR -Force | Out-Null
    Write-Host "√ Directory created successfully: $PACKAGE_DIR" -ForegroundColor Green
    Write-Host ""
    
    # [3/7] Copy JAR files
    Write-Host "[3/7] Copying JAR files..." -ForegroundColor Green

    foreach ($jar in $jarFiles.GetEnumerator()) {
        Write-Host "Copying $($jar.Key)..." -ForegroundColor White
        $destinationPath = Join-Path $PACKAGE_DIR $jar.Key
        Copy-Item $jar.Value -Destination $destinationPath -Force

        if (-not (Test-Path $destinationPath)) {
            throw "Failed to copy $($jar.Key)"
        }
    }

    Write-Host "√ All JAR files copied successfully" -ForegroundColor Green
    Write-Host ""
    
    # [4/7] Verify copy results
    Write-Host "[4/7] Verifying copy results..." -ForegroundColor Green

    $copyErrors = @()
    foreach ($jar in $jarFiles.Keys) {
        $filePath = Join-Path $PACKAGE_DIR $jar
        if (Test-Path $filePath) {
            $fileSize = (Get-Item $filePath).Length
            $fileSizeMB = [math]::Round($fileSize / 1MB, 1)
            Write-Host "√ $jar copied successfully ($fileSizeMB MB)" -ForegroundColor Green
        } else {
            Write-Host "X Error: $jar copy failed" -ForegroundColor Red
            $copyErrors += $jar
        }
    }

    if ($copyErrors.Count -gt 0) {
        throw "File copy verification failed!"
    }
    Write-Host ""
    
    # [5/7] Check compression tool
    Write-Host "[5/7] Checking compression tool..." -ForegroundColor Green

    if (-not (Test-Path $ZIP_TOOL)) {
        throw "7-Zip tool not found: $ZIP_TOOL`nPlease confirm 7-Zip is properly installed"
    }

    Write-Host "√ 7-Zip tool check passed" -ForegroundColor Green
    Write-Host ""
    
    # [6/7] Create ZIP package
    Write-Host "[6/7] Creating ZIP package..." -ForegroundColor Green

    # Remove existing ZIP file
    if (Test-Path $ZIP_FILE) {
        Write-Host "Removing existing ZIP file: $ZIP_FILE" -ForegroundColor Yellow
        Remove-Item $ZIP_FILE -Force
    }

    # Switch to deployment directory and create ZIP package
    Push-Location $DEPLOY_PATH
    try {
        $zipArgs = @("a", "-tzip", "-p$ZIP_PASSWORD", $ZIP_FILE, $TIMESTAMP)
        $process = Start-Process -FilePath $ZIP_TOOL -ArgumentList $zipArgs -Wait -PassThru -NoNewWindow -RedirectStandardOutput "zip_output.log" -RedirectStandardError "zip_error.log"

        if ($process.ExitCode -ne 0) {
            $errorContent = if (Test-Path "zip_error.log") { Get-Content "zip_error.log" -Raw } else { "Unknown error" }
            throw "ZIP package creation failed: $errorContent"
        }

        # Clean up temporary log files
        @("zip_output.log", "zip_error.log") | ForEach-Object {
            if (Test-Path $_) { Remove-Item $_ -Force }
        }
    }
    finally {
        Pop-Location
    }

    Write-Host "√ ZIP package created successfully" -ForegroundColor Green
    Write-Host ""
    
    # Display final results
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "           Deployment Package Complete!" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""

    Write-Host "Package Path: $ZIP_FILE" -ForegroundColor White
    Write-Host "Password: $ZIP_PASSWORD" -ForegroundColor White
    Write-Host "Contains:" -ForegroundColor White
    Write-Host "    +-- $TIMESTAMP\" -ForegroundColor Gray

    # Display actual packaged files
    $jarFilesList = $jarFiles.Keys | Sort-Object
    for ($i = 0; $i -lt $jarFilesList.Count; $i++) {
        $isLast = ($i -eq $jarFilesList.Count - 1)
        $prefix = if ($isLast) { "    +-- " } else { "    |   +-- " }
        Write-Host "$prefix$($jarFilesList[$i])" -ForegroundColor Gray
    }
    Write-Host ""

    # Display file size information
    if (Test-Path $ZIP_FILE) {
        $zipSize = (Get-Item $ZIP_FILE).Length
        $zipSizeMB = [math]::Round($zipSize / 1MB, 1)
        Write-Host "ZIP Size: $zipSizeMB MB" -ForegroundColor White
    }

    # Display directory content statistics
    $totalFiles = (Get-ChildItem $PACKAGE_DIR -File).Count
    $totalSize = (Get-ChildItem $PACKAGE_DIR -File | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 1)
    Write-Host "Original Files: $totalFiles files, Total $totalSizeMB MB" -ForegroundColor White

    Write-Host ""
    Write-Host "√ Deployment package creation completed!" -ForegroundColor Green

    # [7/7] SCP upload to remote server
    if ($SCP_ENABLED) {
        Write-Host ""
        Write-Host "[7/7] Uploading to remote server..." -ForegroundColor Green

        # Check if SCP tool is installed (via OpenSSH or other SSH client)
        $scpCommand = Get-Command "scp" -ErrorAction SilentlyContinue
        if (-not $scpCommand) {
            Write-Host "! Warning: SCP command not found, skipping remote upload" -ForegroundColor Yellow
            Write-Host "   Please install OpenSSH client or other SSH tools" -ForegroundColor Yellow
        } else {
            try {
                $scpTarget = "${SCP_USER}@${SCP_HOST}:${SCP_REMOTE_PATH}/"
                Write-Host "Upload target: $scpTarget" -ForegroundColor White
                Write-Host "Uploading file, please enter SSH password..." -ForegroundColor Yellow

                # Execute SCP command
                $scpArgs = @($ZIP_FILE, $scpTarget)
                $scpProcess = Start-Process -FilePath "scp" -ArgumentList $scpArgs -Wait -PassThru -NoNewWindow

                if ($scpProcess.ExitCode -eq 0) {
                    Write-Host "√ File uploaded successfully!" -ForegroundColor Green
                    Write-Host "Remote path: $scpTarget$(Split-Path $ZIP_FILE -Leaf)" -ForegroundColor White
                } else {
                    Write-Host "X File upload failed (exit code: $($scpProcess.ExitCode))" -ForegroundColor Red
                    Write-Host "   Possible causes: network issues, authentication failure, or insufficient permissions" -ForegroundColor Yellow
                }
            } catch {
                Write-Host "X Error during SCP upload: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host ""
        Write-Host "i SCP upload disabled" -ForegroundColor Gray
    }

} catch {
    Write-Host ""
    Write-Host "X Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Script execution failed!" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""
Read-Host "Press any key to exit"
