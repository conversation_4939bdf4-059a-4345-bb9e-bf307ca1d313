package com.lld.im.tcp.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.lld.im.codec.pack.LoginPack;
import com.lld.im.codec.pack.message.ChatMessageAck;
import com.lld.im.codec.pack.user.LoginAckPack;
import com.lld.im.codec.pack.user.UserStatusChangeNotifyPack;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ImConnectStatusEnum;
import com.lld.im.common.enums.command.GroupEventCommand;
import com.lld.im.common.enums.command.MessageCommand;
import com.lld.im.common.enums.command.SystemCommand;
import com.lld.im.common.enums.command.UserEventCommand;
import com.lld.im.common.model.UserClientDto;
import com.lld.im.common.model.UserSession;
import com.lld.im.common.model.message.CheckSendMessageReq;
import com.lld.im.tcp.feign.FeignMessageService;
import com.lld.im.tcp.feign.FeignUserService;

import com.lld.im.tcp.publish.MqMessageProducer;
import com.lld.im.tcp.redis.RedisManager;
import com.lld.im.tcp.utils.SessionSocketHolder;
import com.lld.im.tcp.utils.AttributeKeys;
import feign.Feign;
import feign.Request;
import feign.jackson.JacksonDecoder;
import feign.jackson.JacksonEncoder;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.util.AttributeKey;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public class NettyServerHandler extends SimpleChannelInboundHandler<Message> {

    private final static Logger logger = LoggerFactory.getLogger(NettyServerHandler.class);

    // 使用统一的AttributeKeys，确保所有地方都使用相同的实例
    private static final AttributeKey<String> ATTR_USER_ID = AttributeKeys.USER_ID;
    private static final AttributeKey<Integer> ATTR_APP_ID = AttributeKeys.APP_ID;
    private static final AttributeKey<Integer> ATTR_CLIENT_TYPE = AttributeKeys.CLIENT_TYPE;
    private static final AttributeKey<String> ATTR_IMEI = AttributeKeys.IMEI;
    private static final AttributeKey<String> ATTR_CLIENT_IMEI = AttributeKeys.CLIENT_IMEI;
    private static final AttributeKey<Long> ATTR_READ_TIME = AttributeKeys.READ_TIME;
    private static final AttributeKey<Boolean> ATTR_IS_GUEST = AttributeKeys.IS_GUEST;

    private Integer brokerId;

    private String logicUrl;

    private FeignMessageService feignMessageService;

    private FeignUserService feignUserService;

    public NettyServerHandler(Integer brokerId,String logicUrl) {
        this.brokerId = brokerId;
        this.logicUrl = logicUrl;
        feignMessageService = Feign.builder()
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .options(new Request.Options(1000, 3500))//设置超时时间
                .target(FeignMessageService.class, logicUrl);

        feignUserService = Feign.builder()
                .encoder(new JacksonEncoder())
                .decoder(new JacksonDecoder())
                .options(new Request.Options(1000, 3500))//设置超时时间
                .target(FeignUserService.class, logicUrl);
    }

    //String
    //Map
    // userId client1 session
    // userId client2 session

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        super.channelActive(ctx);
    }



    @Override
    protected void channelRead0(ChannelHandlerContext ctx, Message msg) throws Exception {

        Integer command = msg.getMessageHeader().getCommand();
        //登录command
        if(command == SystemCommand.LOGIN.getCommand()){

            LoginPack loginPack = JSON.parseObject(JSONObject.toJSONString(msg.getMessagePack()),
                    new TypeReference<LoginPack>() {
                    }.getType());
            /** 登陸事件 **/
            String userId = loginPack.getUserId();

            // 在设置新UserId之前，先获取原来的UserId,为空则不处理清理数据
            String originalUserId = ctx.channel().attr(ATTR_USER_ID).get();

            /** 为channel设置用户id **/
            ctx.channel().attr(ATTR_USER_ID).set(userId);
            String clientImei = msg.getMessageHeader().getClientType() + ":" + msg.getMessageHeader().getImei();
            /** 为channel设置client和imel **/
            ctx.channel().attr(ATTR_CLIENT_IMEI).set(clientImei);
            /** 为channel设置appId **/
            ctx.channel().attr(ATTR_APP_ID).set(msg.getMessageHeader().getAppId());
            /** 为channel设置ClientType **/
            ctx.channel().attr(ATTR_CLIENT_TYPE).set(msg.getMessageHeader().getClientType());
            /** 为channel设置Imei **/
            ctx.channel().attr(ATTR_IMEI).set(msg.getMessageHeader().getImei());

            // 检查是否为游客身份
            boolean isGuest = false;
            if (loginPack.getIsGuest() != null && loginPack.getIsGuest()) {
                isGuest = true;
                // 为channel设置游客标记
                ctx.channel().attr(ATTR_IS_GUEST).set(true);
                logger.info("🎭 游客连接: userId={}, Channel={}", userId, ctx.channel().id());
            } else {
                // 正式用户登录时，检查连接之前是否存在游客标记
                Boolean wasGuest = ctx.channel().attr(ATTR_IS_GUEST).get();

                // 正式用户设置为false
                ctx.channel().attr(ATTR_IS_GUEST).set(false);

                // 只有当前连接之前是游客时，才清理游客数据
                if (wasGuest != null && wasGuest) {
                    // 使用之前保存的原始用户ID（游客ID）进行数据清理
                    if (originalUserId != null) {
                        cleanupCurrentConnectionGuestDataSimple(originalUserId, msg.getMessageHeader().getAppId());
                        logger.info("🔄 检测到游客切换为正式用户，已清理游客数据: guestId={}, newUserId={}", originalUserId, userId);
                    }
                }
            }
            
            //将channel存起来
            if (!isGuest) {
                //Redis map
                UserSession userSession = new UserSession();
                userSession.setAppId(msg.getMessageHeader().getAppId());
                userSession.setClientType(msg.getMessageHeader().getClientType());
                userSession.setUserId(loginPack.getUserId());
                userSession.setConnectState(ImConnectStatusEnum.ONLINE_STATUS.getCode());
                userSession.setBrokerId(brokerId);
                userSession.setImei(msg.getMessageHeader().getImei());

                // 设置用户昵称：优先使用客户端传递的昵称，为空时查询用户服务
                String nickname = getOrQueryUserNickname(loginPack, msg.getMessageHeader().getAppId());
                userSession.setNickname(nickname);

                logger.info("正式用户登录成功: userId={}, nickname={}, clientType={}, imei={}",
                           loginPack.getUserId(), nickname, msg.getMessageHeader().getClientType(), msg.getMessageHeader().getImei());

                try {
                    InetAddress localHost = InetAddress.getLocalHost();
                    userSession.setBrokerHost(localHost.getHostAddress());
                }catch (Exception e){
                    e.printStackTrace();
                }

                RedissonClient redissonClient = RedisManager.getRedissonClient();
                RMap<String, String> map = redissonClient.getMap(msg.getMessageHeader().getAppId() + Constants.RedisConstants.UserSessionConstants + loginPack.getUserId());
                map.put(msg.getMessageHeader().getClientType()+":" + msg.getMessageHeader().getImei()
                        ,JSONObject.toJSONString(userSession));
                SessionSocketHolder
                        .put(msg.getMessageHeader().getAppId()
                                ,loginPack.getUserId(),
                                msg.getMessageHeader().getClientType(),msg.getMessageHeader().getImei(),(NioSocketChannel) ctx.channel());

                UserClientDto dto = new UserClientDto();
                dto.setImei(msg.getMessageHeader().getImei());
                dto.setUserId(loginPack.getUserId());
                dto.setClientType(msg.getMessageHeader().getClientType());
                dto.setAppId(msg.getMessageHeader().getAppId());
                RTopic topic = redissonClient.getTopic(Constants.RedisConstants.UserLoginChannel);
                topic.publish(JSONObject.toJSONString(dto));

                UserStatusChangeNotifyPack userStatusChangeNotifyPack = new UserStatusChangeNotifyPack();
                userStatusChangeNotifyPack.setAppId(msg.getMessageHeader().getAppId());
                userStatusChangeNotifyPack.setUserId(loginPack.getUserId());
                userStatusChangeNotifyPack.setStatus(ImConnectStatusEnum.ONLINE_STATUS.getCode());
                MqMessageProducer.sendMessage(userStatusChangeNotifyPack,msg.getMessageHeader(), UserEventCommand.USER_ONLINE_STATUS_CHANGE.getCommand());
            } else {
                // 游客登录：保存Channel连接，支持昵称传递
                SessionSocketHolder.putGuestChannel(msg.getMessageHeader().getAppId(),
                        loginPack.getUserId(),
                        (NioSocketChannel) ctx.channel());

                // 处理游客昵称：优先使用客户端传递的昵称，否则使用userId
                String guestNickname = StringUtils.isNotEmpty(loginPack.getNickname())
                    ? loginPack.getNickname()
                    : loginPack.getUserId();

                // 将游客信息（包含昵称）存入Redis
                try {
                    RedissonClient redissonClient = RedisManager.getRedissonClient();

                    // 在Redis中记录游客身份和昵称
                    String guestKey = msg.getMessageHeader().getAppId() + Constants.RedisConstants.GuestUserPrefix + loginPack.getUserId();

                    // 存储游客信息，包含昵称
                    JSONObject guestInfo = new JSONObject();
                    guestInfo.put("nickname", guestNickname);
                    guestInfo.put("loginTime", System.currentTimeMillis());
                    redissonClient.getBucket(guestKey).set(guestInfo.toJSONString(), 24, TimeUnit.HOURS);

                    // 发布游客登录事件
                    UserClientDto dto = new UserClientDto();
                    dto.setImei(msg.getMessageHeader().getImei());
                    dto.setUserId(loginPack.getUserId());
                    dto.setClientType(msg.getMessageHeader().getClientType());
                    dto.setAppId(msg.getMessageHeader().getAppId());

                    RTopic guestTopic = redissonClient.getTopic(Constants.RedisConstants.GuestLoginChannel);
                    guestTopic.publish(JSONObject.toJSONString(dto));

                    logger.info("游客登录成功: userId={}, nickname={}, clientType={}, imei={}",
                               loginPack.getUserId(), guestNickname, msg.getMessageHeader().getClientType(), msg.getMessageHeader().getImei());
                } catch (Exception e) {
                    logger.error("游客信息存入Redis失败", e);
                }
            }

            MessagePack<LoginAckPack> loginSuccess = new MessagePack<>();
            LoginAckPack loginAckPack = new LoginAckPack();
            loginAckPack.setUserId(loginPack.getUserId());
            if (isGuest) {
                loginAckPack.setIsGuest(true);
            }
            loginSuccess.setCommand(SystemCommand.LOGINACK.getCommand());
            loginSuccess.setData(loginAckPack);
            loginSuccess.setImei(msg.getMessageHeader().getImei());
            loginSuccess.setAppId(msg.getMessageHeader().getAppId());

            ctx.channel().writeAndFlush(loginSuccess);

        }else if(command == SystemCommand.LOGOUT.getCommand()){
            //删除session
            //redis 删除
            // 检查是否为游客
            Boolean isGuest = ctx.channel().attr(ATTR_IS_GUEST).get();
            if (isGuest != null && isGuest) {
                // 游客注销，只需要移除channel
                SessionSocketHolder.removeGuestChannel((NioSocketChannel) ctx.channel());

                // 清除Redis中的游客标识
                try {
                    String userId = ctx.channel().attr(ATTR_USER_ID).get();
                    Integer appId = ctx.channel().attr(ATTR_APP_ID).get();

                    if (userId != null && appId != null) {
                        RedissonClient redissonClient = RedisManager.getRedissonClient();
                        String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                        redissonClient.getBucket(guestKey).delete();
                        logger.info("已清除Redis中的游客标识，用户ID：{}", userId);
                    }
                } catch (Exception e) {
                    logger.error("清除Redis中的游客标识失败", e);
                }
            } else {
                SessionSocketHolder.removeUserSession((NioSocketChannel) ctx.channel());
            }
        }else if(command == SystemCommand.PING.getCommand()){
            ctx.channel().attr(ATTR_READ_TIME).set(System.currentTimeMillis());
        }else if(command == MessageCommand.MSG_BROADCAST.getCommand()){
            // 处理广播消息
            BroadcastMessageHandler.handle(msg);
        }else if(command == MessageCommand.MSG_P2P.getCommand()
        || command == GroupEventCommand.MSG_GROUP.getCommand()){
            // 游客不允许发送消息，只能接收系统消息
            if (isGuestUser(ctx)) {
                String userId = ctx.channel().attr(ATTR_USER_ID).get();
                logger.warn("🚫 游客尝试发送消息被拒绝: userId={}, command={}", userId, command);
                return;
            }

            try {
                String toId = "";
                CheckSendMessageReq req = new CheckSendMessageReq();
                req.setAppId(msg.getMessageHeader().getAppId());
                req.setCommand(msg.getMessageHeader().getCommand());
                JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(msg.getMessagePack()));
                String fromId = jsonObject.getString("fromId");
                if(command == MessageCommand.MSG_P2P.getCommand()){
                    toId = jsonObject.getString("toId");
                }else {
                    toId = jsonObject.getString("groupId");
                }
                req.setToId(toId);
                req.setFromId(fromId);

                ResponseVO responseVO = feignMessageService.checkSendMessage(req);
                if(responseVO.isOk()){
                    MqMessageProducer.sendMessage(msg,command);
                }else{
                    Integer ackCommand = 0;
                    if(command == MessageCommand.MSG_P2P.getCommand()){
                        ackCommand = MessageCommand.MSG_ACK.getCommand();
                    }else {
                        ackCommand = GroupEventCommand.GROUP_MSG_ACK.getCommand();
                    }

                    ChatMessageAck chatMessageAck = new ChatMessageAck(jsonObject.getString("messageId"));
                    responseVO.setData(chatMessageAck);
                    MessagePack<ResponseVO> ack = new MessagePack<>();
                    ack.setData(responseVO);
                    ack.setCommand(ackCommand);
                    ctx.channel().writeAndFlush(ack);
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }else if(command >= 5010 && command <= 5021) {
            // 处理所有直播间相关指令 (5010-5021)
            JSONObject jsonObject = JSON.parseObject(JSONObject.toJSONString(msg.getMessagePack()));

            // 游客只允许加入(5012)和离开(5013)直播间，其他操作一律禁止
            if (isGuestUser(ctx)) {
                if (command == 5012) {
                    // 5012: 加入直播间 - 游客允许
                    logger.info("游客加入直播间: command={}, userId={}", command,
                            ctx.channel().attr(ATTR_USER_ID).get());
                    MqMessageProducer.sendMessage(msg,command);
                } else if (command == 5013) {
                    // 5013: 离开直播间 - 游客允许 - 直接处理
                    logger.info("游客离开直播间(直接处理): command={}, userId={}", command,
                            ctx.channel().attr(ATTR_USER_ID).get());
                    handleLiveRoomLeaveDirectly(ctx, jsonObject);
                } else {
                    // 其他所有直播间操作都禁止
                    String operationName = getOperationName(command);
                    logger.warn("游客尝试执行禁止操作被拒绝: command={} ({}), userId={}",
                            command, operationName, ctx.channel().attr(ATTR_USER_ID).get());
                    return;
                }
            } else {
                // 正式用户允许所有直播间操作
                if (command == 5013) {
                    // 5013: 离开直播间 - 正式用户 - 直接处理
                    logger.info("正式用户离开直播间(直接处理): command={}, userId={}", command,
                            ctx.channel().attr(ATTR_USER_ID).get());
                    handleLiveRoomLeaveDirectly(ctx, jsonObject);
                } else {
                    MqMessageProducer.sendMessage(msg,command);
                }
            }
        }else {
            MqMessageProducer.sendMessage(msg,command);
        }

    }

    /**
     * 获取直播间操作名称，用于日志记录
     */
    private String getOperationName(Integer command) {
        switch (command) {
            case 5010: return "发送消息";
            case 5011: return "消息ACK";
            case 5012: return "加入直播间";
            case 5013: return "离开直播间";
            case 5014: return "禁言用户";
            case 5015: return "踢出用户";
            case 5016: return "发布公告";
            case 5017: return "全员禁言";
            case 5018: return "点赞";
            case 5019: return "发送礼物";
            case 5020: return "创建直播间";
            case 5021: return "关闭直播间";
            default: return "未知操作";
        }
    }

    /**
     * 直接处理离开直播间指令
     * 在TCP层直接执行Redis清理和通知发送，提升实时性
     *
     * @param ctx 连接上下文
     * @param data 消息数据
     */
    private void handleLiveRoomLeaveDirectly(ChannelHandlerContext ctx, JSONObject messagePackData) {
        try {

            // 1. 提取用户和直播间信息
            String userId = ctx.channel().attr(ATTR_USER_ID).get();
            Integer appId = ctx.channel().attr(ATTR_APP_ID).get();
            Boolean isGuest = SessionSocketHolder.isUserGuest( appId,userId);

            // 从MessagePack的data字段中获取roomId
            String roomId = null;
            if (messagePackData.containsKey("roomId")) {
                roomId = messagePackData.getString("roomId");
            }

            // 2. 参数验证
            if (StringUtils.isEmpty(userId) || appId == null || StringUtils.isEmpty(roomId)) {
                logger.warn("⚠️ 离开直播间参数不完整: userId={}, appId={}, roomId={}, messagePackData={}",
                           userId, appId, roomId, messagePackData.toJSONString());
                return;
            }

            // 3. 执行Redis清理操作
            cleanupUserFromRedis(roomId, userId, appId, isGuest);

            // 4. 更新本地缓存
            SessionSocketHolder.removeUserFromRoom(roomId, userId, isGuest);

            // 5. 发送实时通知给其他用户（修改content为user_exit表示主动离开）
            sendLeaveNotificationToRoomDirect(roomId, userId, appId);

            logger.info("✅ 直接处理离开直播间完成: userId={}, roomId={}, 用户类型={}",
                       userId, roomId, isGuest ? "游客" : "正式用户");

        } catch (Exception e) {
            logger.error("❌ 直接处理离开直播间失败", e);
        }
    }

    //表示 channel 处于不活动状态
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        logger.info("🔌 Channel连接断开: {}", ctx.channel().id());

        try {
            // 执行完整的用户清理和通知流程
            handleUserDisconnection((NioSocketChannel) ctx.channel());
        } catch (Exception e) {
            logger.error("❌ 处理连接断开时发生异常", e);
        } finally {
            ctx.close();
        }
    }

    /**
     * 处理用户连接断开的完整流程
     * 包括用户身份识别、直播间移除、离开通知发送、连接资源清理
     *
     * @param channel 断开的连接
     */
    private void handleUserDisconnection(NioSocketChannel channel) {
        String userId = null;
        Integer appId = null;
        Boolean isGuest = null;

        try {
            // 1. 用户身份识别
            UserInfo userInfo = extractUserInfo(channel);
            if (userInfo == null) {
                logger.warn("⚠️ 无法获取用户信息，跳过断开处理: channel={}", channel.id());
                return;
            }

            userId = userInfo.getUserId();
            appId = userInfo.getAppId();
            isGuest = userInfo.getIsGuest();

            logger.info("🔌 开始处理用户断开: userId={}, appId={}, 用户类型={}, channel={}",
                       userId, appId, isGuest ? "游客" : "正式用户", channel.id());

            // 2. 获取用户所在的直播间列表（在移除前获取，用于发送通知）
            Set<String> userRooms = getUserRooms(userId);
            logger.info("📋 用户所在直播间列表: userId={}, 直播间数={}, 直播间列表={}",
                       userId, userRooms.size(), userRooms);

            // 3. 从所有直播间中移除用户
            removeUserFromAllRooms(userId, appId, isGuest);
            logger.info("🗑️ 已从所有直播间移除用户: userId={}", userId);

            // 4. 发送离开通知给直播间其他用户
            sendLeaveNotifications(userRooms, userId, appId);

            // 5. 清理连接资源
            cleanupConnectionResources(channel, userInfo);

            logger.info("✅ 用户断开处理完成: userId={}, appId={}, 清理直播间数={}",
                       userId, appId, userRooms.size());

        } catch (Exception e) {
            logger.error("❌ 处理用户断开时发生异常: userId={}, appId={}, isGuest={}",
                        userId, appId, isGuest, e);
        }
    }

    /**
     * 统一的游客身份判断方法
     * 优先使用Channel属性，备选使用统一判断服务
     *
     * @param ctx 连接上下文
     * @return 是否为游客
     */
    private boolean isGuestUser(ChannelHandlerContext ctx) {
        try {
            // 1. 优先使用Channel属性（最高效）
            Boolean isGuest = ctx.channel().attr(ATTR_IS_GUEST).get();
            if (isGuest != null) {
                return isGuest;
            }

            // 2. 备选：使用统一的游客判断服务
            String userId = ctx.channel().attr(ATTR_USER_ID).get();
            Integer appId = ctx.channel().attr(ATTR_APP_ID).get();

            if (userId != null && appId != null) {
                boolean guestStatus = SessionSocketHolder.isUserGuest(appId, userId);
                // 将判断结果缓存到Channel属性中，避免重复查询
                ctx.channel().attr(ATTR_IS_GUEST).set(guestStatus);
                return guestStatus;
            }

            return false;
        } catch (Exception e) {
            logger.warn("⚠️ 游客身份判断异常", e);
            return false;
        }
    }

    /**
     * 从Channel中提取用户信息
     *
     * @param channel 连接通道
     * @return 用户信息对象
     */
    private UserInfo extractUserInfo(NioSocketChannel channel) {
        try {
            String userId = channel.attr(ATTR_USER_ID).get();
            Integer appId = channel.attr(ATTR_APP_ID).get();
            Integer clientType = channel.attr(ATTR_CLIENT_TYPE).get();
            String imei = channel.attr(ATTR_IMEI).get();
            Boolean isGuest = channel.attr(ATTR_IS_GUEST).get();

            // 如果没有IsGuest属性，通过统一的游客判断方法确认
            if (isGuest == null && userId != null && appId != null) {
                isGuest = SessionSocketHolder.isUserGuest(appId, userId);
                // 将判断结果缓存到Channel属性中
                channel.attr(ATTR_IS_GUEST).set(isGuest);
            }

            if (userId == null || appId == null) {
                logger.warn("⚠️ Channel缺少必要的用户信息: userId={}, appId={}", userId, appId);
                return null;
            }

            return new UserInfo(userId, appId, clientType, imei, isGuest != null ? isGuest : false);

        } catch (Exception e) {
            logger.error("❌ 提取用户信息时异常: channel={}", channel.id(), e);
            return null;
        }
    }

    /**
     * 获取用户所在的直播间列表
     *
     * @param userId 用户ID
     * @return 直播间ID集合
     */
    private Set<String> getUserRooms(String userId) {
        Set<String> userRooms = new HashSet<>();

        try {
            // 从正式用户直播间中查找
            for (String roomId : SessionSocketHolder.getAllRegularUserRooms()) {
                Set<String> roomUsers = SessionSocketHolder.getRoomUsers(roomId);
                if (roomUsers != null && roomUsers.contains(userId)) {
                    userRooms.add(roomId);
                }
            }

            // 从游客直播间中查找
            for (String roomId : SessionSocketHolder.getAllGuestUserRooms()) {
                Set<String> roomUsers = SessionSocketHolder.getRoomUsers(roomId);
                if (roomUsers != null && roomUsers.contains(userId)) {
                    userRooms.add(roomId);
                }
            }

            logger.debug("用户{}所在直播间: {}", userId, userRooms);

        } catch (Exception e) {
            logger.error("❌ 获取用户直播间列表时异常: userId={}", userId, e);
        }

        return userRooms;
    }

    /**
     * 从所有直播间中移除用户
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @param isGuest 是否为游客
     */
    private void removeUserFromAllRooms(String userId, Integer appId, Boolean isGuest) {
        try {
            // 使用SessionSocketHolder的准确方法移除用户
            SessionSocketHolder.removeUserFromAllRooms(appId, userId);

            // 额外清理Redis中的用户数据
            cleanupRedisUserData(userId, appId, isGuest);

            logger.debug("✅ 已从所有直播间移除用户: userId={}, appId={}, 用户类型={}",
                        userId, appId, isGuest ? "游客" : "正式用户");

        } catch (Exception e) {
            logger.error("❌ 从直播间移除用户时异常: userId={}, appId={}", userId, appId, e);
        }
    }

    /**
     * 游客切换正式用户时的数据清理
     * 当检测到当前连接从游客切换到正式用户时调用
     *
     * 清理范围：
     * 1. Redis游客标识
     * 2. 游客连接映射
     *
     * @param guestId 游客ID（从Channel中获取的原游客ID）
     * @param appId 应用ID
     */
    private void cleanupCurrentConnectionGuestDataSimple(String guestId, Integer appId) {
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient == null) {
                logger.warn("Redis连接不可用，跳过游客数据清理: guestId={}", guestId);
                return;
            }

            // 1. 清理Redis中的游客标识
            String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + guestId;
            boolean guestExists = redissonClient.getBucket(guestKey).isExists();
            if (guestExists) {
                redissonClient.getBucket(guestKey).delete();
                logger.info("🧹 清理游客Redis标识: guestId={}, key={}", guestId, guestKey);
            }

            // 2. 清理游客连接映射
            SessionSocketHolder.removeGuestChannelByUserId(guestId, appId);
            logger.info("🧹 清理游客连接映射: guestId={}, appId={}", guestId, appId);

        } catch (Exception e) {
            logger.error("❌ 清理游客数据时异常: guestId={}, appId={}", guestId, appId, e);
        }
    }



    /**
     * 清理Redis中的用户数据
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @param isGuest 是否为游客
     */
    private void cleanupRedisUserData(String userId, Integer appId, Boolean isGuest) {
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient == null) {
                logger.warn("Redis连接不可用，跳过Redis清理: userId={}", userId);
                return;
            }

            if (isGuest) {
                // 清理游客标识
                String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                redissonClient.getBucket(guestKey).delete();
                logger.debug("清理游客Redis标识: {}", guestKey);
            }

            // 可以在这里添加其他Redis清理逻辑

        } catch (Exception e) {
            logger.error("❌ 清理Redis用户数据时异常: userId={}, appId={}", userId, appId, e);
        }
    }

    /**
     * 清理直播间Redis中的用户数据
     * 专门用于离开直播间时的Redis清理操作
     *
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param appId 应用ID
     * @param isGuest 是否为游客
     */
    private void cleanupUserFromRedis(String roomId, String userId, Integer appId, Boolean isGuest) {
        try {
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient == null) {
                logger.warn("⚠️ Redis连接不可用，跳过Redis清理: userId={}, roomId={}", userId, roomId);
                return;
            }

            // 从在线用户列表中移除
            String redisKey = isGuest
                ? Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomGuestSuffix
                : Constants.RedisConstants.LiveRoomGuestPrefix + roomId + Constants.RedisConstants.LiveRoomUserSuffix;

            RSet<String> userSet = redissonClient.getSet(redisKey);
            boolean removed = userSet.remove(userId);

            logger.debug("🗑️ 从Redis在线用户列表移除: redisKey={}, userId={}, 移除结果={}",
                        redisKey, userId, removed);

            // 如果是游客，清理详细信息
            if (isGuest != null && isGuest) {
                String guestInfoKey = String.format("liveroom:%s:guest:%s", roomId, userId);
                boolean deleted = redissonClient.getBucket(guestInfoKey).delete();

                logger.debug("🗑️ 清理游客详细信息: guestInfoKey={}, 删除结果={}", guestInfoKey, deleted);
            }

            logger.debug("✅ Redis清理完成: roomId={}, userId={}, 用户类型={}",
                        roomId, userId, isGuest ? "游客" : "正式用户");

        } catch (Exception e) {
            logger.error("❌ Redis清理失败: roomId={}, userId={}", roomId, userId, e);
        }
    }

    /**
     * 向指定直播间发送用户主动离开通知
     * 专门用于处理用户主动离开直播间的通知发送
     *
     * @param roomId 直播间ID
     * @param userId 离开的用户ID
     * @param appId 应用ID
     */
    private void sendLeaveNotificationToRoomDirect(String roomId, String userId, Integer appId) {
        try {
            // 获取直播间剩余用户（已经移除了离开的用户）
            Set<String> remainingUsers = SessionSocketHolder.getRoomUsers(roomId);
            logger.debug("📊 直播间剩余用户统计: roomId={}, 剩余用户数={}, 用户列表={}",
                       roomId, remainingUsers != null ? remainingUsers.size() : 0, remainingUsers);

            if (remainingUsers == null || remainingUsers.isEmpty()) {
                logger.info("📭 直播间{}没有剩余用户，跳过离开通知", roomId);
                return;
            }

            // 获取离开用户的昵称
            String nickname = getUserNickname(userId, appId);

            // 构建5013指令（离开直播间）的消息体 - 主动离开
            JSONObject leaveMessageData = new JSONObject();
            leaveMessageData.put("messageType", 10); // 离开消息类型
            leaveMessageData.put("action", "leave");
            leaveMessageData.put("userId", userId);
            leaveMessageData.put("fromNickname", nickname); // 添加用户昵称
            leaveMessageData.put("roomId", roomId);
            leaveMessageData.put("content", "user_exit"); // 主动离开标识

            // 构建完整的消息对象
            JSONObject standardMessage = new JSONObject();
            standardMessage.put("clientType", 0);
            standardMessage.put("command", 5013); // 离开直播间指令
            standardMessage.put("data", leaveMessageData);

            String messageJson = standardMessage.toJSONString();

            // 向直播间剩余用户发送通知
            logger.info("📢 开始发送5013主动离开通知: roomId={}, 离开用户={}({}), 目标用户数={}, 消息内容={}",
                       roomId, userId, nickname, remainingUsers.size(), messageJson);

            int successCount = 0;
            int failCount = 0;

            for (String remainingUserId : remainingUsers) {
                try {
                    logger.debug("📤 向用户发送主动离开通知: userId={}, 离开用户={}", remainingUserId, userId);
                    sendMessageToUser(appId, remainingUserId, messageJson);
                    successCount++;
                } catch (Exception e) {
                    logger.warn("❌ 向用户{}发送主动离开通知失败: roomId={}, 离开用户={}",
                               remainingUserId, roomId, userId, e);
                    failCount++;
                }
            }

            logger.info("✅ 5013主动离开通知发送完成: roomId={}, 离开用户={}({}), 成功={}, 失败={}",
                       roomId, userId, nickname, successCount, failCount);

        } catch (Exception e) {
            logger.error("❌ 向直播间发送主动离开通知时异常: roomId={}, userId={}, appId={}",
                        roomId, userId, appId, e);
        }
    }

    /**
     * 发送离开通知给直播间其他用户
     *
     * @param userRooms 用户所在的直播间列表
     * @param userId 离开的用户ID
     * @param appId 应用ID
     */
    private void sendLeaveNotifications(Set<String> userRooms, String userId, Integer appId) {
        if (userRooms == null || userRooms.isEmpty()) {
            logger.debug("用户{}没有所在的直播间，跳过离开通知", userId);
            return;
        }

        try {
            for (String roomId : userRooms) {
                sendLeaveNotificationToRoom(roomId, userId, appId);
            }

            logger.debug("✅ 已发送离开通知: userId={}, 通知直播间数={}", userId, userRooms.size());

        } catch (Exception e) {
            logger.error("❌ 发送离开通知时异常: userId={}, appId={}", userId, appId, e);
        }
    }

    /**
     * 向指定直播间发送用户离开通知
     *
     * @param roomId 直播间ID
     * @param userId 离开的用户ID
     * @param appId 应用ID
     */
    private void sendLeaveNotificationToRoom(String roomId, String userId, Integer appId) {
        try {
            // 获取直播间剩余用户（已经移除了离开的用户）
            Set<String> remainingUsers = SessionSocketHolder.getRoomUsers(roomId);
            logger.debug("📊 直播间剩余用户统计: roomId={}, 剩余用户数={}, 用户列表={}",
                       roomId, remainingUsers != null ? remainingUsers.size() : 0, remainingUsers);

            if (remainingUsers == null || remainingUsers.isEmpty()) {
                logger.info("📭 直播间{}没有剩余用户，跳过离开通知", roomId);
                return;
            }

            // 获取离开用户的昵称
            String nickname = getUserNickname(userId, appId);

            // 构建5013指令（离开直播间）的消息体
            JSONObject leaveMessageData = new JSONObject();
            leaveMessageData.put("messageType", 10); // 离开消息类型
            leaveMessageData.put("action", "leave");
            leaveMessageData.put("userId", userId);
            leaveMessageData.put("fromNickname", nickname); // 添加用户昵称
            leaveMessageData.put("roomId", roomId);
            leaveMessageData.put("content", "connection_lost");

            // 构建完整的消息对象
            JSONObject standardMessage = new JSONObject();
            standardMessage.put("clientType", 0);
            standardMessage.put("command", 5013); // 离开直播间指令
            standardMessage.put("data", leaveMessageData);

            String messageJson = standardMessage.toJSONString();

            // 向直播间剩余用户发送通知
            logger.info("📢 开始发送5013离开通知: roomId={}, 离开用户={}, 目标用户数={}, 消息内容={}",
                       roomId, userId, remainingUsers.size(), messageJson);

            int successCount = 0;
            int failCount = 0;

            for (String remainingUserId : remainingUsers) {
                try {
                    logger.debug("📤 向用户发送离开通知: userId={}, 离开用户={}", remainingUserId, userId);
                    sendMessageToUser(appId, remainingUserId, messageJson);
                    successCount++;
                } catch (Exception e) {
                    logger.warn("❌ 向用户{}发送离开通知失败: roomId={}, 离开用户={}",
                               remainingUserId, roomId, userId, e);
                    failCount++;
                }
            }

            logger.info("✅ 5013离开通知发送完成: roomId={}, 离开用户={}, 成功={}, 失败={}",
                       roomId, userId, successCount, failCount);

        } catch (Exception e) {
            logger.error("❌ 向直播间发送离开通知时异常: roomId={}, userId={}, appId={}",
                        roomId, userId, appId, e);
        }
    }

    /**
     * 向指定用户发送消息（支持正式用户和游客用户）
     *
     * @param appId 应用ID
     * @param userId 用户ID
     * @param message 消息内容（JSON字符串）
     */
    private void sendMessageToUser(Integer appId, String userId, String message) {
        try {
            boolean sent = false;
            boolean isGuest = SessionSocketHolder.isUserGuest(appId,userId);

            // 获取用户的所有连接
            List<NioSocketChannel> channels = getUserChannels(appId, userId, isGuest);

            if (channels != null && !channels.isEmpty()) {
                for (NioSocketChannel channel : channels) {
                    if (channel != null && channel.isActive()) {
                        if (sendToChannel(channel, message)) {
                            sent = true;
                        }
                    }
                }
            } else {
                logger.debug("{}{}没有找到有效连接", isGuest ? "游客" : "正式用户", userId);
            }

            if (!sent) {
                logger.debug("⚠️ 消息发送失败，用户可能已断开连接: userId={}, 用户类型={}",
                           userId, isGuest ? "游客" : "正式用户");
            }

        } catch (Exception e) {
            logger.error("❌ 向用户发送消息时异常: userId={}, appId={}", userId, appId, e);
        }
    }

    /**
     * 获取用户的所有连接（统一处理游客和正式用户）
     *
     * @param appId 应用ID
     * @param userId 用户ID
     * @param isGuest 是否为游客
     * @return 用户的连接列表
     */
    private List<NioSocketChannel> getUserChannels(Integer appId, String userId, boolean isGuest) {
        if (isGuest) {
            // 游客用户：返回单个连接的列表
            NioSocketChannel guestChannel = SessionSocketHolder.getGuestChannel(appId, userId);
            if (guestChannel != null) {
                return Collections.singletonList(guestChannel);
            }
            return null;
        } else {
            // 正式用户：返回所有连接
            return SessionSocketHolder.get(appId, userId);
        }
    }

    /**
     * 向指定Channel发送消息
     *
     * @param channel 目标Channel
     * @param data 消息数据（JSON字符串）
     * @return 是否发送成功
     */
    private boolean sendToChannel(NioSocketChannel channel, String data) {
        if (channel == null || !channel.isActive()) {
            return false;
        }
        try {
            return sendWebSocketMessage(channel, data);
        } catch (Exception e) {
            logger.error("❌ 发送消息到Channel异常: channel={}", channel.id(), e);
            return false;
        }
    }

    /**
     * 发送WebSocket消息
     */
    private boolean sendWebSocketMessage(NioSocketChannel channel, String data) {
        try {
            JSONObject jsonMsg = JSON.parseObject(data);
            Integer command = jsonMsg.getInteger("command");

            if (command == null) {
                logger.error("❌ 消息中缺少command字段: data={}", data);
                return false;
            }

            // 构建MessagePack对象
            MessagePack<Object> messagePack = new MessagePack<>();
            messagePack.setCommand(command);
            messagePack.setData(jsonMsg);

            // 发送消息
            channel.writeAndFlush(messagePack);
            return true;
        } catch (Exception e) {
            logger.error("❌ 发送WebSocket消息失败: channel={}", channel.id(), e);
            return false;
        }
    }



    /**
     * 清理连接资源
     *
     * @param channel 连接通道
     * @param userInfo 用户信息
     */
    private void cleanupConnectionResources(NioSocketChannel channel, UserInfo userInfo) {
        try {
            if (userInfo.getIsGuest()) {
                // 游客连接清理
                cleanupGuestConnection(channel, userInfo);
            } else {
                // 正式用户连接清理
                cleanupRegularUserConnection(channel, userInfo);
            }

            logger.debug("✅ 连接资源清理完成: userId={}, 用户类型={}",
                        userInfo.getUserId(), userInfo.getIsGuest() ? "游客" : "正式用户");

        } catch (Exception e) {
            logger.error("❌ 清理连接资源时异常: userId={}, appId={}",
                        userInfo.getUserId(), userInfo.getAppId(), e);
        }
    }

    /**
     * 清理游客连接
     *
     * @param channel 连接通道
     * @param userInfo 用户信息
     */
    private void cleanupGuestConnection(NioSocketChannel channel, UserInfo userInfo) {
        try {
            // 移除游客Channel连接
            SessionSocketHolder.removeGuestChannel(channel);

            // 清除Redis中的游客标识
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient != null) {
                String guestKey = userInfo.getAppId() + Constants.RedisConstants.GuestUserPrefix + userInfo.getUserId();
                redissonClient.getBucket(guestKey).delete();
            }

            logger.debug("游客连接清理完成: userId={}, appId={}", userInfo.getUserId(), userInfo.getAppId());

        } catch (Exception e) {
            logger.error("❌ 清理游客连接时异常: userId={}", userInfo.getUserId(), e);
        }
    }

    /**
     * 清理正式用户连接
     *
     * @param channel 连接通道
     * @param userInfo 用户信息
     */
    private void cleanupRegularUserConnection(NioSocketChannel channel, UserInfo userInfo) {
        try {
            // 调用SessionSocketHolder的removeUserSession方法
            SessionSocketHolder.removeUserSession(channel);

            logger.debug("正式用户连接清理完成: userId={}, appId={}", userInfo.getUserId(), userInfo.getAppId());

        } catch (Exception e) {
            logger.error("❌ 清理正式用户连接时异常: userId={}", userInfo.getUserId(), e);
        }
    }

    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        private String userId;
        private Integer appId;
        private Integer clientType;
        private String imei;
        private Boolean isGuest;

        public UserInfo(String userId, Integer appId, Integer clientType, String imei, Boolean isGuest) {
            this.userId = userId;
            this.appId = appId;
            this.clientType = clientType;
            this.imei = imei;
            this.isGuest = isGuest;
        }

        public String getUserId() { return userId; }
        public Integer getAppId() { return appId; }
        public Integer getClientType() { return clientType; }
        public String getImei() { return imei; }
        public Boolean getIsGuest() { return isGuest; }
    }

    /**
     * 用户信息请求类
     */
    private static class UserInfoRequest {
        private String userId;
        private Integer appId;

        public UserInfoRequest(String userId, Integer appId) {
            this.userId = userId;
            this.appId = appId;
        }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public Integer getAppId() { return appId; }
        public void setAppId(Integer appId) { this.appId = appId; }
    }

    /**
     * 获取或查询用户昵称（登录时使用）
     * 优先使用客户端传递的昵称，为空时查询用户服务
     * @param loginPack 登录包
     * @param appId 应用ID
     * @return 用户昵称
     */
    private String getOrQueryUserNickname(LoginPack loginPack, Integer appId) {
        // 优先使用客户端传递的昵称
        if (StringUtils.isNotEmpty(loginPack.getNickname())) {
            logger.debug("使用客户端传递的昵称: userId={}, nickname={}", loginPack.getUserId(), loginPack.getNickname());
            return loginPack.getNickname();
        }

        // 客户端未传递昵称，查询用户服务
        logger.debug("客户端未传递昵称，查询用户服务: userId={}", loginPack.getUserId());
        return queryUserNickname(loginPack.getUserId(), appId);
    }

    /**
     * 登录时查询用户昵称（仅用于正式用户）
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户昵称
     */
    private String queryUserNickname(String userId, Integer appId) {
        try {
            UserInfoRequest request = new UserInfoRequest(userId, appId);
            ResponseVO userInfoResp = feignUserService.getUserInfo(request);
            if (userInfoResp != null && userInfoResp.isOk() && userInfoResp.getData() != null) {
                Map<String, Object> userData = (Map<String, Object>) userInfoResp.getData();
                String nickname = (String) userData.get("nickName");
                if (StringUtils.isNotEmpty(nickname)) {
                    logger.debug("查询到正式用户昵称: userId={}, nickname={}", userId, nickname);
                    return nickname;
                }
            }
        } catch (Exception e) {
            logger.warn("查询正式用户昵称失败，使用userId作为默认值: userId={}, error={}", userId, e.getMessage());
        }

        // 如果查询失败，使用userId作为默认昵称
        return userId;
    }

    /**
     * 获取用户昵称
     * 游客用户：直接使用userId作为昵称
     * 正式用户：优先从Session缓存获取，失败时调用用户服务
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户昵称
     */
    private String getUserNickname(String userId, Integer appId) {
        if (StringUtils.isEmpty(userId)) {
            return "用户";
        }

        try {
            // 判断是否为游客用户
            boolean isGuest = SessionSocketHolder.isUserGuest(appId, userId);

            if (isGuest) {
                // 游客用户：尝试从Redis获取昵称，失败时使用userId
                try {
                    RedissonClient redissonClient = RedisManager.getRedissonClient();
                    String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                    RBucket<String> bucket = redissonClient.getBucket(guestKey);
                    String guestInfoStr = bucket.get();

                    if (StringUtils.isNotEmpty(guestInfoStr)) {
                        JSONObject guestInfo = JSONObject.parseObject(guestInfoStr);
                        String nickname = guestInfo.getString("nickname");
                        if (StringUtils.isNotEmpty(nickname)) {
                            logger.debug("从Redis获取到游客昵称: userId={}, nickname={}", userId, nickname);
                            return nickname;
                        }
                    }
                } catch (Exception e) {
                    logger.debug("获取游客昵称失败，使用userId: userId={}, error={}", userId, e.getMessage());
                }

                // 如果获取失败，使用userId作为默认昵称
                return userId;
            } else {
                // 正式用户：优先从Session缓存获取昵称
                try {
                    // 首先尝试从Redis Session中获取昵称
                    RedissonClient redissonClient = RedisManager.getRedissonClient();
                    RMap<String, String> sessionMap = redissonClient.getMap(appId + Constants.RedisConstants.UserSessionConstants + userId);

                    // 遍历所有设备的Session，查找昵称信息
                    for (String sessionData : sessionMap.values()) {
                        if (StringUtils.isNotEmpty(sessionData)) {
                            UserSession userSession = JSONObject.parseObject(sessionData, UserSession.class);
                            if (userSession != null && StringUtils.isNotEmpty(userSession.getNickname())) {
                                logger.debug("从Session缓存获取到正式用户昵称: userId={}, nickname={}", userId, userSession.getNickname());
                                return userSession.getNickname();
                            }
                        }
                    }

                    // 如果Session中没有昵称，尝试通过用户服务获取
                    logger.debug("Session中未找到昵称，尝试通过用户服务获取: userId={}", userId);
                    UserInfoRequest request = new UserInfoRequest(userId, appId);
                    ResponseVO userInfoResp = feignUserService.getUserInfo(request);
                    if (userInfoResp != null && userInfoResp.isOk() && userInfoResp.getData() != null) {
                        Map<String, Object> userData = (Map<String, Object>) userInfoResp.getData();
                        String nickname = (String) userData.get("nickName");
                        if (StringUtils.isNotEmpty(nickname)) {
                            logger.debug("通过用户服务获取到正式用户昵称: userId={}, nickname={}", userId, nickname);
                            return nickname;
                        }
                    }
                } catch (Exception e) {
                    logger.debug("获取正式用户昵称失败，使用userId: userId={}, error={}", userId, e.getMessage());
                }

                // 如果所有方式都失败，使用userId作为默认昵称
                return userId;
            }
        } catch (Exception e) {
            logger.warn("⚠️ 获取用户昵称失败: userId={}, appId={}, error={}", userId, appId, e.getMessage());
            return userId; // 失败时返回userId作为默认值
        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {


    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        logger.error("❌ Channel异常: {}, 异常信息: {}", ctx.channel().id(), cause.getMessage(), cause);

        try {
            // 异常时也需要执行完整的用户清理流程
            handleUserDisconnection((NioSocketChannel) ctx.channel());
        } catch (Exception e) {
            logger.error("❌ 异常处理中的清理操作失败", e);
        } finally {
            ctx.close();
        }
    }
}
