package com.lld.im.service.config;

import com.lld.im.common.constant.Constants;
import com.lld.im.service.liveroom.sharding.LiveRoomShardingConfig;
import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 直播间RabbitMQ配置类
 * 专门用于配置直播间相关的交换机、队列和绑定关系
 */
@Configuration
public class LiveRoomRabbitMQConfig {

    @Autowired
    private LiveRoomShardingConfig shardingConfig;

    /**
     * 直播间消息交换机
     */
    @Bean
    public DirectExchange liveRoomExchange() {
        return new DirectExchange(Constants.RabbitConstants.LiveRoomExchange, true, false);
    }

    /**
     * 动态创建直播间分片队列
     */
    @Bean
    public List<Queue> liveRoomShardingQueues() {
        List<Queue> queues = new ArrayList<>();
        
        // 如果未启用分片，则只创建一个队列
        if (!shardingConfig.getEnabled()) {
            Queue queue = QueueBuilder.durable(Constants.RabbitConstants.LiveRoomQueue)
                    .build();
            queues.add(queue);
            return queues;
        }
        
        // 根据配置的分片数量创建多个队列
        int shardCount = shardingConfig.getShardCount();
        for (int i = 0; i < shardCount; i++) {
            Queue queue = QueueBuilder.durable(Constants.RabbitConstants.LiveRoomQueue + "." + i)
                    .build();
            queues.add(queue);
        }
        
        return queues;
    }

    /**
     * 动态创建直播间分片队列绑定
     */
    @Bean
    public List<Binding> liveRoomShardingBindings() {
        List<Binding> bindings = new ArrayList<>();
        
        // 如果未启用分片，则只创建一个绑定，使用通配符
        if (!shardingConfig.getEnabled()) {
            Binding binding = BindingBuilder.bind(liveRoomShardingQueues().get(0))
                    .to(liveRoomExchange())
                    .with(Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + "*");
            bindings.add(binding);
            return bindings;
        }
        
        // 根据配置的分片数量创建多个绑定
        int shardCount = shardingConfig.getShardCount();
        List<Queue> queues = liveRoomShardingQueues();
        
        for (int i = 0; i < shardCount; i++) {
            Binding binding = BindingBuilder.bind(queues.get(i))
                    .to(liveRoomExchange())
                    .with(Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + i);
            bindings.add(binding);
        }
        
        return bindings;
    }
} 