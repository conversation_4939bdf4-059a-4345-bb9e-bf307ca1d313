package com.lld.im.service.group.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.utils.SigAPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;

import javax.annotation.Resource;
import java.util.*;

/**
 * 第三方关注天数检查服务
 * @author: lld
 * @description: 调用第三方API检查用户关注作者的天数
 */
@Service
@Slf4j
public class FocusDayCheckService {

    @Resource
    private RestTemplate restTemplate;

    @Value("${third-party.focus-day.api-url:https://www.ano999.com/gateway-api/mk/op/anon/getFocusDay}")
    private String focusDayApiUrl;

    @Value("${third-party.focus-day.timeout:5000}")
    private int requestTimeout;

    @Value("${appConfig.privateKey:}")
    private String privateKey;

    @Value("${appConfig.appId:10000}")
    private long appId;

    /**
     * 检查用户关注作者的天数
     * @param userId 用户ID
     * @param authorId 作者ID（群主ID）
     * @return 关注天数，-1表示未关注或检查失败
     */
    public int checkFocusDay(String userId, String authorId) {
        log.info("开始检查用户关注天数，用户: {}, 作者: {}", userId, authorId);

        try {
            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("groupOwnerId", authorId);
            requestBody.put("memberIds", Arrays.asList(userId));

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "IM-Service/1.0");

            // 添加用户签名头（与JIM签名一致）
            String userSign = generateUserSign(authorId);
            if (userSign != null) {
                headers.add("X-User-Sign", userSign);
                log.debug("添加用户签名头，用户: {}, 签名: {}", userId, userSign);
            }

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            log.debug("发送关注天数检查请求，URL: {}, 参数: {}", focusDayApiUrl, requestBody);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                focusDayApiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                return parseResponse(response.getBody(), userId, authorId);
            } else {
                log.warn("关注天数检查请求失败，HTTP状态码: {}, 用户: {}, 作者: {}", 
                    response.getStatusCode(), userId, authorId);
                return -1;
            }

        } catch (RestClientException e) {
            log.error("调用关注天数检查API异常，用户: {}, 作者: {}, 异常: {}", 
                userId, authorId, e.getMessage(), e);
            return -1;
        } catch (Exception e) {
            log.error("检查关注天数时发生未知异常，用户: {}, 作者: {}, 异常: {}", 
                userId, authorId, e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 解析API响应
     * @param responseBody 响应体
     * @param userId 用户ID
     * @param authorId 作者ID
     * @return 关注天数，-1表示解析失败或未关注
     */
    private int parseResponse(String responseBody, String userId, String authorId) {
        try {
            log.debug("解析关注天数检查响应，用户: {}, 作者: {}, 响应: {}", userId, authorId, responseBody);

            JSONObject responseJson = JSON.parseObject(responseBody);

            // 检查响应状态码
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 200) {
                String message = responseJson.getString("message");
                log.warn("关注天数检查API返回错误，用户: {}, 作者: {}, 错误码: {}, 错误信息: {}",
                    userId, authorId, code, message);
                return -1;
            }

            // 检查success字段
            Boolean success = responseJson.getBoolean("success");
            if (success == null || !success) {
                String message = responseJson.getString("message");
                log.warn("关注天数检查API返回失败状态，用户: {}, 作者: {}, 错误信息: {}",
                    userId, authorId, message);
                return -1;
            }

            // 解析result部分
            JSONObject result = responseJson.getJSONObject("result");
            if (result == null) {
                log.warn("关注天数检查API响应中缺少result字段，用户: {}, 作者: {}", userId, authorId);
                return -1;
            }

            // 获取focusResult对象
            JSONObject focusResult = result.getJSONObject("focusResult");
            if (focusResult == null) {
                log.warn("关注天数检查API响应中缺少focusResult字段，用户: {}, 作者: {}", userId, authorId);
                return -1;
            }

            // 获取指定用户的关注天数
            Integer focusDays = focusResult.getInteger(userId);
            if (focusDays == null) {
                log.warn("关注天数检查API响应中缺少用户{}的关注天数，作者: {}", userId, authorId);
                return -1;
            }

            log.info("成功获取用户关注天数，用户: {}, 作者: {}, 关注天数: {}", userId, authorId, focusDays);
            return focusDays;

        } catch (Exception e) {
            log.error("解析关注天数检查响应异常，用户: {}, 作者: {}, 响应: {}, 异常: {}",
                userId, authorId, responseBody, e.getMessage(), e);
            return -1;
        }
    }

    /**
     * 检查用户是否关注了作者
     * @param userId 用户ID
     * @param authorId 作者ID
     * @return 是否关注
     */
    public boolean isFollowing(String userId, String authorId) {
        int focusDays = checkFocusDay(userId, authorId);
        return focusDays >= 0;
    }

    /**
     * 检查用户关注作者是否满足指定天数
     * @param userId 用户ID
     * @param authorId 作者ID
     * @param requiredDays 要求的关注天数
     * @return 是否满足要求
     */
    public boolean checkFocusDaysRequirement(String userId, String authorId, int requiredDays) {
        int focusDays = checkFocusDay(userId, authorId);
        if (focusDays < 0) {
            return false; // 未关注或检查失败
        }
        return focusDays >= requiredDays;
    }

    /**
     * 批量检查用户关注作者的天数
     * @param userIds 用户ID列表
     * @param authorId 作者ID（群主ID）
     * @return 用户ID到关注天数的映射，-1表示未关注或检查失败
     */
    public Map<String, Integer> batchCheckFocusDay(List<String> userIds, String authorId) {
        Map<String, Integer> result = new HashMap<>();

        if (userIds == null || userIds.isEmpty()) {
            log.warn("批量检查关注天数时用户ID列表为空，作者: {}", authorId);
            return result;
        }

        log.info("开始批量检查用户关注天数，用户数量: {}, 作者: {}", userIds.size(), authorId);

        try {
            // 构建请求参数
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("groupOwnerId", authorId);
            requestBody.put("memberIds", userIds);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.add("User-Agent", "IM-Service/1.0");

            // 添加用户签名头（与JIM签名一致）
            String userSign = generateUserSign(authorId);
            if (userSign != null) {
                headers.add("X-User-Sign", userSign);
                log.debug("添加用户签名头，作者: {}, 签名: {}", authorId, userSign);
            }

            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

            log.debug("发送批量关注天数检查请求，URL: {}, 参数: {}", focusDayApiUrl, requestBody);

            // 发送POST请求
            ResponseEntity<String> response = restTemplate.exchange(
                focusDayApiUrl,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                result = parseBatchResponse(response.getBody(), userIds, authorId);
            } else {
                log.warn("批量关注天数检查请求失败，HTTP状态码: {}, 作者: {}",
                    response.getStatusCode(), authorId);
                // 返回所有用户都是-1的结果
                for (String userId : userIds) {
                    result.put(userId, -1);
                }
            }

        } catch (RestClientException e) {
            log.error("调用批量关注天数检查API异常，作者: {}, 异常: {}",
                authorId, e.getMessage(), e);
            // 返回所有用户都是-1的结果
            for (String userId : userIds) {
                result.put(userId, -1);
            }
        } catch (Exception e) {
            log.error("批量检查关注天数时发生未知异常，作者: {}, 异常: {}",
                authorId, e.getMessage(), e);
            // 返回所有用户都是-1的结果
            for (String userId : userIds) {
                result.put(userId, -1);
            }
        }

        return result;
    }

    /**
     * 解析批量API响应
     * @param responseBody 响应体
     * @param userIds 用户ID列表
     * @param authorId 作者ID
     * @return 用户ID到关注天数的映射
     */
    private Map<String, Integer> parseBatchResponse(String responseBody, List<String> userIds, String authorId) {
        Map<String, Integer> result = new HashMap<>();

        try {
            log.debug("解析批量关注天数检查响应，作者: {}, 响应: {}", authorId, responseBody);

            JSONObject responseJson = JSON.parseObject(responseBody);

            // 检查响应状态码
            Integer code = responseJson.getInteger("code");
            if (code == null || code != 200) {
                String message = responseJson.getString("message");
                log.warn("批量关注天数检查API返回错误，作者: {}, 错误码: {}, 错误信息: {}",
                    authorId, code, message);
                // 返回所有用户都是-1的结果
                for (String userId : userIds) {
                    result.put(userId, -1);
                }
                return result;
            }

            // 检查success字段
            Boolean success = responseJson.getBoolean("success");
            if (success == null || !success) {
                String message = responseJson.getString("message");
                log.warn("批量关注天数检查API返回失败状态，作者: {}, 错误信息: {}",
                    authorId, message);
                // 返回所有用户都是-1的结果
                for (String userId : userIds) {
                    result.put(userId, -1);
                }
                return result;
            }

            // 解析result部分
            JSONObject resultObj = responseJson.getJSONObject("result");
            if (resultObj == null) {
                log.warn("批量关注天数检查API响应中缺少result字段，作者: {}", authorId);
                // 返回所有用户都是-1的结果
                for (String userId : userIds) {
                    result.put(userId, -1);
                }
                return result;
            }

            // 获取focusResult对象
            JSONObject focusResult = resultObj.getJSONObject("focusResult");
            if (focusResult == null) {
                log.warn("批量关注天数检查API响应中缺少focusResult字段，作者: {}", authorId);
                // 返回所有用户都是-1的结果
                for (String userId : userIds) {
                    result.put(userId, -1);
                }
                return result;
            }

            // 解析每个用户的关注天数
            for (String userId : userIds) {
                Integer focusDays = focusResult.getInteger(userId);
                if (focusDays != null) {
                    result.put(userId, focusDays);
                    log.debug("用户关注天数，用户: {}, 作者: {}, 关注天数: {}", userId, authorId, focusDays);
                } else {
                    result.put(userId, -1);
                    log.warn("批量关注天数检查API响应中缺少用户{}的关注天数，作者: {}", userId, authorId);
                }
            }

            log.info("成功解析批量关注天数检查响应，作者: {}, 用户数量: {}", authorId, result.size());
            return result;

        } catch (Exception e) {
            log.error("解析批量关注天数检查响应异常，作者: {}, 响应: {}, 异常: {}",
                authorId, responseBody, e.getMessage(), e);
            // 返回所有用户都是-1的结果
            for (String userId : userIds) {
                result.put(userId, -1);
            }
            return result;
        }
    }

    /**
     * 生成用户签名（与JIM签名一致）
     * @param userId 用户ID
     * @return 用户签名，生成失败返回null
     */
    private String generateUserSign(String userId) {
        try {
            if (privateKey == null || privateKey.isEmpty()) {
                log.warn("私钥未配置，无法生成用户签名，用户: {}", userId);
                return null;
            }

            // 创建SigAPI实例
            SigAPI sigAPI = new SigAPI(appId, privateKey);

            // 生成用户签名，有效期设置为1小时（3600秒）
            long expireSeconds = 3600L;
            String userSign = sigAPI.genUserSig(userId, expireSeconds);

            if (userSign != null && !userSign.isEmpty()) {
                log.debug("成功生成用户签名，用户: {}, 有效期: {}秒", userId, expireSeconds);
                return userSign;
            } else {
                log.warn("生成用户签名失败，返回空字符串，用户: {}", userId);
                return null;
            }

        } catch (Exception e) {
            log.error("生成用户签名时发生异常，用户: {}, 异常: {}", userId, e.getMessage(), e);
            return null;
        }
    }
}
