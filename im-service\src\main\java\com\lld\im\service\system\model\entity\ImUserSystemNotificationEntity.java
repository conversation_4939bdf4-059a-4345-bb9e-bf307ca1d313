package com.lld.im.service.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("im_user_system_notification")
public class ImUserSystemNotificationEntity {
    
    @TableId
    private Long id;                     // 主键
    
    private Integer appId;               // 应用ID
    
    private String userId;               // 用户ID
    
    private Long notificationId;         // 通知ID
    
    private Long sequence;               // 序列号
    
    private Integer readStatus;          // 读取状态 0未读 1已读
    
    private Long readTime;               // 读取时间
    
    private Long createTime;             // 创建时间
} 