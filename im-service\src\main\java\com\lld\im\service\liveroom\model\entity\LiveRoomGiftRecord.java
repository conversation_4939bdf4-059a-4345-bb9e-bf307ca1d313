package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间礼物记录实体类
 */
@Data
@TableName("im_live_room_gift_record")
public class LiveRoomGiftRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 记录ID
     */
    private String recordId;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 礼物ID
     */
    private String giftId;

    /**
     * 赠送者ID
     */
    private String senderId;

    /**
     * 接收者ID
     */
    private String receiverId;

    /**
     * 礼物数量
     */
    private Integer giftCount;

    /**
     * 礼物单价(金币)
     */
    private Integer giftPrice;

    /**
     * 总价(金币)
     */
    private Integer totalPrice;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 赠送时间
     */
    private Date sendTime;

    /**
     * 创建时间
     */
    private Date createTime;
} 