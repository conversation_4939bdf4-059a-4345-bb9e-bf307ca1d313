package com.lld.im.service.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Locale;

/**
 * 国际化消息工具类
 * 
 * @description: 提供统一的国际化消息获取方法，支持多语言环境
 * @author: IM System
 * @version: 1.0
 */
@Component
public class MessageUtils {

    private static final Logger logger = LoggerFactory.getLogger(MessageUtils.class);

    @Autowired
    private MessageSource messageSource;

    // 静态实例，用于在非Spring管理的类中使用
    private static MessageUtils instance;

    /**
     * 初始化静态实例
     */
    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 获取静态实例
     * 
     * @return MessageUtils实例
     */
    public static MessageUtils getInstance() {
        return instance;
    }

    /**
     * 获取国际化消息
     * 
     * @param code 消息键
     * @param args 消息参数
     * @return 国际化后的消息
     */
    public String getMessage(String code, Object... args) {
        try {
            Locale locale = LocaleContextHolder.getLocale();
            if (locale == null) {
                locale = Locale.SIMPLIFIED_CHINESE;
            }
            
            String message = messageSource.getMessage(code, args, code, locale);
            logger.debug("获取国际化消息: code={}, locale={}, message={}", code, locale, message);
            return message;
            
        } catch (Exception e) {
            logger.warn("获取国际化消息失败: code={}, args={}, error={}", code, args, e.getMessage());
            // 如果获取失败，返回消息键本身
            return code;
        }
    }

    /**
     * 获取错误消息
     * 
     * @param errorCode 错误码
     * @param args      消息参数
     * @return 国际化后的错误消息
     */
    public String getErrorMessage(String errorCode, Object... args) {
        // 错误消息的键格式为: error.{errorCode}
        return getMessage("error." + errorCode, args);
    }

    /**
     * 获取验证消息
     * 
     * @param validationCode 验证码
     * @param args           消息参数
     * @return 国际化后的验证消息
     */
    public String getValidationMessage(String validationCode, Object... args) {
        // 验证消息的键格式为: validation.{validationCode}
        return getMessage("validation." + validationCode, args);
    }

    /**
     * 获取业务消息
     * 
     * @param businessCode 业务码
     * @param args         消息参数
     * @return 国际化后的业务消息
     */
    public String getBusinessMessage(String businessCode, Object... args) {
        // 业务消息的键格式为: business.{businessCode}
        return getMessage("business." + businessCode, args);
    }

    /**
     * 检查消息键是否存在
     * 
     * @param code   消息键
     * @param locale 语言环境
     * @return 是否存在
     */
    public boolean hasMessage(String code, Locale locale) {
        try {
            if (locale == null) {
                locale = LocaleContextHolder.getLocale();
                if (locale == null) {
                    locale = Locale.SIMPLIFIED_CHINESE;
                }
            }
            
            String message = messageSource.getMessage(code, null, null, locale);
            return message != null;
            
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取当前语言环境
     * 
     * @return 当前语言环境
     */
    public Locale getCurrentLocale() {
        Locale locale = LocaleContextHolder.getLocale();
        return locale != null ? locale : Locale.SIMPLIFIED_CHINESE;
    }

    /**
     * 格式化消息（支持占位符）
     * 
     * @param template 消息模板
     * @param args     参数
     * @return 格式化后的消息
     */
    public String formatMessage(String template, Object... args) {
        try {
            return String.format(template, args);
        } catch (Exception e) {
            logger.warn("格式化消息失败: template={}, args={}, error={}", template, args, e.getMessage());
            return template;
        }
    }
}
