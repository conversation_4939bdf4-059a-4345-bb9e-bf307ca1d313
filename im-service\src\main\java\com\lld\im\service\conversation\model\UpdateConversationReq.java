package com.lld.im.service.conversation.model;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 更新会话请求
 */
@ApiModel(description = "更新会话请求模型")
@Data
public class UpdateConversationReq extends RequestBase {

    @ApiModelProperty(value = "会话ID", example = "conv_123456", notes = "要更新的会话唯一标识")
    private String conversationId;

    @ApiModelProperty(value = "是否静音", example = "0", notes = "0-不静音 1-静音")
    private Integer isMute;

    @ApiModelProperty(value = "是否置顶", example = "0", notes = "0-不置顶 1-置顶")
    private Integer isTop;

    @ApiModelProperty(value = "用户ID", example = "user123", notes = "执行更新操作的用户ID")
    private String fromId;

}
