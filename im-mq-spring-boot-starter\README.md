# IM MQ Spring Boot Starter

IM系统消息队列Spring Boot Starter，用于简化业务方集成IM系统的MQ消息发送功能。

## 功能特性

- **开箱即用**：引入依赖即可使用，无需复杂配置
- **消息类型**：支持系统通知和直播间消息发送
- **精准推送**：支持全员、普通用户、游客用户等不同目标群体
- **配置简单**：只需配置RabbitMQ连接信息和交换机名称
- **参数验证**：内置消息参数验证，确保数据完整性
- **日志完善**：详细的日志记录，便于问题排查
- **异常处理**：完善的异常处理机制

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>com.lld.im</groupId>
    <artifactId>im-mq-spring-boot-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
# RabbitMQ基础配置，如果项目中已经配置了RabbitMQ，则无需重复配置
spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    publisher-confirm-type: correlated
    publisher-returns: true

# IM MQ配置
im:
  mq:
    enabled: true
    notification:
      exchange: im.notification.exchange
      routing-key: im.notification
    liveroom:
      exchange: im.liveroom.exchange
      routing-key: im.liveroom
```

### 3. 使用示例

```java
@RestController
public class MessageController {
    
    @Autowired
    private ImMessageSender imMessageSender;
    
    /**
     * 发送系统通知给所有用户
     */
    @PostMapping("/notification/all")
    public String sendNotificationToAll() {
        ImSystemNotificationMessage message = new ImSystemNotificationMessage();
        message.setAppId(10000);
        message.setTitle("系统维护通知");
        message.setContent("系统将于今晚22:00-24:00进行维护");
        message.setNotificationType(ImSystemNotificationMessage.NotificationType.SYSTEM_MAINTENANCE);
        
        imMessageSender.sendSystemNotification(message);
        return "发送成功";
    }
    
    /**
     * 发送系统通知给普通用户
     */
    @PostMapping("/notification/normal")
    public String sendNotificationToNormalUsers() {
        ImSystemNotificationMessage message = new ImSystemNotificationMessage();
        message.setAppId(10000);
        message.setTitle("会员活动通知");
        message.setContent("新用户注册即可获得7天免费会员");
        message.setNotificationType(ImSystemNotificationMessage.NotificationType.ACTIVITY_PROMOTION);
        
        imMessageSender.sendSystemNotificationToNormalUsers(message);
        return "发送成功";
    }
    
    /**
     * 发送直播间消息
     */
    @PostMapping("/liveroom/message")
    public String sendLiveRoomMessage() {
        ImLiveRoomMessage message = new ImLiveRoomMessage();
        message.setAppId(10000);
        message.setRoomId("room123");
        message.setFromId("user123");
        message.setFromNickname("张三");
        message.setFromAvatar("https://example.com/avatar/user123.jpg");
        message.setMessageType(ImLiveRoomMessage.MessageType.TEXT);
        message.setContent("大家好，我是新来的！");
        
        imMessageSender.sendLiveRoomMessage(message);
        return "发送成功";
    }
}
```

## 消息类型

### 系统通知消息

支持以下通知类型根据业务需要扩展即可：
- `SYSTEM_MAINTENANCE(1)`：系统维护通知


支持以下目标类型：
- `ALL_USERS(1)`：所有用户
- `NORMAL_USERS(2)`：普通用户
- `GUEST_USERS(3)`：游客用户

### 直播间消息

支持以下消息类型：
- `TEXT(1)`：文本消息
- `IMAGE(2)`：图片消息
- `VOICE(3)`：语音消息
- `VIDEO(4)`：视频消息
- `EMOJI(5)`：表情消息
- `GIFT(6)`：礼物消息
- `LIKE(7)`：点赞消息
- `SYSTEM(8)`：系统消息
- `JOIN(9)`：加入消息
- `LEAVE(10)`：离开消息

## 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `im.mq.enabled` | Boolean | true | 是否启用IM MQ功能 |
| `im.mq.notification.exchange` | String | im.notification.exchange | 系统通知交换机名称 |
| `im.mq.notification.routing-key` | String | im.notification | 系统通知路由键 |
| `im.mq.liveroom.exchange` | String | im.liveroom.exchange | 直播间消息交换机名称 |
| `im.mq.liveroom.routing-key` | String | im.liveroom | 直播间消息路由键 |

## 高级用法

### 批量发送消息

```java
// 批量发送系统通知
List<ImSystemNotificationMessage> notifications = Arrays.asList(
    createNotification("通知1", "内容1"),
    createNotification("通知2", "内容2")
);
imMessageSender.sendBatchSystemNotification(notifications);

// 批量发送直播间消息
List<ImLiveRoomMessage> messages = Arrays.asList(
    createLiveRoomMessage("room1", "消息1"),
    createLiveRoomMessage("room2", "消息2")
);
imMessageSender.sendBatchLiveRoomMessage(messages);
```

### 指定接收用户

```java
ImSystemNotificationMessage message = new ImSystemNotificationMessage();
message.setTitle("个人通知");
message.setContent("您的账户余额不足");
message.setNotificationType(ImSystemNotificationMessage.NotificationType.PERSONAL_ACCOUNT);

// 指定接收用户列表
List<String> receiverIds = Arrays.asList("user001", "user002", "user003");
message.setReceiverIds(receiverIds);

imMessageSender.sendSystemNotification(message);
```

### 扩展字段使用

```java
ImLiveRoomMessage message = new ImLiveRoomMessage();
// ... 设置基本字段

// 添加扩展字段
Map<String, Object> extra = new HashMap<>();
extra.put("giftId", "gift001");
extra.put("giftName", "玫瑰花");
extra.put("giftCount", 1);
extra.put("giftPrice", 10);
message.setExtra(extra);

imMessageSender.sendLiveRoomMessage(message);
```

## 注意事项

1. **消息ID**：如果不设置messageId，系统会自动生成UUID
2. **时间戳**：如果不设置timestamp，系统会自动设置为当前时间
3. **参数验证**：所有必填字段都会进行验证，验证失败会抛出异常
4. **异常处理**：发送失败会抛出`ImMessageException`异常
5. **日志记录**：所有操作都有详细的日志记录

