package com.lld.im.service.system.config;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 通知类型配置类
 * @description: 管理通知类型名称映射和类型枚举
 * @author: lld
 * @version: 1.0
 */
@Component
public class NotificationTypeConfig {

    /**
     * 通知类型名称映射
     * 注意：类型7(赛事通知)不存储到数据库，仅实时推送，所以不在此映射中
     */
    private static final Map<Integer, String> TYPE_NAMES;

    static {
        Map<Integer, String> typeNames = new HashMap<>();
        typeNames.put(1, "系统消息");
        typeNames.put(2, "竞猜通知");
        typeNames.put(3, "我的关注");
        typeNames.put(4, "点赞评论");
        typeNames.put(5, "直播提醒");
        typeNames.put(6, "活动消息");
        typeNames.put(7, "赛事事件通知");
        typeNames.put(8, "赛事通知");
        TYPE_NAMES = Collections.unmodifiableMap(typeNames);
    }

    /**
     * 获取通知类型名称
     * @param type 通知类型
     * @return 类型名称
     */
    public String getTypeName(Integer type) {
        return TYPE_NAMES.getOrDefault(type, "未知类型");
    }

    /**
     * 获取所有支持的通知类型
     * @return 通知类型集合
     */
    public Set<Integer> getAllTypes() {
        return TYPE_NAMES.keySet();
    }

    /**
     * 检查通知类型是否有效
     * @param type 通知类型
     * @return 是否有效
     */
    public boolean isValidType(Integer type) {
        return TYPE_NAMES.containsKey(type);
    }

    /**
     * 通知类型常量
     */
    public static class NotificationType {
        public static final int SYSTEM_MESSAGE = 1;      // 系统消息
        public static final int GUESS_NOTIFICATION = 2;  // 竞猜通知
        public static final int MY_FOLLOW = 3;           // 我的关注
        public static final int LIKE_COMMENT = 4;        // 点赞评论
        public static final int LIVE_REMINDER = 5;       // 直播提醒
        public static final int ACTIVITY_MESSAGE = 6;    // 活动消息
        public static final int SPORTS_NOTIFICATION = 7; // 赛事通知（不存储）
        public static final int TIPS_NOTIFICATION = 8;   // 赛事通知
    }
}
