
spring:
  profiles:
    active: prod
  autoconfigure:
    exclude:
      - org.redisson.spring.starter.RedissonAutoConfiguration



# logger 配置
logging:
  config: classpath:logback-spring.xml
  level:
    com.lld.im: INFO
    org.springframework.data.redis: WARN
    org.springframework.amqp: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"


mybatis-plus:

  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_EMPTY

#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
