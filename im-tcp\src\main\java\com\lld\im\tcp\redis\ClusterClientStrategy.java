package com.lld.im.tcp.redis;

import com.lld.im.codec.config.BootstrapConfig;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.Map;
import java.util.HashMap;

/**
 * Redis集群模式连接策略
 * 
 * @author: lld
 * @version: 1.0
 */
public class ClusterClientStrategy implements RedisClientStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(ClusterClientStrategy.class);

    @Override
    public RedissonClient getRedissonClient(BootstrapConfig.RedisConfig redisConfig) {
        logger.info("初始化Redis集群连接...");
        
        Config config = new Config();
        ClusterServersConfig clusterConfig = config.useClusterServers();
        
        // 添加集群节点
        if (redisConfig.getCluster() != null && redisConfig.getCluster().getNodes() != null) {
            for (String node : redisConfig.getCluster().getNodes()) {
                String nodeAddress = node.startsWith("redis://") ? node : "redis://" + node;
                clusterConfig.addNodeAddress(nodeAddress);
                logger.info("添加Redis集群节点: {}", nodeAddress);
            }
        } else {
            throw new IllegalArgumentException("Redis集群模式下必须配置nodes节点列表");
        }
        
        // 设置密码
        if (StringUtils.isNotBlank(redisConfig.getPassword())) {
            clusterConfig.setPassword(redisConfig.getPassword());
        }
        
        // 设置基本参数
        clusterConfig.setTimeout(redisConfig.getTimeout())
                    .setMasterConnectionPoolSize(redisConfig.getPoolSize())
                    .setSlaveConnectionPoolSize(redisConfig.getPoolSize())
                    .setMasterConnectionMinimumIdleSize(redisConfig.getPoolMinIdle())
                    .setSlaveConnectionMinimumIdleSize(redisConfig.getPoolMinIdle())
                    .setConnectTimeout(redisConfig.getPoolConnTimeout())
                    // 优化连接管理参数，减少PING超时问题
                    .setIdleConnectionTimeout(120000)   // 空闲连接超时2分钟
                    .setPingConnectionInterval(60000)   // PING间隔增加到60秒
                    .setKeepAlive(true)                 // 启用TCP keepalive
                    .setTcpNoDelay(true);               // 禁用Nagle算法，减少延迟
        
        // 设置集群特有参数
        if (redisConfig.getCluster().getScanInterval() != null) {
            clusterConfig.setScanInterval(redisConfig.getCluster().getScanInterval());
        }

        // 注意：Redisson 3.15.6版本的ClusterServersConfig没有setMaxRedirections方法
        // maxRedirects参数在集群配置中会使用默认值

        // 检测是否为远程连接并进行相应配置
        boolean isRemoteConnection = detectRemoteConnection(redisConfig);
        if (isRemoteConnection) {
            logger.info("检测到远程Redis集群连接，应用远程连接优化配置");
            setupRemoteConnectionOptimization(clusterConfig);
        } else {
            logger.info("检测到本地Redis集群连接，应用本地连接配置");
            // 设置NAT映射 - 解决本地Docker容器主机名解析问题
            setupNatMapping(clusterConfig);
        }

        // 设置编码器
        StringCodec stringCodec = new StringCodec();
        config.setCodec(stringCodec);
        
        logger.info("Redis集群连接配置完成，节点数量: {}", redisConfig.getCluster().getNodes().size());
        return Redisson.create(config);
    }
    
    /**
     * 设置NAT映射，解决Docker容器主机名解析问题
     * 将容器内部主机名映射到外部可访问的地址
     */
    private void setupNatMapping(ClusterServersConfig clusterConfig) {
        try {
            // 创建NAT映射表 - 将容器内部地址映射到外部地址
            Map<String, String> natMap = new HashMap<>();
            natMap.put("redis-node-1:6379", "127.0.0.1:7000");
            natMap.put("redis-node-2:6379", "127.0.0.1:7001");
            natMap.put("redis-node-3:6379", "127.0.0.1:7002");

            // 尝试使用Redisson的NAT映射功能
            try {
                // 检查是否有setNatMap方法
                java.lang.reflect.Method setNatMapMethod = clusterConfig.getClass().getMethod("setNatMap", Map.class);
                setNatMapMethod.invoke(clusterConfig, natMap);
                logger.info("已设置Redis集群NAT映射: {}", natMap);
                return;
            } catch (NoSuchMethodException e) {
                logger.info("当前Redisson版本不支持setNatMap方法，尝试其他方法");
            } catch (Exception e) {
                logger.warn("设置NAT映射失败: {}", e.getMessage());
            }

            // 尝试使用natMapper方法
            try {
                java.lang.reflect.Method natMapperMethod = clusterConfig.getClass().getMethod("setNatMapper",
                    Class.forName("org.redisson.api.NatMapper"));
                // 如果有natMapper方法，需要创建NatMapper实现
                logger.info("发现natMapper方法，但需要实现NatMapper接口");
            } catch (Exception e) {
                logger.debug("natMapper方法不可用: {}", e.getMessage());
            }

            // 如果都不支持，提示用户配置hosts
            logger.warn("当前Redisson版本不支持NAT映射功能");
            logger.warn("请确保系统hosts文件包含以下映射：");
            logger.warn("127.0.0.1 redis-node-1");
            logger.warn("127.0.0.1 redis-node-2");
            logger.warn("127.0.0.1 redis-node-3");
            logger.warn("并且应用配置使用外部端口：localhost:7000, localhost:7001, localhost:7002");

        } catch (Exception e) {
            logger.error("设置NAT映射失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 检测是否为远程连接
     */
    private boolean detectRemoteConnection(BootstrapConfig.RedisConfig redisConfig) {
        if (redisConfig.getCluster() == null || redisConfig.getCluster().getNodes() == null) {
            return false;
        }

        for (String node : redisConfig.getCluster().getNodes()) {
            String host = node.split(":")[0];
            // 检测是否为本地地址
            if (!host.equals("localhost") && !host.equals("127.0.0.1") && !host.equals("::1")) {
                logger.info("检测到远程地址: {}", host);
                return true;
            }
        }
        return false;
    }

    /**
     * 设置远程连接优化配置
     */
    private void setupRemoteConnectionOptimization(ClusterServersConfig clusterConfig) {
        try {
            // 远程连接优化参数 - 使用反射调用可能存在的方法
            try {
                // 尝试设置失败槽位重连间隔
                java.lang.reflect.Method method1 = clusterConfig.getClass().getMethod("setFailedSlotReconnectionInterval", int.class);
                method1.invoke(clusterConfig, 5000);
                logger.debug("设置失败槽位重连间隔: 5000ms");
            } catch (Exception e) {
                logger.debug("setFailedSlotReconnectionInterval方法不可用");
            }

            try {
                // 尝试设置槽位检查间隔
                java.lang.reflect.Method method2 = clusterConfig.getClass().getMethod("setSlotCheckInterval", int.class);
                method2.invoke(clusterConfig, 10000);
                logger.debug("设置槽位检查间隔: 10000ms");
            } catch (Exception e) {
                logger.debug("setSlotCheckInterval方法不可用");
            }

            // 设置订阅连接池参数（这些方法通常可用）
            clusterConfig
                .setSubscriptionConnectionPoolSize(25)      // 订阅连接池大小
                .setSubscriptionConnectionMinimumIdleSize(5); // 订阅连接最小空闲数

            logger.info("已应用远程Redis集群连接优化配置");
            logger.info("建议监控网络延迟和连接池使用情况");

        } catch (Exception e) {
            logger.warn("设置远程连接优化配置时出现警告: {}", e.getMessage());
        }
    }

    /**
     * 设置主机名映射（备用方案）
     */
    private void setupHostsMapping() {
        logger.info("尝试设置系统主机名映射...");
        // 这里可以添加系统hosts文件修改逻辑，但需要管理员权限
        // 或者提示用户手动添加hosts映射
        logger.warn("请在系统hosts文件中添加以下映射：");
        logger.warn("127.0.0.1 redis-node-1");
        logger.warn("127.0.0.1 redis-node-2");
        logger.warn("127.0.0.1 redis-node-3");
    }

    @Override
    public String getStrategyName() {
        return "cluster";
    }
}
