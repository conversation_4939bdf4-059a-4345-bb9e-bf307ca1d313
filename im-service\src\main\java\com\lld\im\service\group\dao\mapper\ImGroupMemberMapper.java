package com.lld.im.service.group.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.group.dao.ImGroupMemberEntity;
import com.lld.im.service.group.model.req.GroupMemberDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ImGroupMemberMapper extends BaseMapper<ImGroupMemberEntity> {

    @Select("select group_id from im_group_member where app_id = #{appId} AND member_id = #{memberId} ")
    public List<String> getJoinedGroupId(Integer appId, String memberId);

    @Select("select group_id from im_group_member where app_id = #{appId} AND member_id = #{memberId} and role != #{role}" )
    public List<String> syncJoinedGroupId(Integer appId, String memberId, int role);

    /**
     * 获取用户所有相关的群组ID（包括已退出的），用于增量同步
     * @param appId 应用ID
     * @param memberId 用户ID
     * @return 用户所有相关的群组ID列表
     */
    @Select("select DISTINCT group_id from im_group_member where app_id = #{appId} AND member_id = #{memberId}")
    public List<String> getAllRelatedGroupId(Integer appId, String memberId);

    /**
     * 获取用户群组同步信息（联合查询群组信息和成员状态）
     * @param appId 应用ID
     * @param memberId 用户ID
     * @param lastSequence 上次同步的序列号
     * @param maxLimit 最大返回数量
     * @return 群组同步信息列表
     */
    @Select("<script>" +
            "SELECT " +
            "g.group_id, g.group_name, g.group_type, g.owner_id, g.apply_join_type, " +
            "g.introduction, g.notification, g.photo, g.max_member_count, g.status, " +
            "g.sequence, g.create_time, g.update_time, g.extra, " +
            "gm.role as member_role, gm.join_time, gm.leave_time " +
            "FROM im_group g " +
            "INNER JOIN im_group_member gm ON g.group_id = gm.group_id AND g.app_id = gm.app_id " +
            "WHERE g.app_id = #{appId} AND gm.member_id = #{memberId} " +
            "AND g.sequence &gt; #{lastSequence} " +
            "ORDER BY g.sequence ASC " +
            "LIMIT #{maxLimit}" +
            "</script>")
    @Results({
            @Result(column = "group_id", property = "groupInfo.groupId"),
            @Result(column = "group_name", property = "groupInfo.groupName"),
            @Result(column = "group_type", property = "groupInfo.groupType"),
            @Result(column = "owner_id", property = "groupInfo.ownerId"),
            @Result(column = "apply_join_type", property = "groupInfo.applyJoinType"),
            @Result(column = "introduction", property = "groupInfo.introduction"),
            @Result(column = "notification", property = "groupInfo.notification"),
            @Result(column = "photo", property = "groupInfo.photo"),
            @Result(column = "max_member_count", property = "groupInfo.maxMemberCount"),
            @Result(column = "status", property = "groupInfo.status"),
            @Result(column = "sequence", property = "groupInfo.sequence"),
            @Result(column = "create_time", property = "groupInfo.createTime"),
            @Result(column = "update_time", property = "groupInfo.updateTime"),
            @Result(column = "extra", property = "groupInfo.extra"),
            @Result(column = "member_role", property = "memberRole"),
            @Result(column = "join_time", property = "joinTime"),
            @Result(column = "leave_time", property = "leaveTime"),
            @Result(column = "sequence", property = "groupSequence")
    })
    public List<com.lld.im.service.group.model.resp.GroupSyncResp> syncGroupWithMemberStatus(
            @Param("appId") Integer appId,
            @Param("memberId") String memberId,
            @Param("lastSequence") Long lastSequence,
            @Param("maxLimit") Integer maxLimit);


    @Results({
            @Result(column = "member_id", property = "memberId"),
//            @Result(column = "speak_flag", property = "speakFlag"),
            @Result(column = "speak_date", property = "speakDate"),
            @Result(column = "role", property = "role"),
            @Result(column = "alias", property = "alias"),
            @Result(column = "join_time", property = "joinTime"),
            @Result(column = "join_type", property = "joinType")
    })
    @Select("select " +
            " member_id, " +
//            " speak_flag,  " +
            " speak_date,  " +
            " role, " +
            " alias, " +
            " join_time ," +
            " join_type " +
            " from im_group_member where app_id = #{appId} AND group_id = #{groupId} ")
    public List<GroupMemberDto> getGroupMember(Integer appId, String groupId);

    @Select("select " +
            " member_id " +
            " from im_group_member where app_id = #{appId} AND group_id = #{groupId} and role != 3")
    public List<String> getGroupMemberId(Integer appId, String groupId);


    @Results({
            @Result(column = "member_id", property = "memberId"),
//            @Result(column = "speak_flag", property = "speakFlag"),
            @Result(column = "role", property = "role")
//            @Result(column = "alias", property = "alias"),
//            @Result(column = "join_time", property = "joinTime"),
//            @Result(column = "join_type", property = "joinType")
    })
    @Select("select " +
            " member_id, " +
//            " speak_flag,  " +
            " role " +
//            " alias, " +
//            " join_time ," +
//            " join_type " +
            " from im_group_member where app_id = #{appId} AND group_id = #{groupId} and role in (1,2) ")
    public List<GroupMemberDto> getGroupManager(String groupId, Integer appId);

}
