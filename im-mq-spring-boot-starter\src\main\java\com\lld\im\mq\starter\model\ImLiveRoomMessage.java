package com.lld.im.mq.starter.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * IM直播间消息模型
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImLiveRoomMessage extends ImBaseMessage {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 直播间ID
     */
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;
    
    /**
     * 发送者ID
     */
    @NotBlank(message = "发送者ID不能为空")
    private String fromId;
    
    /**
     * 发送者昵称
     */
    private String fromNickname;
    
    /**
     * 发送者头像
     */
    private String fromAvatar;
    
    /**
     * 消息类型
     * 1-文本消息, 2-图片消息, 3-语音消息, 4-视频消息, 5-表情消息, 
     * 6-礼物消息, 7-点赞消息, 8-系统消息, 9-加入消息, 10-离开消息
     */
    @NotNull(message = "消息类型不能为空")
    private Integer messageType;
    
    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;
    
    /**
     * 客户端类型(可选)
     *     WEBAPI(0,"webApi"),
     *     WEB(1,"web"),
     *     IOS(2,"ios"),
     *     ANDROID(3,"android"),
     *     WINDOWS(4,"windows"),
     *     MAC(5,"mac"),
     */
    private Integer clientType;
    
    /**
     * 设备标识(可选)
     */
    private String imei;

    
    /**
     * 消息类型常量
     */
    public static class MessageType {
        public static final int TEXT = 1;        // 文本消息
        public static final int IMAGE = 2;       // 图片消息
        public static final int VOICE = 3;       // 语音消息
        public static final int VIDEO = 4;       // 视频消息
        public static final int EMOJI = 5;       // 表情消息
        public static final int GIFT = 6;        // 礼物消息
        public static final int LIKE = 7;        // 点赞消息
        public static final int SYSTEM = 8;      // 系统消息
        public static final int JOIN = 9;        // 加入消息
        public static final int LEAVE = 10;      // 离开消息
    }
    

}
