package com.lld.im.service.group.service;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.SyncResp;
import com.lld.im.service.group.dao.ImGroupEntity;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.model.resp.GetGroupSpeakPermissionResp;
import com.lld.im.service.group.model.resp.GetGroupJoinThresholdResp;
import com.lld.im.service.group.model.resp.GroupSyncResp;
import com.lld.im.service.group.model.resp.GroupsCreatedByUserPageResp;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public interface ImGroupService {

    public ResponseVO importGroup(ImportGroupReq req);

    public ResponseVO createGroup(CreateGroupReq req);

    public ResponseVO updateBaseGroupInfo(UpdateGroupReq req);

    public ResponseVO getJoinedGroup(GetJoinedGroupReq req);

    public ResponseVO destroyGroup(DestroyGroupReq req);

    public ResponseVO transferGroup(TransferGroupReq req);

    public ResponseVO<ImGroupEntity> getGroup(String groupId, Integer appId);

    public ResponseVO getGroup(GetGroupReq req);

    public ResponseVO muteGroup(MuteGroupReq req);

    ResponseVO syncJoinedGroupList(SyncReq req);

    /**
     * 增量同步群组列表V2（包含退出群组信息）
     * 修复版本：能够返回用户退出的群组信息，确保客户端数据一致性
     * @param req 同步请求
     * @return 群组同步响应，包含群组信息和用户状态
     */
    ResponseVO<SyncResp<GroupSyncResp>> syncJoinedGroupListV2(SyncReq req);

    Long getUserGroupMaxSeq(String userId, Integer appId);

    /**
     * 设置群组发言权限
     * @param req 设置权限请求
     * @return 操作结果
     */
    ResponseVO setGroupSpeakPermission(SetGroupSpeakPermissionReq req);

    /**
     * 获取群组发言权限配置
     * @param groupId 群组ID
     * @param appId 应用ID
     * @return 权限配置信息
     */
    ResponseVO<GetGroupSpeakPermissionResp> getGroupSpeakPermission(String groupId, Integer appId);

    /**
     * 设置群组进群门槛
     * @param req 设置门槛请求
     * @return 操作结果
     */
    ResponseVO setGroupJoinThreshold(SetGroupJoinThresholdReq req);

    /**
     * 获取群组进群门槛配置
     * @param groupId 群组ID
     * @param appId 应用ID
     * @return 门槛配置信息
     */
    ResponseVO<GetGroupJoinThresholdResp> getGroupJoinThreshold(String groupId, Integer appId);

    /**
     * 获取指定用户创建的群组列表
     * @param targetUserId 目标用户ID
     * @param appId 应用ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param currentUserId 当前登录用户ID
     * @return 群组列表分页结果
     */
    ResponseVO<GroupsCreatedByUserPageResp> getGroupsCreatedByUser(String targetUserId, Integer appId, Integer pageNum, Integer pageSize, String currentUserId);
}
