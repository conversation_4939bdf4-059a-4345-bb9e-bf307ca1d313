package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * IM系统通知消息模型
 *
 * <AUTHOR>
 */
@ApiModel(description = "IM系统通知消息模型")
@Data
@EqualsAndHashCode(callSuper = true)
public class ImSystemNotificationMessage extends ImBaseMessage {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "通知标题", required = true, example = "系统维护通知", notes = "系统通知的标题")
    private String title;

    @ApiModelProperty(value = "通知内容", required = true, example = "系统将于今晚22:00-24:00进行维护", notes = "系统通知的详细内容")
    private String content;

    @ApiModelProperty(value = "通知类型 (1:系统维护 2:活动推广 3:功能更新)", required = true, example = "1", notes = "系统通知的分类类型")
    private Integer notificationType;

    @ApiModelProperty(value = "接收者ID列表 (空=广播所有用户)", example = "[\"user123\", \"user456\"]", notes = "指定接收通知的用户ID列表，为空表示广播给所有用户")
    private List<String> receiverIds;

    @ApiModelProperty(value = "目标类型 (1:所有用户 2:普通用户 3:游客用户)", example = "1", notes = "通知的目标用户群体，默认为1")
    private Integer targetType = 1;

    @ApiModelProperty(value = "相关链接", example = "https://example.com/notice", notes = "通知相关的链接地址（可选）")
    private String url;


    /**
     * 目标类型常量
     */
    public static class TargetType {
        public static final int ALL_USERS = 1;      // 所有用户
        public static final int NORMAL_USERS = 2;   // 普通用户
        public static final int GUEST_USERS = 3;    // 游客用户
    }
}
