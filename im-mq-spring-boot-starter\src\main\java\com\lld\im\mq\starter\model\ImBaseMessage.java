package com.lld.im.mq.starter.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * IM消息基础模型
 * 
 * <AUTHOR>
 */
@Data
public class ImBaseMessage implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID，如果不设置会自动生成UUID
     */
    private String messageId;
    
    /**
     * 应用ID
     */
    private Integer appId;
    
    /**
     * 时间戳，如果不设置会自动生成当前时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Long timestamp;
    
    /**
     * 扩展字段，用于传递额外的业务数据
     */
    private Map<String, Object> extra;
}
