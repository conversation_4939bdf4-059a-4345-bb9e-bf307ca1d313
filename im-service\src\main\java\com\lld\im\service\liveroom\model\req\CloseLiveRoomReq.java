package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 关闭直播间请求模型
 * 
 * @description: 关闭直播间操作的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "关闭直播间请求模型")
@Data
public class CloseLiveRoomReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要关闭的直播间唯一标识")
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;

    @ApiModelProperty(value = "关闭原因", example = "直播结束", notes = "关闭直播间的具体原因，可选参数")
    private String reason;

}
