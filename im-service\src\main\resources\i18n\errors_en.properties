# Error message resource file (English)
# 错误消息资源文件（英文）

# Basic errors
error.success=Success
error.system.error=Internal server error, please contact administrator
error.parameter.error=Parameter validation error
error.method.not.supported=Request method not supported

# User related errors
error.user.import.size.beyond=Import quantity exceeds limit
error.user.not.exist=User does not exist
error.user.server.get.failed=Failed to get user from server
error.user.modify.failed=Failed to update user
error.user.server.not.available=No available service

# Message related errors
error.message.sender.forbidden=Sender is forbidden
error.message.sender.muted=Sender is muted
error.message.body.not.exist=Message body does not exist
error.message.recall.timeout=Message recall time has expired
error.message.already.recalled=Message has already been recalled

# Friendship errors
error.friendship.import.size.beyond=Import quantity exceeds limit
error.friendship.add.failed=Failed to add friend
error.friendship.already.friend=Target is already your friend
error.friendship.not.friend=Target is not your friend
error.friendship.deleted=Friend has been deleted
error.friendship.blacklisted=Friend has been blacklisted
error.friendship.target.blacklisted.you=Target has blacklisted you
error.friendship.relation.not.exist=Relationship record does not exist
error.friendship.add.black.failed=Failed to add to blacklist
error.friendship.not.in.blacklist=Friend is no longer in your blacklist
error.friendship.cannot.approve.others=Cannot approve other people's friend requests
error.friendship.request.not.exist=Friend request does not exist
error.friendship.group.create.failed=Failed to create friend group
error.friendship.group.already.exist=Friend group already exists
error.friendship.group.not.exist=Friend group does not exist
error.friendship.non.friend.msg.limit.exceeded=Non-friend message limit exceeded, please wait for reply

# Group related errors
error.group.import.size.beyond=Import quantity exceeds limit
error.group.not.exist=Group does not exist
error.group.already.exist=Group already exists
error.group.create.failed=Failed to create group
error.group.member.not.exist=Group member does not exist
error.group.member.already.exist=Group member already exists
error.group.owner.cannot.leave=Group owner cannot leave group
error.group.permission.denied=Permission denied
error.group.member.limit.exceeded=Group member limit exceeded
error.group.join.threshold.config.error=Group join threshold configuration error
error.group.join.threshold.follow.not.enough=Follow time insufficient, cannot join group
error.group.join.threshold.follow.days.not.enough=Follow days insufficient, cannot join group
error.group.join.threshold.check.failed=Group join threshold validation failed

# Conversation related errors
error.conversation.update.param.error=Conversation update parameter error

# Live room related errors
error.liveroom.param.error=Parameter error
error.liveroom.not.exist=Live room does not exist
error.liveroom.user.not.in.room=User is not in the live room
error.liveroom.room.muted=Live room is muted for all users
error.liveroom.user.muted=You have been muted
error.liveroom.no.permission=Permission denied
error.liveroom.operation.failed=Operation failed
error.liveroom.guest.operation.not.allowed=Guest operation not allowed
error.liveroom.room.full=Live room is full
error.liveroom.user.not.exist=User does not exist
error.liveroom.room.ids.empty=Live room ID list cannot be empty
error.liveroom.room.ids.limit.exceeded=The number of live rooms queried at one time cannot exceed 100
error.liveroom.valid.room.ids.empty=Valid live room ID list cannot be empty
error.liveroom.rooms.not.found=Specified live rooms not found
error.liveroom.get.recent.messages.failed=Failed to get recent messages
error.liveroom.get.online.users.failed=Failed to get online user list
error.liveroom.batch.check.failed=Failed to batch check live room status
error.liveroom.cannot.mute.higher.role=Cannot mute users with higher or equal role
error.liveroom.cannot.kick.higher.role=Cannot kick users with higher or equal role

# Gateway related errors
error.gateway.user.sign.not.exist=User signature does not exist
error.gateway.app.id.not.exist=AppId does not exist
error.gateway.operator.not.exist=Operator does not exist
error.gateway.user.sign.error=User signature is incorrect
error.gateway.user.sign.operator.not.match=User signature does not match operator
error.gateway.user.sign.expired=User signature has expired

# Parameter validation errors
validation.user.id.not.blank=User ID cannot be blank
validation.app.id.not.null=App ID cannot be null
validation.group.id.not.blank=Group ID cannot be blank
validation.group.name.not.blank=Group name cannot be blank
validation.member.id.not.blank=Member ID cannot be blank
validation.member.ids.not.empty=Member ID list cannot be empty
validation.speak.date.not.null=Speak date cannot be null
validation.keyword.not.blank=Search keyword cannot be blank
validation.keyword.size=Search keyword length must be between 1-100 characters
validation.conversation.id.not.blank=Conversation ID cannot be blank
validation.room.id.not.blank=Room ID cannot be blank
validation.room.name.not.blank=Room name cannot be blank
validation.anchor.id.not.blank=Anchor ID cannot be blank
validation.announcement.not.blank=Announcement content cannot be blank
validation.from.id.not.blank=Sender ID cannot be blank
validation.content.not.blank=Message content cannot be blank
validation.message.type.not.null=Message type cannot be null
validation.mute.status.not.null=Mute status cannot be null
validation.room.ids.not.empty=Room ID list cannot be empty
validation.room.ids.size=Number of rooms per query cannot exceed 100
validation.order.by.pattern=Order by must be ASC or DESC
validation.fragment.length.min=Fragment length cannot be less than 10
validation.fragment.length.max=Fragment length cannot be greater than 200
validation.mute.all.pattern=Mute all status must be 0 or 1
validation.need.review.pattern=Review status must be 0 or 1
validation.apply.reason.size=Apply reason cannot exceed 500 characters
