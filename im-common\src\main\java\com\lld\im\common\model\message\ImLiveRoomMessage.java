package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * IM直播间消息模型
 *
 * <AUTHOR>
 */
@ApiModel(description = "IM直播间消息模型")
@Data
@EqualsAndHashCode(callSuper = true)
public class ImLiveRoomMessage extends ImBaseMessage {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "直播间的唯一标识")
    private String roomId;

    @ApiModelProperty(value = "发送者ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "发送者昵称", example = "张三", notes = "发送者在直播间显示的昵称")
    private String fromNickname;

    @ApiModelProperty(value = "发送者头像", example = "https://example.com/avatar.jpg", notes = "发送者头像图片地址")
    private String fromAvatar;

    @ApiModelProperty(value = "消息类型 (1:文本 2:图片 3:语音 4:视频 5:表情 6:礼物 7:点赞 8:系统 9:加入 10:离开)", required = true, example = "1", notes = "直播间消息的类型标识")
    private Integer messageType;

    @ApiModelProperty(value = "消息内容", required = true, example = "大家好！", notes = "消息的具体内容")
    private String content;

    @ApiModelProperty(value = "客户端类型 (0:webApi 1:web 2:ios 3:android 4:windows 5:mac)", example = "1", notes = "发送消息的客户端类型")
    private Integer clientType;

    @ApiModelProperty(value = "设备标识", example = "device123", notes = "设备的唯一标识符")
    private String imei;

    
    /**
     * 消息类型常量
     */
    public static class MessageType {
        public static final int TEXT = 1;        // 文本消息
        public static final int IMAGE = 2;       // 图片消息
        public static final int VOICE = 3;       // 语音消息
        public static final int VIDEO = 4;       // 视频消息
        public static final int EMOJI = 5;       // 表情消息
        public static final int GIFT = 6;        // 礼物消息
        public static final int LIKE = 7;        // 点赞消息
        public static final int SYSTEM = 8;      // 系统消息
        public static final int JOIN = 9;        // 加入消息
        public static final int LEAVE = 10;      // 离开消息
    }
    

}
