package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 查询用户最近群组申请记录请求
 * @description: 用户查询自己在指定群组的最近申请记录
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "查询用户最近群组申请记录请求模型")
@Data
public class GetUserApplyListReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要查询申请记录的群组唯一标识")
    @NotBlank(message = "群组ID不能为空")
    private String groupId;
}
