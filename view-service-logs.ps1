# IM System Service Log Viewer
# View logs and status of running IM debug services

param(
    [switch]$Help,
    [switch]$Status,
    [switch]$Ports,
    [switch]$All
)

if ($Help) {
    Write-Host "IM System Service Log Viewer" -ForegroundColor Cyan
    Write-Host "============================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\view-service-logs.ps1           # Show running services status" -ForegroundColor White
    Write-Host "  .\view-service-logs.ps1 -Status   # Detailed service status" -ForegroundColor White
    Write-Host "  .\view-service-logs.ps1 -Ports    # Show debug port usage" -ForegroundColor White
    Write-Host "  .\view-service-logs.ps1 -All      # Show all information" -ForegroundColor White
    Write-Host ""
    Write-Host "Note:" -ForegroundColor Yellow
    Write-Host "  Services started with WindowStyle Hidden don't have accessible logs." -ForegroundColor Red
    Write-Host "  Use .\debug.ps1 for services with visible log output." -ForegroundColor Green
    Write-Host ""
    exit 0
}

function Get-IMServices {
    $javaProcesses = Get-Process java -ErrorAction SilentlyContinue
    $imServices = @()
    
    foreach ($process in $javaProcesses) {
        try {
            $commandLine = (Get-CimInstance Win32_Process -Filter "ProcessId = $($process.Id)").CommandLine
            
            if ($commandLine -match "im-(tcp|service|message-store)") {
                $serviceType = $matches[1]
                $serviceName = "im-$serviceType"
                
                # Extract debug port
                $debugPort = $null
                if ($commandLine -match "address=(\d+)") {
                    $debugPort = $matches[1]
                }
                
                $imServices += @{
                    PID = $process.Id
                    ServiceName = $serviceName
                    ServiceType = $serviceType
                    DebugPort = $debugPort
                    CommandLine = $commandLine
                    StartTime = $process.StartTime
                    CPU = $process.CPU
                    WorkingSet = [math]::Round($process.WorkingSet / 1MB, 1)
                }
            }
        }
        catch {
            # Skip processes we can't access
        }
    }
    
    return $imServices
}

function Show-ServiceStatus {
    $services = Get-IMServices
    
    if ($services.Count -eq 0) {
        Write-Host "No IM debug services are currently running." -ForegroundColor Yellow
        Write-Host ""
        Write-Host "To start services:" -ForegroundColor Cyan
        Write-Host "  .\quick-debug-all.ps1 -Interactive    # Interactive selection" -ForegroundColor White
        Write-Host "  .\debug.ps1 im-service      # Start with log output" -ForegroundColor White
        return
    }
    
    Write-Host "Running IM Debug Services:" -ForegroundColor Green
    Write-Host "=========================" -ForegroundColor Cyan
    
    foreach ($service in $services) {
        $color = switch ($service.ServiceType) {
            "tcp" { "Blue" }
            "service" { "Green" }
            "message-store" { "Magenta" }
            default { "White" }
        }
        
        $displayName = switch ($service.ServiceType) {
            "tcp" { "TCP Connection Service" }
            "service" { "Business Service" }
            "message-store" { "Message Store Service" }
            default { $service.ServiceName }
        }
        
        Write-Host ""
        Write-Host "🟢 $displayName" -ForegroundColor $color
        Write-Host "   PID: $($service.PID)" -ForegroundColor White
        Write-Host "   Debug Port: $($service.DebugPort)" -ForegroundColor Green
        Write-Host "   Memory: $($service.WorkingSet) MB" -ForegroundColor White
        if ($service.StartTime) {
            $uptime = (Get-Date) - $service.StartTime
            Write-Host "   Uptime: $($uptime.ToString('hh\:mm\:ss'))" -ForegroundColor White
        }
    }
    
    Write-Host ""
    Write-Host "VS Code Debug Connection:" -ForegroundColor Yellow
    foreach ($service in $services) {
        $attachName = switch ($service.ServiceType) {
            "tcp" { "Attach to IM-TCP" }
            "service" { "Attach to IM-Service" }
            "message-store" { "Attach to IM-Message-Store" }
        }
        Write-Host "  - $attachName (port $($service.DebugPort))" -ForegroundColor Cyan
    }
}

function Show-PortStatus {
    Write-Host "Debug Port Status:" -ForegroundColor Yellow
    Write-Host "==================" -ForegroundColor Cyan
    
    $debugPorts = @(5005, 5006, 5007)
    $portNames = @{
        5005 = "im-tcp (TCP Connection Service)"
        5006 = "im-service (Business Service)"
        5007 = "im-message-store (Message Store Service)"
    }
    
    foreach ($port in $debugPorts) {
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Host "🟢 Port $port - $($portNames[$port]) - ACTIVE" -ForegroundColor Green
                
                # Try to find the process using this port
                $netstat = netstat -ano | Select-String ":$port "
                if ($netstat) {
                    $pid = ($netstat -split '\s+')[-1]
                    try {
                        $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                        if ($process) {
                            Write-Host "   Process: $($process.ProcessName) (PID: $pid)" -ForegroundColor White
                        }
                    }
                    catch {
                        Write-Host "   Process: PID $pid" -ForegroundColor White
                    }
                }
            } else {
                Write-Host "⚪ Port $port - $($portNames[$port]) - AVAILABLE" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "⚪ Port $port - $($portNames[$port]) - AVAILABLE" -ForegroundColor Gray
        }
    }
}

function Show-DetailedStatus {
    $services = Get-IMServices
    
    Write-Host "Detailed Service Information:" -ForegroundColor Yellow
    Write-Host "============================" -ForegroundColor Cyan
    
    if ($services.Count -eq 0) {
        Write-Host "No IM services running." -ForegroundColor Red
        return
    }
    
    foreach ($service in $services) {
        Write-Host ""
        Write-Host "Service: $($service.ServiceName)" -ForegroundColor Green
        Write-Host "PID: $($service.PID)" -ForegroundColor White
        Write-Host "Debug Port: $($service.DebugPort)" -ForegroundColor Green
        Write-Host "Memory Usage: $($service.WorkingSet) MB" -ForegroundColor White
        if ($service.CPU) {
            Write-Host "CPU Time: $([math]::Round($service.CPU, 2)) seconds" -ForegroundColor White
        }
        if ($service.StartTime) {
            Write-Host "Start Time: $($service.StartTime)" -ForegroundColor White
            $uptime = (Get-Date) - $service.StartTime
            Write-Host "Uptime: $($uptime.ToString('d\.hh\:mm\:ss'))" -ForegroundColor White
        }
        Write-Host "Command Line:" -ForegroundColor Yellow
        Write-Host "  $($service.CommandLine)" -ForegroundColor Cyan
        Write-Host "----------------------------------------" -ForegroundColor Gray
    }
}

# Main execution
Write-Host "IM System Service Log Viewer" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Cyan
Write-Host ""

if ($All) {
    Show-ServiceStatus
    Write-Host ""
    Show-PortStatus
    Write-Host ""
    Show-DetailedStatus
} elseif ($Status) {
    Show-DetailedStatus
} elseif ($Ports) {
    Show-PortStatus
} else {
    Show-ServiceStatus
}

Write-Host ""
Write-Host "Commands:" -ForegroundColor Yellow
Write-Host "  .\view-service-logs.ps1 -All      # Show all information" -ForegroundColor White
Write-Host "  .\debug.ps1 im-service  # Start service with logs" -ForegroundColor White
Write-Host "  .\stop-im-en.ps1 -Force           # Stop all services" -ForegroundColor White
