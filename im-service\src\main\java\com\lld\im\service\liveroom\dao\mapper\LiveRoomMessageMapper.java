package com.lld.im.service.liveroom.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.liveroom.model.entity.LiveRoomMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 直播间消息Mapper接口
 */
@Mapper
public interface LiveRoomMessageMapper extends BaseMapper<LiveRoomMessage> {

    /**
     * 获取直播间最近消息
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @param limit  消息数量
     * @return 消息列表
     */
    @Select("SELECT * FROM im_live_room_message WHERE room_id = #{roomId} AND app_id = #{appId} ORDER BY sequence DESC LIMIT #{limit}")
    List<LiveRoomMessage> getRecentMessages(@Param("roomId") String roomId, @Param("appId") Integer appId, @Param("limit") Integer limit);

    /**
     * 获取直播间消息序列号大于指定值的消息
     *
     * @param roomId   直播间ID
     * @param appId    应用ID
     * @param sequence 序列号
     * @param limit    消息数量
     * @return 消息列表
     */
    @Select("SELECT * FROM im_live_room_message WHERE room_id = #{roomId} AND app_id = #{appId} AND sequence > #{sequence} ORDER BY sequence ASC LIMIT #{limit}")
    List<LiveRoomMessage> getMessagesAfterSequence(@Param("roomId") String roomId, @Param("appId") Integer appId, @Param("sequence") Long sequence, @Param("limit") Integer limit);
} 