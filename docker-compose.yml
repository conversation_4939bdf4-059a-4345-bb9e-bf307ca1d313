version: '3.8'

services:
  # Redis Cluster 配置 - 3个节点
  redis-node-1:
    image: redis:7.0-alpine
    container_name: redis-node-1
    ports:
      - "7000:6379"
      - "17000:16379"
    volumes:
      - ./docker/redis/node-1/data:/data
      - ./docker/redis/node-1/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - im-cluster-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-node-2:
    image: redis:7.0-alpine
    container_name: redis-node-2
    ports:
      - "7001:6379"
      - "17001:16379"
    volumes:
      - ./docker/redis/node-2/data:/data
      - ./docker/redis/node-2/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - im-cluster-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-node-3:
    image: redis:7.0-alpine
    container_name: redis-node-3
    ports:
      - "7002:6379"
      - "17002:16379"
    volumes:
      - ./docker/redis/node-3/data:/data
      - ./docker/redis/node-3/conf:/usr/local/etc/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - im-cluster-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cluster 初始化服务
  redis-cluster-init:
    image: redis:7.0-alpine
    container_name: redis-cluster-init
    depends_on:
      - redis-node-1
      - redis-node-2
      - redis-node-3
    networks:
      - im-cluster-network
    command: >
      sh -c "
        sleep 10 &&
        redis-cli -a 123456 --cluster create
        redis-node-1:6379
        redis-node-2:6379
        redis-node-3:6379
        --cluster-replicas 0
        --cluster-yes
      "
    restart: "no"

  # RabbitMQ Cluster 配置 - 3个节点
  rabbitmq-node-1:
    image: rabbitmq:3.12-management-alpine
    container_name: rabbitmq-node-1
    hostname: rabbit1
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_ERLANG_COOKIE: "im-cluster-cookie"
      RABBITMQ_DEFAULT_USER: "admin"
      RABBITMQ_DEFAULT_PASS: "admin123"
      RABBITMQ_NODE_TYPE: "disc"
    volumes:
      - ./docker/rabbitmq/node-1/data:/var/lib/rabbitmq
      - ./docker/rabbitmq/node-1/logs:/var/log/rabbitmq
    networks:
      - im-cluster-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  rabbitmq-node-2:
    image: rabbitmq:3.12-management-alpine
    container_name: rabbitmq-node-2
    hostname: rabbit2
    ports:
      - "5673:5672"
      - "15673:15672"
    environment:
      RABBITMQ_ERLANG_COOKIE: "im-cluster-cookie"
      RABBITMQ_DEFAULT_USER: "admin"
      RABBITMQ_DEFAULT_PASS: "admin123"
      RABBITMQ_NODE_TYPE: "disc"
    volumes:
      - ./docker/rabbitmq/node-2/data:/var/lib/rabbitmq
      - ./docker/rabbitmq/node-2/logs:/var/log/rabbitmq
    networks:
      - im-cluster-network
    depends_on:
      - rabbitmq-node-1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  rabbitmq-node-3:
    image: rabbitmq:3.12-management-alpine
    container_name: rabbitmq-node-3
    hostname: rabbit3
    ports:
      - "5674:5672"
      - "15674:15672"
    environment:
      RABBITMQ_ERLANG_COOKIE: "im-cluster-cookie"
      RABBITMQ_DEFAULT_USER: "admin"
      RABBITMQ_DEFAULT_PASS: "admin123"
      RABBITMQ_NODE_TYPE: "disc"
    volumes:
      - ./docker/rabbitmq/node-3/data:/var/lib/rabbitmq
      - ./docker/rabbitmq/node-3/logs:/var/log/rabbitmq
    networks:
      - im-cluster-network
    depends_on:
      - rabbitmq-node-1
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  im-cluster-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-node-1-data:
  redis-node-2-data:
  redis-node-3-data:
  rabbitmq-node-1-data:
  rabbitmq-node-2-data:
  rabbitmq-node-3-data:
