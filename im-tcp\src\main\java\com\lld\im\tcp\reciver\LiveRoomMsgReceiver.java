package com.lld.im.tcp.reciver;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.config.BootstrapConfig;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessageHeader;
import com.lld.im.codec.proto.MessagePack;
import com.lld.im.common.constant.Constants;
import com.lld.im.tcp.handler.LiveRoomMessageHandler;
import com.lld.im.tcp.util.MessageFieldExtractor;
import com.lld.im.tcp.utils.MqFactory;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 直播间消息接收器
 * 用于接收和处理直播间消息
 * 支持分片队列消费
 */
@Slf4j
public class LiveRoomMsgReceiver {

    private static String brokerId;
    private static int shardCount = 4; // 默认分片数量
    private static boolean shardingEnabled = true; // 默认启用分片

    /**
     * 初始化直播间消息接收器
     */
    public static void init(BootstrapConfig config) {
        LiveRoomMsgReceiver.brokerId = String.valueOf(config.getLim().getBrokerId());
        
        // 从BootstrapConfig加载分片配置
        loadShardingConfig(config);
        
        // 如果启用分片，则初始化所有分片队列
        if (shardingEnabled) {
            initLiveRoomShardingQueues();
        } else {
            // 否则初始化单一队列
            initLiveRoomMessageQueue(Constants.RabbitConstants.LiveRoomQueue, 
                    Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + "*");
        }
        
        log.info("直播间消息接收器初始化完成，brokerId: {}, 分片启用: {}, 分片数量: {}", 
                brokerId, shardingEnabled, shardCount);
    }

    /**
     * 从BootstrapConfig加载分片配置
     */
    private static void loadShardingConfig(BootstrapConfig config) {
        try {
            // 从BootstrapConfig获取直播间分片配置
            if (config.getLiveroom() != null && config.getLiveroom().getSharding() != null) {
                BootstrapConfig.ShardingConfig shardingConfig = config.getLiveroom().getSharding();
                
                // 读取是否启用分片
                if (shardingConfig.getEnabled() != null) {
                    shardingEnabled = shardingConfig.getEnabled();
                }
                
                // 读取分片数量
                if (shardingConfig.getShardCount() != null) {
                    shardCount = shardingConfig.getShardCount();
                }
                
                log.info("从BootstrapConfig加载分片配置：enabled={}, shardCount={}", 
                        shardingEnabled, shardCount);
            } else {
                log.warn("BootstrapConfig中未找到直播间分片配置，使用默认分片配置");
            }
        } catch (Exception e) {
            log.warn("加载分片配置失败，使用默认值: enabled={}, shardCount={}", 
                    shardingEnabled, shardCount, e);
        }
    }

    /**
     * 初始化所有分片队列
     */
    private static void initLiveRoomShardingQueues() {
        try {
            // 声明交换机（所有分片共用一个交换机）
            Channel channel = MqFactory.getChannel(Constants.RabbitConstants.LiveRoomExchange);
            log.info("声明直播间消息交换机: {}", Constants.RabbitConstants.LiveRoomExchange);
            channel.exchangeDeclare(Constants.RabbitConstants.LiveRoomExchange, "direct", true);
            
            // 初始化每个分片队列
            for (int i = 0; i < shardCount; i++) {
                String queueName = Constants.RabbitConstants.LiveRoomQueue + "." + i;
                String routingKey = Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + i;
                
                initLiveRoomMessageQueue(queueName, routingKey);
            }
        } catch (Exception e) {
            log.error("初始化直播间分片队列失败", e);
        }
    }

    /**
     * 初始化直播间消息队列的消费者
     * 
     * @param queueName 队列名称
     * @param routingKey 路由键
     */
    private static void initLiveRoomMessageQueue(String queueName, String routingKey) {
        try {
            Channel channel = MqFactory.getChannel(queueName);
            
            // 声明队列
            log.info("声明直播间消息队列: {}", queueName);
            channel.queueDeclare(queueName, true, false, false, null);
            
            // 绑定队列到交换机
            log.info("绑定队列到交换机: {} -> {} 使用路由键: {}", 
                    queueName, Constants.RabbitConstants.LiveRoomExchange, routingKey);
            channel.queueBind(queueName, Constants.RabbitConstants.LiveRoomExchange, routingKey);

            // 设置消费者
            log.info("设置直播间消息队列消费者: {}", queueName);
            channel.basicConsume(queueName, false, new DefaultConsumer(channel) {
                @Override
                public void handleDelivery(String consumerTag, Envelope envelope,
                                        AMQP.BasicProperties properties, byte[] body) throws IOException {
                    try {
                        String msgStr = new String(body, StandardCharsets.UTF_8);
                        log.info("收到直播间消息 [{}]: {}", queueName, msgStr);

                        // 使用工具类提取和验证消息字段
                        MessageFieldExtractor.MessageFields fields = MessageFieldExtractor.extractFromString(msgStr);
                        if (fields == null) {
                            log.error("消息字段提取失败: {}", msgStr);
                            channel.basicNack(envelope.getDeliveryTag(), false, false);
                            return;
                        }

                        // 验证和设置默认值
                        fields.validateAndSetDefaults();
                        if (!fields.isValid()) {
                            log.error("消息字段验证失败: {}", msgStr);
                            channel.basicNack(envelope.getDeliveryTag(), false, false);
                            return;
                        }

                        // 处理直播间ID
                        String roomId = fields.getRoomId();
                        if (StringUtils.isEmpty(roomId)) {
                            // 尝试从路由键中提取直播间ID
                            String routingKeyReceived = envelope.getRoutingKey();
                            roomId = MessageFieldExtractor.extractRoomIdFromRoutingKey(
                                    routingKeyReceived, Constants.RabbitConstants.LiveRoomRoutingKeyPrefix);

                            if (StringUtils.isNotEmpty(roomId)) {
                                // 将提取的roomId添加到data中
                                if (fields.getData() == null) {
                                    fields.setData(new JSONObject());
                                }
                                fields.getData().put("roomId", roomId);
                                log.info("从路由键提取并设置直播间ID: {}", roomId);
                            }
                        }

                        log.info("🎬 处理直播间消息: command={}, appId={}, roomId={}",
                                fields.getCommand(), fields.getAppId(), roomId);

                        // 构建完整的消息对象，使用简化的逻辑
                        Message message = LiveRoomMsgReceiver.buildMessage(fields);

                        log.debug("构建完整消息对象: command={}, appId={}, clientType={}, imei={}",
                                fields.getCommand(), fields.getAppId(), fields.getClientType(), fields.getImei());

                        // 使用直播间消息处理器处理消息
                        log.debug("📤 调用LiveRoomMessageHandler处理消息: command={}", fields.getCommand());
                        LiveRoomMessageHandler.handle(message);

                        channel.basicAck(envelope.getDeliveryTag(), false);
                        log.debug("✅ 直播间消息处理完成: command={}", fields.getCommand());
                    } catch (Exception e) {
                        log.error("处理直播间消息异常 [{}]", queueName, e);
                        channel.basicNack(envelope.getDeliveryTag(), false, true);
                    }
                }
            });
            log.info("直播间消息队列 [{}] 消费者初始化完成", queueName);
        } catch (Exception e) {
            log.error("初始化直播间消息队列 [{}] 消费者失败", queueName, e);
        }
    }

    /**
     * 构建Message对象
     * 使用提取的字段构建标准的Message对象
     *
     * @param fields 提取的消息字段
     * @return 构建的Message对象
     */
    private static Message buildMessage(MessageFieldExtractor.MessageFields fields) {
        Message message = new Message();

        // 构建MessageHeader
        MessageHeader messageHeader = new MessageHeader();
        messageHeader.setCommand(fields.getCommand());
        messageHeader.setAppId(fields.getAppId());
        messageHeader.setClientType(fields.getClientType());
        messageHeader.setImei(fields.getImei());
        messageHeader.setVersion(1); // 默认版本号
        messageHeader.setMessageType(0x0); // JSON格式

        // 构建MessagePack
        MessagePack<JSONObject> messagePack = new MessagePack<>();
        messagePack.setCommand(fields.getCommand());
        messagePack.setAppId(fields.getAppId());
        messagePack.setClientType(fields.getClientType());
        messagePack.setImei(fields.getImei());

        // 构建完整的JSON对象，包含顶层字段和data
        JSONObject fullMessage = new JSONObject();
        fullMessage.put("command", fields.getCommand());
        fullMessage.put("appId", fields.getAppId());
        fullMessage.put("clientType", fields.getClientType());
        fullMessage.put("imei", fields.getImei());
        fullMessage.put("timestamp", fields.getTimestamp());
        fullMessage.put("data", fields.getData() != null ? fields.getData() : new JSONObject());

        messagePack.setData(fullMessage);

        message.setMessageHeader(messageHeader);
        message.setMessagePack(messagePack);

        return message;
    }
}