-- 插入测试直播间数据
INSERT INTO `im_live_room` (`room_id`, `room_name`, `room_cover`, `status`, `anchor_id`, `app_id`, `announcement`, `mute_all`, `need_review`, `max_online_count`)
VALUES
('live_10001', '音乐直播间', 'https://example.com/covers/music.jpg', 1, 'user_10001', 10000, '欢迎来到音乐直播间，请文明发言！', 0, 0, 1000),
('live_10002', '游戏直播间', 'https://example.com/covers/game.jpg', 1, 'user_10002', 10000, '欢迎来到游戏直播间，一起玩游戏吧！', 0, 0, 2000),
('live_10003', '聊天直播间', 'https://example.com/covers/chat.jpg', 0, 'user_10003', 10000, '欢迎来到聊天直播间，分享你的故事！', 0, 1, 500),
('live_10004', '教育直播间', 'https://example.com/covers/edu.jpg', 2, 'user_10004', 10000, '欢迎来到教育直播间，一起学习进步！', 0, 0, 800),
('live_10005', '美食直播间', 'https://example.com/covers/food.jpg', 1, 'user_10005', 10000, '欢迎来到美食直播间，分享美食心得！', 0, 0, 1500);

-- 插入测试直播间成员数据
INSERT INTO `im_live_room_member` (`room_id`, `user_id`, `role`, `mute`, `app_id`, `join_time`, `last_active_time`)
VALUES
('live_10001', 'user_10001', 1, 0, 10000, NOW(), NOW()),
('live_10001', 'user_10006', 2, 0, 10000, NOW(), NOW()),
('live_10001', 'user_10007', 3, 0, 10000, NOW(), NOW()),
('live_10001', 'user_10008', 4, 0, 10000, NOW(), NOW()),
('live_10001', 'user_10009', 4, 1, 10000, NOW(), NOW()),
('live_10002', 'user_10002', 1, 0, 10000, NOW(), NOW()),
('live_10002', 'user_10010', 2, 0, 10000, NOW(), NOW()),
('live_10002', 'user_10011', 4, 0, 10000, NOW(), NOW()),
('live_10003', 'user_10003', 1, 0, 10000, NOW(), NOW()),
('live_10004', 'user_10004', 1, 0, 10000, NOW(), NOW()),
('live_10005', 'user_10005', 1, 0, 10000, NOW(), NOW());

-- 插入测试直播间消息数据
INSERT INTO `im_live_room_message` (`message_id`, `room_id`, `from_id`, `from_nickname`, `from_avatar`, `message_type`, `content`, `sequence`, `app_id`, `send_time`)
VALUES
('msg_10001', 'live_10001', 'user_10001', '主播小明', 'https://example.com/avatars/10001.jpg', 1, '大家好，欢迎来到我的直播间！', 1, 10000, NOW()),
('msg_10002', 'live_10001', 'user_10007', '小红', 'https://example.com/avatars/10007.jpg', 1, '主播好，支持你！', 2, 10000, NOW()),
('msg_10003', 'live_10001', 'user_10008', '小张', 'https://example.com/avatars/10008.jpg', 1, '主播唱得真好听！', 3, 10000, NOW()),
('msg_10004', 'live_10001', 'user_10007', '小红', 'https://example.com/avatars/10007.jpg', 6, '{"gift_id":"gift_10001","gift_name":"鲜花","gift_count":5}', 4, 10000, NOW()),
('msg_10005', 'live_10001', 'user_10006', '管理员小李', 'https://example.com/avatars/10006.jpg', 8, '欢迎新用户加入直播间！', 5, 10000, NOW()),
('msg_10006', 'live_10002', 'user_10002', '游戏达人', 'https://example.com/avatars/10002.jpg', 1, '今天我们玩最新的游戏！', 1, 10000, NOW()),
('msg_10007', 'live_10002', 'user_10011', '游戏粉丝', 'https://example.com/avatars/10011.jpg', 1, '主播技术太厉害了！', 2, 10000, NOW()),
('msg_10008', 'live_10002', 'user_10010', '游戏管理员', 'https://example.com/avatars/10010.jpg', 7, '{"count":10}', 3, 10000, NOW()),
('msg_10009', 'live_10005', 'user_10005', '美食大厨', 'https://example.com/avatars/10005.jpg', 1, '今天我们来做红烧肉！', 1, 10000, NOW()),
('msg_10010', 'live_10005', 'system', '系统', 'https://example.com/avatars/system.jpg', 9, '{"user_id":"user_10012","nickname":"新用户"}', 2, 10000, NOW());

-- 插入测试礼物数据
INSERT INTO `im_live_room_gift` (`gift_id`, `gift_name`, `gift_img`, `gift_animation`, `gift_price`, `gift_type`, `status`, `app_id`)
VALUES
('gift_10001', '鲜花', 'https://example.com/gifts/flower.png', 'https://example.com/gifts/flower.gif', 10, 1, 1, 10000),
('gift_10002', '爱心', 'https://example.com/gifts/heart.png', 'https://example.com/gifts/heart.gif', 20, 1, 1, 10000),
('gift_10003', '火箭', 'https://example.com/gifts/rocket.png', 'https://example.com/gifts/rocket.gif', 100, 2, 1, 10000),
('gift_10004', '豪车', 'https://example.com/gifts/car.png', 'https://example.com/gifts/car.gif', 500, 2, 1, 10000),
('gift_10005', '皇冠', 'https://example.com/gifts/crown.png', 'https://example.com/gifts/crown.gif', 1000, 3, 1, 10000);

-- 插入测试礼物记录数据
INSERT INTO `im_live_room_gift_record` (`record_id`, `room_id`, `gift_id`, `sender_id`, `receiver_id`, `gift_count`, `gift_price`, `total_price`, `app_id`, `send_time`)
VALUES
('record_10001', 'live_10001', 'gift_10001', 'user_10007', 'user_10001', 5, 10, 50, 10000, NOW()),
('record_10002', 'live_10001', 'gift_10002', 'user_10008', 'user_10001', 3, 20, 60, 10000, NOW()),
('record_10003', 'live_10001', 'gift_10003', 'user_10007', 'user_10001', 1, 100, 100, 10000, NOW()),
('record_10004', 'live_10002', 'gift_10001', 'user_10011', 'user_10002', 2, 10, 20, 10000, NOW()),
('record_10005', 'live_10005', 'gift_10004', 'user_10012', 'user_10005', 1, 500, 500, 10000, NOW());

-- 插入测试直播间管理员数据
INSERT INTO `im_live_room_admin` (`room_id`, `user_id`, `operator_id`, `app_id`)
VALUES
('live_10001', 'user_10006', 'user_10001', 10000),
('live_10002', 'user_10010', 'user_10002', 10000);

-- 插入测试直播间禁言用户数据
INSERT INTO `im_live_room_mute_user` (`room_id`, `user_id`, `operator_id`, `mute_end_time`, `app_id`)
VALUES
('live_10001', 'user_10009', 'user_10006', DATE_ADD(NOW(), INTERVAL 1 HOUR), 10000);

-- 插入测试直播间封禁用户数据
INSERT INTO `im_live_room_blocked_user` (`room_id`, `user_id`, `operator_id`, `block_end_time`, `reason`, `app_id`)
VALUES
('live_10001', 'user_10013', 'user_10006', DATE_ADD(NOW(), INTERVAL 1 DAY), '发布不良信息', 10000),
('live_10002', 'user_10014', 'user_10010', DATE_ADD(NOW(), INTERVAL 7 DAY), '多次违规', 10000);

-- 插入测试直播间统计数据
INSERT INTO `im_live_room_stats` (`room_id`, `online_count`, `peak_online_count`, `total_viewer_count`, `like_count`, `gift_count`, `gift_value`, `start_time`, `app_id`)
VALUES
('live_10001', 120, 200, 500, 300, 9, 210, DATE_SUB(NOW(), INTERVAL 2 HOUR), 10000),
('live_10002', 85, 150, 320, 200, 2, 20, DATE_SUB(NOW(), INTERVAL 1 HOUR), 10000),
('live_10005', 65, 100, 200, 150, 1, 500, DATE_SUB(NOW(), INTERVAL 30 MINUTE), 10000); 