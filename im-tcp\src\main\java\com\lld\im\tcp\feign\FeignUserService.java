package com.lld.im.tcp.feign;

import com.lld.im.common.ResponseVO;
import feign.Headers;
import feign.RequestLine;

/**
 * 用户服务Feign客户端
 * 用于TCP层调用用户服务获取用户信息
 */
public interface FeignUserService {

    /**
     * 获取单个用户信息
     * @param requestBody 请求体，包含userId和appId
     * @return 用户信息
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /v1/user/data/getSingleUserInfo")
    ResponseVO getUserInfo(Object requestBody);
}
