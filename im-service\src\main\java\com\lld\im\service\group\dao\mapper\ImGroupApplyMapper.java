package com.lld.im.service.group.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lld.im.service.group.dao.ImGroupApplyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @description: 群申请记录数据访问层
 * @author: lld
 * @version: 1.0
 */
@Mapper
public interface ImGroupApplyMapper extends BaseMapper<ImGroupApplyEntity> {

    /**
     * 查询用户在指定群组的待审批申请
     * @param appId 应用ID
     * @param groupId 群组ID
     * @param applicantId 申请人ID
     * @return 待审批申请记录
     */
    @Select("SELECT * FROM im_group_apply_request WHERE app_id = #{appId} AND group_id = #{groupId} " +
            "AND applicant_id = #{applicantId} AND apply_status = 0 ORDER BY apply_time DESC LIMIT 1")
    ImGroupApplyEntity getPendingApply(@Param("appId") Integer appId, 
                                       @Param("groupId") String groupId, 
                                       @Param("applicantId") String applicantId);

    /**
     * 查询用户的申请历史
     * @param appId 应用ID
     * @param applicantId 申请人ID
     * @param limit 限制数量
     * @return 申请记录列表
     */
    @Select("SELECT * FROM im_group_apply_request WHERE app_id = #{appId} AND applicant_id = #{applicantId} " +
            "ORDER BY apply_time DESC LIMIT #{limit}")
    List<ImGroupApplyEntity> getUserApplyHistory(@Param("appId") Integer appId,
                                                 @Param("applicantId") String applicantId,
                                                 @Param("limit") Integer limit);

    /**
     * 分页查询群组的申请列表（使用MyBatis-Plus分页）
     * @param page 分页对象
     * @param appId 应用ID
     * @param groupId 群组ID
     * @param applyStatus 申请状态，null表示查询所有状态
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 分页申请记录
     */
    @Select("<script>" +
            "SELECT * FROM im_group_apply_request WHERE app_id = #{appId} AND group_id = #{groupId} " +
            "<if test=\"applyStatus != null\"> AND apply_status = #{applyStatus} </if>" +
            "<if test=\"startTime != null\"> AND apply_time &gt;= #{startTime} </if>" +
            "<if test=\"endTime != null\"> AND apply_time &lt;= #{endTime} </if>" +
            "ORDER BY apply_time DESC" +
            "</script>")
    IPage<ImGroupApplyEntity> getGroupApplyListPage(IPage<ImGroupApplyEntity> page,
                                                    @Param("appId") Integer appId,
                                                    @Param("groupId") String groupId,
                                                    @Param("applyStatus") Integer applyStatus,
                                                    @Param("startTime") Long startTime,
                                                    @Param("endTime") Long endTime);

    /**
     * 查询用户在指定群组的最近申请记录
     * @param appId 应用ID
     * @param applicantId 申请人ID
     * @param groupId 群组ID
     * @return 最近申请记录，如果没有则返回null
     */
    @Select("SELECT * FROM im_group_apply_request WHERE app_id = #{appId} AND applicant_id = #{applicantId} " +
            "AND group_id = #{groupId} ORDER BY apply_time DESC LIMIT 1")
    ImGroupApplyEntity getUserLatestApply(@Param("appId") Integer appId,
                                          @Param("applicantId") String applicantId,
                                          @Param("groupId") String groupId);
}
