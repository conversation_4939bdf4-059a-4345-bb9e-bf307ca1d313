package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 删除好友请求
 */
@ApiModel(description = "删除好友请求模型")
@Data
public class DeleteFriendReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起删除操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "好友用户ID", required = true, example = "user456", notes = "要删除的好友用户ID")
    @NotBlank(message = "toId不能为空")
    private String toId;

}
