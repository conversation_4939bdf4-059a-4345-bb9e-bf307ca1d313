# IM服务终止脚本
# 用于终止 im-tcp、im-service、im-message-store 三个服务进程
# 作者: IM System
# 日期: 2025-08-04

param(
    [switch]$Force,           # 强制终止进程
    [switch]$Verbose,         # 详细输出
    [switch]$WaitForExit,     # 等待进程正常退出
    [int]$TimeoutSeconds = 30 # 等待超时时间（秒）
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 记录日志函数
function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-ColorOutput $logMessage "Red" }
        "WARN"  { Write-ColorOutput $logMessage "Yellow" }
        "SUCCESS" { Write-ColorOutput $logMessage "Green" }
        default { Write-ColorOutput $logMessage "White" }
    }
    
    if ($Verbose) {
        Add-Content -Path "stop-im-services.log" -Value $logMessage
    }
}

# 检查进程是否存在
function Test-ProcessExists {
    param([int]$ProcessId)
    try {
        $process = Get-Process -Id $ProcessId -ErrorAction Stop
        return $true
    }
    catch {
        return $false
    }
}

# 优雅停止进程
function Stop-ProcessGracefully {
    param(
        [System.Diagnostics.Process]$Process,
        [int]$TimeoutSeconds = 30
    )
    
    try {
        Write-Log "尝试优雅停止进程 PID: $($Process.Id), 名称: $($Process.ProcessName)" "INFO"
        
        # 发送关闭信号
        if ($Process.CloseMainWindow()) {
            Write-Log "已发送关闭信号到进程 $($Process.Id)" "INFO"
            
            # 等待进程退出
            if ($Process.WaitForExit($TimeoutSeconds * 1000)) {
                Write-Log "进程 $($Process.Id) 已优雅退出" "SUCCESS"
                return $true
            } else {
                Write-Log "进程 $($Process.Id) 在 $TimeoutSeconds 秒内未退出" "WARN"
                return $false
            }
        } else {
            Write-Log "无法发送关闭信号到进程 $($Process.Id)" "WARN"
            return $false
        }
    }
    catch {
        Write-Log "优雅停止进程 $($Process.Id) 时发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 强制终止进程
function Stop-ProcessForcefully {
    param([System.Diagnostics.Process]$Process)
    
    try {
        Write-Log "强制终止进程 PID: $($Process.Id), 名称: $($Process.ProcessName)" "WARN"
        $Process.Kill()
        $Process.WaitForExit(5000)  # 等待5秒确认进程已终止
        Write-Log "进程 $($Process.Id) 已被强制终止" "SUCCESS"
        return $true
    }
    catch {
        Write-Log "强制终止进程 $($Process.Id) 时发生错误: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# 查找IM服务进程
function Find-IMServiceProcesses {
    Write-Log "正在查找IM服务进程..." "INFO"
    
    $imProcesses = @()
    
    # 定义服务标识符
    $serviceIdentifiers = @(
        @{ Name = "im-tcp"; Patterns = @("im-tcp", "tcp-.*\.jar", "im-tcp.*\.jar") },
        @{ Name = "im-service"; Patterns = @("im-service", "im-service.*\.jar") },
        @{ Name = "im-message-store"; Patterns = @("im-message-store", "im-message-store.*\.jar") }
    )
    
    # 获取所有Java进程
    $javaProcesses = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq "java.exe" }
    
    foreach ($javaProcess in $javaProcesses) {
        $commandLine = $javaProcess.CommandLine
        if ([string]::IsNullOrEmpty($commandLine)) { continue }
        
        foreach ($service in $serviceIdentifiers) {
            foreach ($pattern in $service.Patterns) {
                if ($commandLine -match $pattern) {
                    $processObj = Get-Process -Id $javaProcess.ProcessId -ErrorAction SilentlyContinue
                    if ($processObj) {
                        $imProcesses += @{
                            ServiceName = $service.Name
                            Process = $processObj
                            CommandLine = $commandLine
                        }
                        Write-Log "发现 $($service.Name) 服务进程: PID $($javaProcess.ProcessId)" "INFO"
                        if ($Verbose) {
                            Write-Log "命令行: $commandLine" "INFO"
                        }
                        break
                    }
                }
            }
        }
    }
    
    return $imProcesses
}

# 主函数
function Main {
    Write-ColorOutput "========================================" "Cyan"
    Write-ColorOutput "    IM服务进程终止脚本" "Cyan"
    Write-ColorOutput "========================================" "Cyan"
    Write-Log "脚本开始执行" "INFO"
    
    # 查找IM服务进程
    $imProcesses = Find-IMServiceProcesses
    
    if ($imProcesses.Count -eq 0) {
        Write-Log "未发现任何IM服务进程正在运行" "INFO"
        return
    }
    
    Write-Log "发现 $($imProcesses.Count) 个IM服务进程" "INFO"
    
    # 显示发现的进程
    Write-ColorOutput "`n发现的IM服务进程:" "Yellow"
    foreach ($item in $imProcesses) {
        Write-ColorOutput "- $($item.ServiceName): PID $($item.Process.Id)" "White"
    }
    
    # 询问用户确认（除非使用Force参数）
    if (-not $Force) {
        Write-ColorOutput "`n是否要终止这些进程? (Y/N): " "Yellow" -NoNewline
        $confirmation = Read-Host
        if ($confirmation -notmatch '^[Yy]') {
            Write-Log "用户取消操作" "INFO"
            return
        }
    }
    
    Write-ColorOutput "`n开始终止进程..." "Yellow"
    
    $successCount = 0
    $failureCount = 0
    
    foreach ($item in $imProcesses) {
        $process = $item.Process
        $serviceName = $item.ServiceName
        
        Write-Log "正在处理 $serviceName 服务 (PID: $($process.Id))" "INFO"
        
        $stopped = $false
        
        # 如果不是强制模式，先尝试优雅停止
        if (-not $Force -and $WaitForExit) {
            $stopped = Stop-ProcessGracefully -Process $process -TimeoutSeconds $TimeoutSeconds
        }
        
        # 如果优雅停止失败或使用强制模式，则强制终止
        if (-not $stopped) {
            $stopped = Stop-ProcessForcefully -Process $process
        }
        
        if ($stopped) {
            $successCount++
            Write-Log "$serviceName 服务已成功终止" "SUCCESS"
        } else {
            $failureCount++
            Write-Log "$serviceName 服务终止失败" "ERROR"
        }
        
        Start-Sleep -Milliseconds 500  # 短暂延迟
    }
    
    # 输出结果统计
    Write-ColorOutput "`n========================================" "Cyan"
    Write-ColorOutput "执行结果统计:" "Cyan"
    Write-ColorOutput "成功终止: $successCount 个进程" "Green"
    if ($failureCount -gt 0) {
        Write-ColorOutput "终止失败: $failureCount 个进程" "Red"
    }
    Write-ColorOutput "========================================" "Cyan"
    
    Write-Log "脚本执行完成" "INFO"
}

# 脚本入口点
try {
    Main
}
catch {
    Write-Log "脚本执行过程中发生未处理的错误: $($_.Exception.Message)" "ERROR"
    exit 1
}
