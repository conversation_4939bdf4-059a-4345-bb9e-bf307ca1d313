package com.lld.im.service.liveroom.mq;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.constant.Constants;
import com.lld.im.service.liveroom.model.data.LiveRoomMessageData;
import com.lld.im.service.liveroom.sharding.LiveRoomShardingManager;
import com.lld.im.service.liveroom.util.MessageFieldExtractor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.UUID;

/**
 * 直播间消息生产者
 * 负责将消息发送到对应的分片队列
 * 重构后直接构建标准格式消息，避免字段提取-删除操作
 */
@Slf4j
@Component
public class LiveRoomMessageProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private LiveRoomShardingManager shardingManager;

    /**
     * 发送直播间消息到对应分片队列
     * 重构后直接构建标准格式，避免字段提取-删除操作
     *
     * @param roomId     直播间ID
     * @param command    消息命令
     * @param messageData 消息数据对象
     * @param appId      应用ID
     * @param clientType 客户端类型
     * @param imei       设备标识
     * @return 是否发送成功
     */
    public boolean sendMessage(String roomId, Integer command, LiveRoomMessageData messageData,
                              Integer appId, Integer clientType, String imei) {
        String exchange = Constants.RabbitConstants.LiveRoomExchange;

        // 获取分片路由键
        String routingKey = shardingManager.getLiveRoomRoutingKey(roomId, appId);
        log.info("发送直播间消息，房间ID: {}, 命令: {}, 路由键: {}", roomId, command, routingKey);

        // 直接构建标准格式消息，无需字段提取-删除操作
        JSONObject liveRoomMsg = MessageFieldExtractor.buildStandardMessage(
                command, appId, clientType, imei, buildDataObject(messageData, roomId)
        );

        log.debug("构建的标准消息格式: {}", liveRoomMsg.toJSONString());

        return sendMessageToExchange(exchange, routingKey, liveRoomMsg, appId);
    }



    /**
     * 构建data对象
     *
     * @param messageData 消息数据
     * @param roomId 直播间ID
     * @return JSON数据对象
     */
    private JSONObject buildDataObject(LiveRoomMessageData messageData, String roomId) {
        JSONObject data = new JSONObject();

        // 确保roomId存在
        data.put("roomId", roomId);

        // 添加消息数据字段
        if (messageData.getUserId() != null) {
            data.put("userId", messageData.getUserId());
        }
        if (messageData.getAction() != null) {
            data.put("action", messageData.getAction());
        }
        if (messageData.getMessageType() != null) {
            data.put("messageType", messageData.getMessageType());
        }
        if (messageData.getContent() != null) {
            data.put("content", messageData.getContent());
        }
        if (messageData.getMessageId() != null) {
            data.put("messageId", messageData.getMessageId());
        }
        if (messageData.getFromNickname() != null) {
            data.put("fromNickname", messageData.getFromNickname());
        }
        if (messageData.getFromAvatar() != null) {
            data.put("fromAvatar", messageData.getFromAvatar());
        }
        if (messageData.getFromRole() != null) {
            data.put("fromRole", messageData.getFromRole());
        }
        if (messageData.getToId() != null) {
            data.put("toId", messageData.getToId());
        }
        if (messageData.getIsGuest() != null) {
            data.put("isGuest", messageData.getIsGuest());
        }
        if (messageData.getSequence() != null) {
            data.put("sequence", messageData.getSequence());
        }
        if (messageData.getSendTime() != null) {
            data.put("sendTime", messageData.getSendTime());
        }
        if (messageData.getExtra() != null) {
            data.put("extra", messageData.getExtra());
        }

        return data;
    }

    /**
     * 发送消息到指定直播间的特定用户
     *
     * @param roomId     直播间ID
     * @param userId     用户ID
     * @param command    消息命令
     * @param messageData 消息数据
     * @param appId      应用ID
     * @param clientType 客户端类型
     * @param imei       设备标识
     * @return 是否发送成功
     */
    public boolean sendMessageToUser(String roomId, String userId, Integer command,
                                   LiveRoomMessageData messageData, Integer appId,
                                   Integer clientType, String imei) {
        String exchange = Constants.RabbitConstants.LiveRoomExchange;

        // 路由键结合用户ID，确保消息可以到达特定用户
        String routingKey = Constants.RabbitConstants.LiveRoomRoutingKeyPrefix + roomId + "." + userId;

        // 确保消息数据包含userId
        messageData.setUserId(userId);

        // 直接构建标准格式消息
        JSONObject liveRoomMsg = MessageFieldExtractor.buildStandardMessage(
                command, appId, clientType, imei, buildDataObject(messageData, roomId)
        );

        return sendMessageToExchange(exchange, routingKey, liveRoomMsg, appId);
    }




    /**
     * 发送消息到指定的交换机和路由
     *
     * @param exchange   交换机名称
     * @param routingKey 路由键
     * @param data       消息数据
     * @param appId      应用ID
     * @return 是否发送成功
     */
    private boolean sendMessageToExchange(String exchange, String routingKey, Object data, Integer appId) {
        String messageId = UUID.randomUUID().toString();
        log.info("发送消息到交换机，ID: {}, 交换机: {}, 路由键: {}",
                messageId, exchange, routingKey);

        try {
            CorrelationData correlationData = new CorrelationData(messageId);

            // 消息属性与头信息
            MessagePostProcessor messagePostProcessor = message -> {
                message.getMessageProperties().setMessageId(messageId);
                message.getMessageProperties().setAppId(appId.toString());
                message.getMessageProperties().setTimestamp(new Date());
                return message;
            };

            rabbitTemplate.convertAndSend(exchange, routingKey,
                    JSONObject.toJSONString(data), messagePostProcessor, correlationData);
            log.info("消息成功发送到交换机，ID: {}", messageId);
            return true;
        } catch (Exception e) {
            log.error("发送消息到交换机失败: {}, 错误: {}", messageId, e.getMessage(), e);
            return false;
        }
    }
} 