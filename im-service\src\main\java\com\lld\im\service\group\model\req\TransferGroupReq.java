package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 转让群组请求
 */
@ApiModel(description = "转让群组请求模型")
@Data
public class TransferGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要转让的群组唯一标识")
    @NotNull(message = "群id不能为空")
    private String groupId;

    @ApiModelProperty(value = "新群主用户ID", required = true, example = "user456", notes = "接收群主权限的用户ID")
    private String ownerId;

}
