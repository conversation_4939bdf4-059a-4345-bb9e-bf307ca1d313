package com.lld.im.common.constant;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public class Constants {

    /** channel绑定的userId Key*/
    public static final String UserId = "userId";

    /** channel绑定的appId */
    public static final String AppId = "appId";

    public static final String ClientType = "clientType";

    public static final String Imei = "imei";

    /** channel绑定的clientType 和 imel Key*/
    public static final String ClientImei = "clientImei";

    public static final String ReadTime = "readTime";

    public static final String IsGuest = "is_Guest";

    public static final String IS_WEBSOCKET = "isWebSocket";

    public static final String ImCoreZkRoot = "/im-coreRoot";

    public static final String ImCoreZkRootTcp = "/tcp";

    public static final String ImCoreZkRootWeb = "/web";


    public static class RedisConstants{

        /**
         * userSign，格式：appId:userSign:
         */
        public static final String userSign = "userSign";

        /**
         * 用户上线通知channel
         */
        public static final String UserLoginChannel
                = "signal/channel/LOGIN_USER_INNER_QUEUE";


        /**
         * 用户session，appId + UserSessionConstants + 用户id 例如10000：userSession：lld
         */
        public static final String UserSessionConstants = ":userSession:";

        /**
         * 缓存客户端消息防重，格式： appId + :cacheMessage: + messageId
         */
        public static final String cacheMessage = "cacheMessage";

        public static final String OfflineMessage = "offlineMessage";

        /**
         * seq 前缀
         */
        public static final String SeqPrefix = "seq";

        /**
         * 用户订阅列表，格式 ：appId + :subscribe: + userId。Hash结构，filed为订阅自己的人
         */
        public static final String subscribe = "subscribe";

        /**
         * 用户自定义在线状态，格式 ：appId + :userCustomerStatus: + userId。set，value为用户id
         */
        public static final String userCustomerStatus = "userCustomerStatus";
        
        /**
         * 游客前缀，格式：appId + GuestUserPrefix + userId
         */
        public static final String GuestUserPrefix = ":guest:";
        
        /**
         * 游客登录通知channel
         */
        public static final String GuestLoginChannel = "signal/channel/GUEST_LOGIN_INNER_QUEUE";
        
        /**
         * 直播间游客列表，格式：liveroom:{roomId}:guests，set结构
         */
        public static final String LiveRoomGuestPrefix = "liveroom:";
        
        /**
         * 直播间游客后缀
         */
        public static final String LiveRoomGuestSuffix = ":guests";
        
        /**
         * 直播间用户后缀
         */
        public static final String LiveRoomUserSuffix = ":users";

        /**
         * 离线系统通知
         */
        public static final String OfflineNotification = "offlineNotification";

    }

    public static class RabbitConstants{

        public static final String Im2UserService = "pipeline2UserService";

        public static final String Im2MessageService = "pipeline2MessageService";

        public static final String Im2GroupService = "pipeline2GroupService";

        public static final String Im2FriendshipService = "pipeline2FriendshipService";

        public static final String MessageService2Im = "messageService2Pipeline";

        public static final String GroupService2Im = "GroupService2Pipeline";

        public static final String FriendShip2Im = "friendShip2Pipeline";

        public static final String StoreP2PMessage = "storeP2PMessage";

        public static final String StoreGroupMessage = "storeGroupMessage";

        // 系统通知相关MQ配置
        public static final String SystemNotification = "systemNotification";
        
        // 系统广播消息相关MQ配置
        public static final String BroadcastMessage = "broadcastMessage";

        // 用于区分普通用户和游客的路由键
        public static final String NormalUserRoutingKey = "user.normal";
        public static final String GuestUserRoutingKey = "user.guest";
        public static final String AllUserRoutingKey = "user.all";

        // 直播间相关MQ配置
        public static final String LiveRoomExchange = "live_room_exchange";
        public static final String LiveRoomQueue = "live_room_message_queue";
        public static final String LiveRoomRoutingKeyPrefix = "live_room.";
        
        // 直播间分片相关常量
        public static final String LiveRoomShardingEnabled = "live_room_sharding_enabled";
        public static final String LiveRoomShardingCount = "live_room_sharding_count";
        public static final String LiveRoomShardingStrategy = "live_room_sharding_strategy";

        public static final String Im2LiveRoomService = "im2LiveRoomService";

    }

    public static class CallbackCommand{
        public static final String ModifyUserAfter = "user.modify.after";

        public static final String CreateGroupAfter = "group.create.after";

        public static final String UpdateGroupAfter = "group.update.after";

        public static final String DestoryGroupAfter = "group.destory.after";

        public static final String TransferGroupAfter = "group.transfer.after";

        public static final String GroupMemberAddBefore = "group.member.add.before";

        public static final String GroupMemberAddAfter = "group.member.add.after";

        public static final String GroupMemberDeleteAfter = "group.member.delete.after";

        public static final String AddFriendBefore = "friend.add.before";

        public static final String AddFriendAfter = "friend.add.after";

        public static final String UpdateFriendBefore = "friend.update.before";

        public static final String UpdateFriendAfter = "friend.update.after";

        public static final String DeleteFriendAfter = "friend.delete.after";

        public static final String AddBlackAfter = "black.add.after";

        public static final String DeleteBlack = "black.delete";

        public static final String SendMessageAfter = "message.send.after";

        public static final String SendMessageBefore = "message.send.before";

    }

    public static class SeqConstants {
        public static final String Message = "messageSeq";

        public static final String GroupMessage = "groupMessageSeq";


        public static final String Friendship = "friendshipSeq";

//        public static final String FriendshipBlack = "friendshipBlackSeq";

        public static final String FriendshipRequest = "friendshipRequestSeq";

        public static final String FriendshipGroup = "friendshipGrouptSeq";

        public static final String Group = "groupSeq";

        public static final String GroupApply = "groupApplySeq";

        public static final String Conversation = "conversationSeq";
        
        public static final String LiveRoomMessage = "liveRoomMessageSeq";

        /**
         * 系统通知序列号
         */
        public static final String Notification = "notification";

    }

}
