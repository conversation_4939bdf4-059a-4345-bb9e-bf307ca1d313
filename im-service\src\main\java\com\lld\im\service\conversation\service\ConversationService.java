package com.lld.im.service.conversation.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.lld.im.codec.pack.conversation.CreateConversationPack;
import com.lld.im.codec.pack.conversation.DeleteConversationPack;
import com.lld.im.codec.pack.conversation.UpdateConversationPack;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.config.AppConfig;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ConversationErrorCode;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.command.ConversationEventCommand;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.SyncResp;
import com.lld.im.common.model.message.MessageContent;
import com.lld.im.common.model.message.MessageReadedContent;
import com.lld.im.common.model.message.GroupChatMessageContent;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import com.lld.im.service.conversation.dao.mapper.ImConversationSetMapper;
import com.lld.im.service.conversation.model.DeleteConversationReq;
import com.lld.im.service.conversation.model.UpdateConversationReq;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.utils.MessageProducer;
import com.lld.im.service.utils.WriteUserSeq;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
public class ConversationService {

    private static final Logger logger = LoggerFactory.getLogger(ConversationService.class);

    @Autowired
    ImConversationSetMapper imConversationSetMapper;

    @Autowired
    MessageProducer messageProducer;

    @Autowired
    AppConfig appConfig;

    @Autowired
    RedisSeq redisSeq;

    @Autowired
    WriteUserSeq writeUserSeq;

    public String convertConversationId(Integer type,String fromId,String toId){
        return type + "_" + fromId + "_" + toId;
    }

    public void  messageMarkRead(MessageReadedContent messageReadedContent){

        String toId = messageReadedContent.getToId();
        if(messageReadedContent.getConversationType() == ConversationTypeEnum.GROUP.getCode()){
            toId = messageReadedContent.getGroupId();
        }
        String conversationId = convertConversationId(messageReadedContent.getConversationType(),
                messageReadedContent.getFromId(), toId);
        QueryWrapper<ImConversationSetEntity> query = new QueryWrapper<>();
        query.eq("conversation_id",conversationId);
        query.eq("app_id",messageReadedContent.getAppId());
        ImConversationSetEntity imConversationSetEntity = imConversationSetMapper.selectOne(query);
        if(imConversationSetEntity == null){
            // 只有新会话才生成序列号
            imConversationSetEntity = new ImConversationSetEntity();
            long seq = redisSeq.doGetSeq(messageReadedContent.getAppId() + ":" + Constants.SeqConstants.Conversation);
            imConversationSetEntity.setConversationId(conversationId);
            BeanUtils.copyProperties(messageReadedContent,imConversationSetEntity);
            imConversationSetEntity.setReadedSequence(messageReadedContent.getMessageSequence());
            imConversationSetEntity.setToId(toId);
            imConversationSetEntity.setSequence(seq);
            // 修复：消息已读时，lastMessageSequence应该设置为已读消息的序列号
            // 这种情况下表示这是第一次创建会话记录，已读消息就是最新消息
            imConversationSetEntity.setLastMessageSequence(messageReadedContent.getMessageSequence());
            imConversationSetMapper.insert(imConversationSetEntity);
            writeUserSeq.writeUserSeq(messageReadedContent.getAppId(),
                    messageReadedContent.getFromId(),Constants.SeqConstants.Conversation,seq);
        }else{
            // 已存在的会话，只更新已读状态，不更新会话序列号
            imConversationSetEntity.setReadedSequence(messageReadedContent.getMessageSequence());

            // 修复：消息已读时，只有当已读消息序列号大于当前lastMessageSequence时才更新
            // 这确保lastMessageSequence始终保存最新消息的序列号
            if (imConversationSetEntity.getLastMessageSequence() == null ||
                messageReadedContent.getMessageSequence() > imConversationSetEntity.getLastMessageSequence()) {
                imConversationSetEntity.setLastMessageSequence(messageReadedContent.getMessageSequence());
            }

            imConversationSetMapper.updateReadStatus(imConversationSetEntity);
            // 不更新用户序列号缓存，因为会话序列号没有变化
        }
    }

    /**
     * @description: 删除会话
     * @param
     * @return com.lld.im.common.ResponseVO
     * <AUTHOR>
     */
    public ResponseVO deleteConversation(DeleteConversationReq req){

        //置顶 有免打扰
//        QueryWrapper<ImConversationSetEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("conversation_id",req.getConversationId());
//        queryWrapper.eq("app_id",req.getAppId());
//        ImConversationSetEntity imConversationSetEntity = imConversationSetMapper.selectOne(queryWrapper);
//        if(imConversationSetEntity != null){
//            imConversationSetEntity.setIsMute(0);
//            imConversationSetEntity.setIsTop(0);
//            imConversationSetMapper.update(imConversationSetEntity,queryWrapper);
//        }

        if(appConfig.getDeleteConversationSyncMode() == 1){
            DeleteConversationPack pack = new DeleteConversationPack();
            pack.setConversationId(req.getConversationId());
            messageProducer.sendToUserExceptClient(req.getFromId(),
                    ConversationEventCommand.CONVERSATION_DELETE,
                    pack,new ClientInfo(req.getAppId(),req.getClientType(),
                            req.getImei()));
        }
        return ResponseVO.successResponse();
    }

    /**
     * @description: 更新会话 置顶or免打扰
     * @param
     * @return com.lld.im.common.ResponseVO
     * <AUTHOR>
     */
    public ResponseVO updateConversation(UpdateConversationReq req){




        if(req.getIsTop() == null && req.getIsMute() == null){
            return ResponseVO.errorResponse(ConversationErrorCode.CONVERSATION_UPDATE_PARAM_ERROR);
        }
        QueryWrapper<ImConversationSetEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("conversation_id",req.getConversationId());
        queryWrapper.eq("app_id",req.getAppId());
        ImConversationSetEntity imConversationSetEntity = imConversationSetMapper.selectOne(queryWrapper);
        if(imConversationSetEntity != null){
            // 会话设置变更不需要更新会话序列号，通过实时消息推送同步状态
            long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Conversation);
            if(req.getIsTop() != null){
                imConversationSetEntity.setIsTop(req.getIsTop());
            }
            if(req.getIsMute() != null){
                imConversationSetEntity.setIsMute(req.getIsMute());
            }
            imConversationSetEntity.setSequence(seq);
            imConversationSetMapper.update(imConversationSetEntity,queryWrapper);
            writeUserSeq.writeUserSeq(req.getAppId(), req.getFromId(),
                    Constants.SeqConstants.Conversation, seq);

            UpdateConversationPack pack = new UpdateConversationPack();
            pack.setConversationId(req.getConversationId());
            pack.setIsMute(imConversationSetEntity.getIsMute());
            pack.setIsTop(imConversationSetEntity.getIsTop());
            pack.setSequence(imConversationSetEntity.getSequence()); // 使用原有的序列号
            pack.setConversationType(imConversationSetEntity.getConversationType());
            messageProducer.sendToUserExceptClient(req.getFromId(),
                    ConversationEventCommand.CONVERSATION_UPDATE,
                    pack,new ClientInfo(req.getAppId(),req.getClientType(),
                            req.getImei()));
        }
        return ResponseVO.successResponse();
    }

    public ResponseVO syncConversationSet(SyncReq req) {
        // 1. 参数验证和默认值设置
        if (req.getMaxLimit() == null || req.getMaxLimit() > 100) {
            req.setMaxLimit(100);
        }
        if (req.getLastSequence() == null) {
            req.setLastSequence(0L);
        }

        SyncResp<ImConversationSetEntity> resp = new SyncResp<>();

        // 2. 获取服务端当前最大序列号
        Long serverMaxSeq = imConversationSetMapper.geConversationSetMaxSeq(req.getAppId(), req.getOperater());

        // 3. 提前终止优化：如果客户端的lastSequence已经是最新的，直接返回空结果
        if (req.getLastSequence() >= serverMaxSeq) {
            resp.setMaxSequence(serverMaxSeq);
            resp.setDataList(new ArrayList<>());
            resp.setCompleted(true);
            return ResponseVO.successResponse(resp);
        }

        // 4. 查询增量数据：seq > req.getLastSequence limit maxLimit
        QueryWrapper<ImConversationSetEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("from_id", req.getOperater());
        queryWrapper.gt("sequence", req.getLastSequence());
        queryWrapper.eq("app_id", req.getAppId());
        queryWrapper.last(" limit " + req.getMaxLimit());
        queryWrapper.orderByAsc("sequence");
        List<ImConversationSetEntity> list = imConversationSetMapper.selectList(queryWrapper);

        if (!CollectionUtils.isEmpty(list)) {
            ImConversationSetEntity maxSeqEntity = list.get(list.size() - 1);
            resp.setDataList(list);

            // 5. 设置当前批次的最大序列号作为maxSequence（修正语义）
            Long currentBatchMaxSeq = maxSeqEntity.getSequence();
            resp.setMaxSequence(currentBatchMaxSeq);

            // 6. 判断是否同步完成：当前批次最后一条记录的序列号 >= 服务端最大序列号
            resp.setCompleted(currentBatchMaxSeq >= serverMaxSeq);
            return ResponseVO.successResponse(resp);
        }

        // 7. 没有查询到数据，说明已经同步完成
        resp.setMaxSequence(serverMaxSeq);
        resp.setCompleted(true);
        return ResponseVO.successResponse(resp);
    }



    /**
     * 消息发送时更新会话记录（支持实时推送）
     * @param messageContent 单聊消息内容
     * @param clientType 客户端类型
     * @param imei 设备标识
     */
    public void updateConversationSetForP2P(MessageContent messageContent, Integer clientType, String imei) {
        try {
            // 为发送方创建或更新会话记录（排除发送方客户端的推送）
            createOrUpdateP2PConversation(
                messageContent.getAppId(),
                messageContent.getFromId(),
                messageContent.getToId(),
                ConversationTypeEnum.P2P.getCode(),
                messageContent.getMessageSequence(),
                clientType,
                imei
            );

            // 为接收方创建或更新会话记录（推送给接收方所有端）
            createOrUpdateP2PConversation(
                messageContent.getAppId(),
                messageContent.getToId(),
                messageContent.getFromId(),
                ConversationTypeEnum.P2P.getCode(),
                messageContent.getMessageSequence(),
                null,
                null
            );

            logger.info("P2P会话记录更新成功: appId={}, fromId={}, toId={}, messageSeq={}",
                messageContent.getAppId(), messageContent.getFromId(), messageContent.getToId(), messageContent.getMessageSequence());
        } catch (Exception e) {
            logger.error("P2P会话记录更新失败: appId={}, fromId={}, toId={}, error={}",
                messageContent.getAppId(), messageContent.getFromId(), messageContent.getToId(), e.getMessage(), e);
        }
    }

    /**
     * 群聊消息发送时更新会话记录 - 优化版本，使用批量操作
     * @param messageContent 群聊消息内容
     * @param memberIds 群成员ID列表
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateConversationSetForGroup(GroupChatMessageContent messageContent, List<String> memberIds) {
        try {
            // 批量处理群聊会话记录更新
            updateGroupConversationsBatch(
                messageContent.getAppId(),
                messageContent.getGroupId(),
                memberIds,
                messageContent.getMessageSequence()
            );

            logger.info("群聊会话记录更新成功: appId={}, groupId={}, memberCount={}, messageSeq={}",
                messageContent.getAppId(), messageContent.getGroupId(), memberIds.size(), messageContent.getMessageSequence());
        } catch (Exception e) {
            logger.error("群聊会话记录更新失败: appId={}, groupId={}, error={}",
                messageContent.getAppId(), messageContent.getGroupId(), e.getMessage(), e);
            throw e; // 重新抛出异常以触发事务回滚
        }
    }

    /**
     * 批量更新群聊会话记录 - 性能优化版本
     */
    private void updateGroupConversationsBatch(Integer appId, String groupId, List<String> memberIds, Long messageSequence) {
        List<ImConversationSetEntity> entitiesToInsert = new ArrayList<>();
        List<String> existingConversationIds = new ArrayList<>();

        // 批量查询现有会话记录
        List<String> conversationIds = new ArrayList<>();
        for (String memberId : memberIds) {
            conversationIds.add(convertConversationId(ConversationTypeEnum.GROUP.getCode(), memberId, groupId));
        }

        QueryWrapper<ImConversationSetEntity> query = new QueryWrapper<>();
        query.eq("app_id", appId);
        query.in("conversation_id", conversationIds);
        List<ImConversationSetEntity> existingEntities = imConversationSetMapper.selectList(query);

        // 收集现有会话记录的ID
        for (ImConversationSetEntity entity : existingEntities) {
            existingConversationIds.add(entity.getConversationId());
        }

        // 只有新会话才生成序列号
        Long conversationSeq = null;
        boolean hasNewConversations = false;

        // 为不存在的会话记录创建新实体
        for (String memberId : memberIds) {
            String conversationId = convertConversationId(ConversationTypeEnum.GROUP.getCode(), memberId, groupId);
            if (!existingConversationIds.contains(conversationId)) {
                if (!hasNewConversations) {
                    // 只在有新会话时才生成序列号
                    conversationSeq = redisSeq.doGetSeq(appId + ":" + Constants.SeqConstants.Conversation);
                    hasNewConversations = true;
                }
                ImConversationSetEntity newEntity = createGroupConversationEntity(
                    appId, memberId, groupId, ConversationTypeEnum.GROUP.getCode(), conversationSeq, messageSequence);
                entitiesToInsert.add(newEntity);
            }
        }

        // 批量插入新记录
        if (!entitiesToInsert.isEmpty()) {
            imConversationSetMapper.batchInsertOrUpdateSequence(entitiesToInsert);
        }

        // 批量更新现有记录的消息序列号（不更新会话序列号）
        for (ImConversationSetEntity entity : existingEntities) {
            imConversationSetMapper.updateLastMessageSequence(entity.getConversationId(), entity.getAppId(), messageSequence);
        }

        // 只为新会话的用户更新序列号缓存并推送通知
        if (hasNewConversations && conversationSeq != null) {
            for (String memberId : memberIds) {
                String conversationId = convertConversationId(ConversationTypeEnum.GROUP.getCode(), memberId, groupId);
                if (!existingConversationIds.contains(conversationId)) {
                    writeUserSeq.writeUserSeq(appId, memberId, Constants.SeqConstants.Conversation, conversationSeq);

                    // 为新会话推送创建通知
                    for (ImConversationSetEntity newEntity : entitiesToInsert) {
                        if (newEntity.getFromId().equals(memberId)) {
                            sendConversationCreateNotification(newEntity, null, null);
                            break;
                        }
                    }
                }
            }
        }
    }



    /**
     * 创建或更新单聊会话记录（支持实时推送）
     */
    private void createOrUpdateP2PConversation(Integer appId, String fromId, String toId, Integer conversationType, Long messageSequence, Integer clientType, String imei) {
        String conversationId = convertConversationId(conversationType, fromId, toId);

        // 检查会话记录是否存在
        QueryWrapper<ImConversationSetEntity> query = new QueryWrapper<>();
        query.eq("conversation_id", conversationId);
        query.eq("app_id", appId);

        ImConversationSetEntity existingEntity = imConversationSetMapper.selectOne(query);

        if (existingEntity == null) {
            // 只有新会话才生成序列号用于增量同步
            long conversationSeq = redisSeq.doGetSeq(appId + ":" + Constants.SeqConstants.Conversation);

            // 创建新的会话记录
            ImConversationSetEntity newEntity = createP2PConversationEntity(appId, fromId, toId, conversationType, conversationSeq, messageSequence);
            imConversationSetMapper.insert(newEntity);
            writeUserSeq.writeUserSeq(appId, fromId, Constants.SeqConstants.Conversation, conversationSeq);

            // 实时推送新会话创建通知
            sendConversationCreateNotification(newEntity, clientType, imei);

            logger.info("新建单聊会话并推送通知: appId={}, conversationId={}, fromId={}, toId={}",
                appId, conversationId, fromId, toId);
        } else {
            // 已存在的会话只更新消息序列号，不更新会话序列号
            imConversationSetMapper.updateLastMessageSequence(conversationId, appId, messageSequence);
            // 不调用 writeUserSeq，因为会话序列号没有变化
        }
    }



    /**
     * 创建单聊会话记录实体
     */
    private ImConversationSetEntity createP2PConversationEntity(Integer appId, String fromId, String toId, Integer conversationType, Long conversationSequence, Long messageSequence) {
        ImConversationSetEntity entity = new ImConversationSetEntity();
        entity.setConversationId(convertConversationId(conversationType, fromId, toId));
        entity.setConversationType(conversationType);
        entity.setFromId(fromId);
        entity.setToId(toId);
        entity.setAppId(appId);
        entity.setIsMute(0);
        entity.setIsTop(0);
        entity.setReadedSequence(0L);

        // 会话序列号用于增量同步
        entity.setSequence(conversationSequence);
        // 消息序列号用于未读数量计算
        entity.setLastMessageSequence(messageSequence);

        return entity;
    }

    /**
     * 创建群聊会话记录实体
     */
    private ImConversationSetEntity createGroupConversationEntity(Integer appId, String memberId, String groupId, Integer conversationType, Long conversationSequence, Long messageSequence) {
        ImConversationSetEntity entity = new ImConversationSetEntity();
        entity.setConversationId(convertConversationId(conversationType, memberId, groupId));
        entity.setConversationType(conversationType);
        entity.setFromId(memberId);
        entity.setToId(groupId);
        entity.setAppId(appId);
        entity.setIsMute(0);
        entity.setIsTop(0);
        entity.setReadedSequence(0L);

        // 会话序列号用于增量同步
        entity.setSequence(conversationSequence);
        // 消息序列号用于未读数量计算
        entity.setLastMessageSequence(messageSequence);

        return entity;
    }



    /**
     * 发送新会话创建通知
     */
    private void sendConversationCreateNotification(ImConversationSetEntity entity, Integer clientType, String imei) {
        try {
            CreateConversationPack pack = new CreateConversationPack();
            pack.setConversationId(entity.getConversationId());
            pack.setConversationType(entity.getConversationType());
            pack.setFromId(entity.getFromId());
            pack.setToId(entity.getToId());
            pack.setSequence(entity.getSequence());
            pack.setLastMessageSequence(entity.getLastMessageSequence());
            pack.setIsTop(entity.getIsTop());
            pack.setIsMute(entity.getIsMute());
            pack.setReadedSequence(entity.getReadedSequence());

            // 推送给会话的拥有者（fromId）
            if (clientType != null && imei != null) {
                // 排除当前发送消息的客户端，避免重复通知
                messageProducer.sendToUserExceptClient(entity.getFromId(),
                        ConversationEventCommand.CONVERSATION_CREATE,
                        pack, new ClientInfo(entity.getAppId(), clientType, imei));
            } else {
                // 推送给用户的所有在线端
                messageProducer.sendToUser(entity.getFromId(),
                        ConversationEventCommand.CONVERSATION_CREATE,
                        pack, entity.getAppId());
            }

            logger.debug("新会话创建通知发送成功: conversationId={}, fromId={}",
                entity.getConversationId(), entity.getFromId());
        } catch (Exception e) {
            logger.error("发送新会话创建通知失败: conversationId={}, error={}",
                entity.getConversationId(), e.getMessage(), e);
        }
    }
}
