package com.lld.im.service.liveroom.model.data;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 直播间消息数据模型
 * 用于统一直播间消息的数据结构，避免字段重复和嵌套问题
 */
@Data
@Accessors(chain = true)
public class LiveRoomMessageData {

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 操作类型 (join, leave, chat, gift, like, mute, kick, announcement等)
     */
    private String action;

    /**
     * 消息类型 (1:文本 2:图片 3:语音 4:视频 5:表情 6:礼物 7:点赞 8:系统 9:加入 10:离开)
     */
    private Integer messageType;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 发送者昵称
     */
    private String fromNickname;

    /**
     * 发送者头像
     */
    private String fromAvatar;

    /**
     * 发送者角色 (1-主播 2-管理员 3-VIP用户 4-普通用户 5-游客)
     */
    private Integer fromRole;

    /**
     * 接收者ID (用于礼物等点对点消息)
     */
    private String toId;

    /**
     * 接收者昵称
     */
    private String toNickname;

    /**
     * 是否游客
     */
    private Boolean isGuest;

    /**
     * 消息序列号
     */
    private Long sequence;

    /**
     * 发送时间
     */
    private Long sendTime;

    /**
     * 礼物ID (礼物消息专用)
     */
    private String giftId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 礼物数量
     */
    private Integer giftCount;

    /**
     * 礼物价值
     */
    private Integer giftValue;

    /**
     * 禁言原因 (禁言消息专用)
     */
    private String muteReason;

    /**
     * 禁言时长 (秒)
     */
    private Integer muteDuration;

    /**
     * 踢出原因 (踢人消息专用)
     */
    private String kickReason;

    /**
     * 公告内容 (公告消息专用)
     */
    private String announcement;

    /**
     * 扩展字段，用于未来功能扩展
     */
    private Map<String, Object> extra;

    /**
     * 创建聊天消息数据
     */
    public static LiveRoomMessageData createChatMessage(String roomId, String userId, String content, 
                                                       Integer messageType, String messageId) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("chat")
                .setContent(content)
                .setMessageType(messageType)
                .setMessageId(messageId);
    }

    /**
     * 创建加入直播间消息数据
     */
    public static LiveRoomMessageData createJoinMessage(String roomId, String userId, String nickname,
                                                       String avatar, Integer role, Boolean isGuest) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("join")
                .setFromNickname(nickname)
                .setFromAvatar(avatar)
                .setFromRole(role)
                .setIsGuest(isGuest)
                .setMessageType(9); // 加入消息类型
    }

    /**
     * 创建主播创建直播间后自动加入的消息数据
     */
    public static LiveRoomMessageData createAnchorJoinMessage(String roomId, String userId, String nickname,
                                                             String avatar, Integer role) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("create")
                .setFromNickname(nickname)
                .setFromAvatar(avatar)
                .setFromRole(role)
                .setIsGuest(false) // 主播不是游客
                .setMessageType(8); // 系统消息类型
    }

    /**
     * 创建离开直播间消息数据
     */
    public static LiveRoomMessageData createLeaveMessage(String roomId, String userId,String nickname, String reason) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setFromNickname(nickname)
                .setAction("leave")
                .setContent(reason)
                .setMessageType(10); // 离开消息类型
    }

    /**
     * 创建礼物消息数据
     */
    public static LiveRoomMessageData createGiftMessage(String roomId, String fromId, String toId, 
                                                       String giftId, String giftName, Integer giftCount, 
                                                       Integer giftValue, String messageId) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(fromId)
                .setToId(toId)
                .setAction("gift")
                .setGiftId(giftId)
                .setGiftName(giftName)
                .setGiftCount(giftCount)
                .setGiftValue(giftValue)
                .setMessageId(messageId)
                .setMessageType(6); // 礼物消息类型
    }

    /**
     * 创建点赞消息数据
     */
    public static LiveRoomMessageData createLikeMessage(String roomId, String userId, String messageId) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("like")
                .setMessageId(messageId)
                .setMessageType(7); // 点赞消息类型
    }

    /**
     * 创建禁言消息数据
     */
    public static LiveRoomMessageData createMuteMessage(String roomId, String userId, String targetUserId, 
                                                       String reason, Integer duration) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setToId(targetUserId)
                .setAction("mute")
                .setMuteReason(reason)
                .setMuteDuration(duration)
                .setMessageType(8); // 系统消息类型
    }

    /**
     * 创建踢人消息数据
     */
    public static LiveRoomMessageData createKickMessage(String roomId, String userId, String targetUserId, 
                                                       String reason) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setToId(targetUserId)
                .setAction("kick")
                .setKickReason(reason)
                .setMessageType(8); // 系统消息类型
    }

    /**
     * 创建公告消息数据
     */
    public static LiveRoomMessageData createAnnouncementMessage(String roomId, String userId, String announcement) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("announcement")
                .setAnnouncement(announcement)
                .setMessageType(8); // 系统消息类型
    }

    /**
     * 创建全员禁言消息数据
     */
    public static LiveRoomMessageData createMuteAllMessage(String roomId, String userId, Boolean muteAll) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("muteAll")
                .setContent(muteAll ? "开启全员禁言" : "关闭全员禁言")
                .setMessageType(8); // 系统消息类型
    }

    /**
     * 创建关闭直播间消息数据
     */
    public static LiveRoomMessageData createCloseMessage(String roomId, String userId, String reason) {
        return new LiveRoomMessageData()
                .setRoomId(roomId)
                .setUserId(userId)
                .setAction("close")
                .setContent(reason != null ? reason : "主播关闭了直播间")
                .setMessageType(8); // 系统消息类型
    }
}
