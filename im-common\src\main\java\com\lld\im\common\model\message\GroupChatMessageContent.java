package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 群聊消息内容模型
 * @description: 群组聊天消息内容数据模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "群聊消息内容模型")
@Data
public class GroupChatMessageContent extends MessageContent {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "群组的唯一标识")
    private String groupId;

    @ApiModelProperty(value = "群成员ID列表", example = "[\"user123\", \"user456\", \"user789\"]", notes = "接收消息的群成员用户ID列表")
    private List<String> memberId;

}
