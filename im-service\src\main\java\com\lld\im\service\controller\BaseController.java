package com.lld.im.service.controller;

import com.lld.im.common.model.RequestBase;
import com.lld.im.service.interceptor.GateWayInterceptor;
import com.lld.im.service.utils.RequestHeaderUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 控制器基类
 * 提供公共参数处理的统一方法
 * 
 * @description: 为所有控制器提供公共参数自动填充功能
 * @author: IM System
 * @version: 1.0
 */
public abstract class BaseController {
    
    /**
     * 自动填充请求对象的公共参数
     * 从ThreadLocal或请求头中获取公共参数并设置到请求对象中
     * 
     * @param request 需要填充公共参数的请求对象
     */
    protected void fillCommonParams(RequestBase request) {
        if (request == null) {
            return;
        }
        
        // 优先从ThreadLocal获取（拦截器已处理）
        Integer appId = GateWayInterceptor.getCurrentAppId();
        String identifier = GateWayInterceptor.getCurrentUser();
        
        // 如果ThreadLocal中没有，则从请求头获取
        if (appId == null) {
            appId = RequestHeaderUtils.getAppId();
        }
        if (StringUtils.isBlank(identifier)) {
            identifier = RequestHeaderUtils.getIdentifier();
        }
        
        // 设置公共参数
        if (appId != null) {
            request.setAppId(appId);
        }
        if (StringUtils.isNotBlank(identifier)) {
            request.setOperater(identifier);
        }
        
        // 设置其他可选参数
        Integer clientType = RequestHeaderUtils.getClientType();
        if (clientType != null) {
            request.setClientType(clientType);
        }
        
        String imei = RequestHeaderUtils.getImei();
        if (StringUtils.isNotBlank(imei)) {
            request.setImei(imei);
        }
    }
    
    /**
     * 获取当前请求的应用ID
     * 优先从ThreadLocal获取，其次从请求头获取
     */
    protected Integer getCurrentAppId() {
        Integer appId = GateWayInterceptor.getCurrentAppId();
        if (appId != null) {
            return appId;
        }
        return RequestHeaderUtils.getAppId();
    }
    
    /**
     * 获取当前请求的用户标识
     * 优先从ThreadLocal获取，其次从请求头获取
     */
    protected String getCurrentIdentifier() {
        String identifier = GateWayInterceptor.getCurrentUser();
        if (StringUtils.isNotBlank(identifier)) {
            return identifier;
        }
        return RequestHeaderUtils.getIdentifier();
    }
    
    /**
     * 获取当前请求的用户签名
     * 优先从ThreadLocal获取，其次从请求头获取
     */
    protected String getCurrentUserSign() {
        String userSign = GateWayInterceptor.getCurrentUserSign();
        if (StringUtils.isNotBlank(userSign)) {
            return userSign;
        }
        return RequestHeaderUtils.getUserSign();
    }
    
    /**
     * 验证公共参数是否完整
     */
    protected boolean validateCommonParams() {
        Integer appId = getCurrentAppId();
        String identifier = getCurrentIdentifier();
        String userSign = getCurrentUserSign();
        
        return appId != null && 
               StringUtils.isNotBlank(identifier) && 
               StringUtils.isNotBlank(userSign);
    }
    
    /**
     * 创建并填充公共参数的请求对象
     */
    protected <T extends RequestBase> T createRequestWithCommonParams(T request) {
        fillCommonParams(request);
        return request;
    }
}
