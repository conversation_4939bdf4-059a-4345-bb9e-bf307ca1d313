# 生产环境配置示例
# 复制此文件为 .env 并修改为实际值

# Redis集群配置
REDIS_PASSWORD=your_strong_redis_password_here_min_16_chars
REDIS_NODE1_HOST=*********
REDIS_NODE1_PORT=7000
REDIS_NODE2_HOST=*********
REDIS_NODE2_PORT=7000
REDIS_NODE3_HOST=*********
REDIS_NODE3_PORT=7000

# RabbitMQ集群配置
RABBITMQ_NODE1_HOST=*********
RABBITMQ_NODE1_PORT=5672
RABBITMQ_NODE2_HOST=*********
RABBITMQ_NODE2_PORT=5672
RABBITMQ_NODE3_HOST=*********
RABBITMQ_NODE3_PORT=5672
RABBITMQ_VHOST=/im-production
RABBITMQ_USERNAME=im_user
RABBITMQ_PASSWORD=your_strong_rabbitmq_password_here

# Nacos集群配置
NACOS_SERVERS=*********:8848,*********:8848,*********:8848
NACOS_NAMESPACE=im-production
NACOS_GROUP=PRODUCTION_GROUP
NACOS_USERNAME=nacos_admin
NACOS_PASSWORD=your_strong_nacos_password_here

# SSL/TLS配置（可选）
# RABBITMQ_SSL_ENABLED=true
# RABBITMQ_KEYSTORE_PATH=/path/to/keystore.p12
# RABBITMQ_KEYSTORE_PASSWORD=keystore_password
# RABBITMQ_TRUSTSTORE_PATH=/path/to/truststore.p12
# RABBITMQ_TRUSTSTORE_PASSWORD=truststore_password
