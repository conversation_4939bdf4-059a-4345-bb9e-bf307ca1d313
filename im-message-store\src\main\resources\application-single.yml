# 原始单机模式配置备份
# 此文件保留了修改前的原始配置，可用于回退到单机模式



spring:
  profiles:
    active: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: root
    url: ******************************************************************************************
    username: root

  redis:
    host: 127.0.0.1
    port: 6379
    jedis:
      pool:
        max-active: 100
        max-idle: 100
        max-wait: 1000
        min-idle: 10
    password:
  rabbitmq:
    host: localhost
    port: 5672
    addresses: localhost
    username: guest
    password: guest
    #    virtual-host:
    listener:
      simple:
        concurrency: 5
        max-concurrency: 10
        acknowledge-mode: MANUAL
        prefetch: 1
    publisher-confirms: true
    publisher-returns: true
    template:
      mandatory: true
    cache:
      connection:
        mode: channel
      channel:
        size: 36
        checkout-timeout: 0



# logger 配置
logging:
  config: classpath:logback-spring.xml


mybatis-plus:

  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  mapper-locations: classpath*:mapper/*.xml
  global-config:
    db-config:
      update-strategy: NOT_EMPTY

#mybatis:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
