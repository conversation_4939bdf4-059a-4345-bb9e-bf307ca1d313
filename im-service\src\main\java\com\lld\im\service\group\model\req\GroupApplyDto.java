package com.lld.im.service.group.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 群申请信息DTO
 * @description: 群申请数据传输对象
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "群申请信息数据传输对象")
@Data
public class GroupApplyDto {

    @ApiModelProperty(value = "申请ID", example = "123456", notes = "群申请记录的唯一标识")
    private Long applyId;

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "申请加入的群组唯一标识")
    private String groupId;

    @ApiModelProperty(value = "申请人用户ID", required = true, example = "user123", notes = "发起申请的用户唯一标识")
    private String applicantId;

    @ApiModelProperty(value = "申请状态", example = "0", notes = "申请状态：0-待审批 1-已同意 2-已拒绝 3-已撤销")
    private Integer applyStatus;

    @ApiModelProperty(value = "申请理由", example = "希望加入技术交流群", notes = "用户申请加群的理由说明")
    private String applyReason;

    @ApiModelProperty(value = "申请时间", example = "1672531200000", notes = "申请提交的时间戳，单位毫秒")
    private Long applyTime;

    @ApiModelProperty(value = "审批人用户ID", example = "admin123", notes = "处理申请的群主或管理员用户ID")
    private String approverId;

    @ApiModelProperty(value = "审批时间", example = "1672531400000", notes = "申请处理的时间戳，单位毫秒")
    private Long approveTime;

    @ApiModelProperty(value = "拒绝理由", example = "群已满员", notes = "拒绝申请的理由说明")
    private String rejectReason;

    @ApiModelProperty(value = "申请人昵称", example = "小明", notes = "申请人的用户昵称（用于显示）")
    private String applicantNickname;

    @ApiModelProperty(value = "申请人头像", example = "https://example.com/avatar.jpg", notes = "申请人的头像URL（用于显示）")
    private String applicantAvatar;

    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "群组名称（用于显示）")
    private String groupName;

    @ApiModelProperty(value = "审批人昵称", example = "管理员", notes = "审批人的用户昵称（用于显示）")
    private String approverNickname;
}
