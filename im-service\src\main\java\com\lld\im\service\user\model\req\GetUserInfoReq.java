package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 获取用户信息请求
 */
@ApiModel(description = "获取用户信息请求模型")
@Data
public class GetUserInfoReq extends RequestBase {

    @ApiModelProperty(value = "用户ID列表", example = "[\"user123\", \"user456\"]", notes = "要查询信息的用户ID列表，为空则查询所有用户")
    private List<String> userIds;

}
