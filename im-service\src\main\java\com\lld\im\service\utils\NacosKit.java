package com.lld.im.service.utils;

import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: Nacos 工具类，替代ZKit
 **/
@Component
public class NacosKit {

    private static Logger logger = LoggerFactory.getLogger(NacosKit.class);

    @Autowired
    private NamingService namingService;

    /**
     * 获取所有TCP服务节点 - 兼容原有ZKit接口
     *
     * @return TCP节点列表，格式为 ip:port
     */
    public List<String> getAllTcpNode() {
        try {
            List<Instance> instances = namingService.getAllInstances("im-tcp-service");
            List<String> result = instances.stream()
                    .filter(Instance::isHealthy)
                    .map(instance -> instance.getIp() + ":" + instance.getPort())
                    .collect(Collectors.toList());
            
            logger.debug("Query all TCP nodes success, count=[{}]", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Get TCP nodes from Nacos failed", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有WebSocket服务节点 - 兼容原有ZKit接口
     *
     * @return WebSocket节点列表，格式为 ip:port
     */
    public List<String> getAllWebNode() {
        try {
            List<Instance> instances = namingService.getAllInstances("im-websocket-service");
            List<String> result = instances.stream()
                    .filter(Instance::isHealthy)
                    .map(instance -> instance.getIp() + ":" + instance.getPort())
                    .collect(Collectors.toList());
            
            logger.debug("Query all WebSocket nodes success, count=[{}]", result.size());
            return result;
        } catch (Exception e) {
            logger.error("Get WebSocket nodes from Nacos failed", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定服务的所有健康实例
     *
     * @param serviceName 服务名称
     * @return 实例列表
     */
    public List<Instance> getAllInstances(String serviceName) {
        try {
            return namingService.getAllInstances(serviceName);
        } catch (Exception e) {
            logger.error("Get instances for service [{}] failed", serviceName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指定服务的健康实例数量
     *
     * @param serviceName 服务名称
     * @return 健康实例数量
     */
    public int getHealthyInstanceCount(String serviceName) {
        try {
            List<Instance> instances = namingService.getAllInstances(serviceName);
            return (int) instances.stream().filter(Instance::isHealthy).count();
        } catch (Exception e) {
            logger.error("Get healthy instance count for service [{}] failed", serviceName, e);
            return 0;
        }
    }
}
