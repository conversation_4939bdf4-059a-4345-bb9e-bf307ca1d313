# IM-TCP 配置文件说明

## 配置文件列表

### 1. config.yml (默认配置)
- **用途**: 单机开发环境配置
- **Redis**: 单机模式 (localhost:6379)
- **RabbitMQ**: 单机模式 (localhost:5672)
- **适用场景**: 本地开发、测试

### 2. config-docker-cluster.yml (Docker集群配置)
- **用途**: Docker集群环境配置
- **Redis**: 集群模式 (localhost:7000-7002)
- **RabbitMQ**: 集群模式 (localhost:5672-5674)
- **适用场景**: Docker集群部署

### 3. config-cluster-example.yml (集群配置示例)
- **用途**: 生产环境集群配置模板
- **Redis**: 集群模式 (可配置多台服务器)
- **RabbitMQ**: 集群模式 (可配置多台服务器)
- **适用场景**: 生产环境部署参考

### 4. config-sentinel-example.yml (哨兵配置示例)
- **用途**: Redis哨兵模式配置模板
- **Redis**: 哨兵模式 (高可用)
- **RabbitMQ**: 集群模式
- **适用场景**: 需要Redis高可用的环境

## 使用方法

### 启动Docker集群环境

1. **启动集群服务**:
```bash
# 在项目根目录执行
docker-compose up -d
```

2. **使用集群配置启动应用**:
```bash
# 方法1: 指定配置文件
java -jar im-tcp.jar --spring.config.location=classpath:config-docker-cluster.yml

# 方法2: 复制配置文件
cp config-docker-cluster.yml config.yml
java -jar im-tcp.jar
```

### 配置切换

#### 开发环境 → Docker集群环境
```bash
# 备份原配置
cp config.yml config-dev.yml

# 使用Docker集群配置
cp config-docker-cluster.yml config.yml
```

#### Docker集群环境 → 生产环境
```bash
# 基于集群示例配置修改
cp config-cluster-example.yml config-prod.yml
# 修改其中的IP地址和端口为实际生产环境地址
```

## 当前Docker集群信息

### Redis Cluster
- **节点**: 3个主节点，无副本
- **端口映射**:
  - Node 1: localhost:7000 → redis-node-1:6379
  - Node 2: localhost:7001 → redis-node-2:6379
  - Node 3: localhost:7002 → redis-node-3:6379
- **密码**: 123456
- **槽位分配**:
  - Node 1: 0-5460 (5461个槽位)
  - Node 2: 5461-10922 (5462个槽位)
  - Node 3: 10923-16383 (5461个槽位)

### RabbitMQ Cluster
- **节点**: 3个disc节点
- **端口映射**:
  - Node 1: localhost:5672 → rabbitmq-node-1:5672
  - Node 2: localhost:5673 → rabbitmq-node-2:5672
  - Node 3: localhost:5674 → rabbitmq-node-3:5672
- **管理界面**:
  - Node 1: http://localhost:15672
  - Node 2: http://localhost:15673
  - Node 3: http://localhost:15674
- **认证**: admin/admin123

## 配置参数说明

### Redis配置
```yaml
redis:
  mode: cluster          # 模式: single/sentinel/cluster
  database: 0           # 数据库编号
  password: 123456      # 密码
  timeout: 3000         # 超时时间(毫秒)
  poolMinIdle: 8        # 连接池最小空闲连接数
  poolConnTimeout: 3000 # 连接超时时间(毫秒)
  poolSize: 10          # 连接池大小
```

### RabbitMQ配置
```yaml
rabbitmq:
  addresses:                    # 集群地址列表
    - host: localhost
      port: 5672
  virtualHost: /               # 虚拟主机
  userName: admin              # 用户名
  password: admin123           # 密码
  connectionTimeout: 5000      # 连接超时(毫秒)
  requestedHeartbeat: 30       # 心跳间隔(秒)
  networkRecoveryInterval: 5000 # 网络恢复间隔(毫秒)
  automaticRecoveryEnabled: true # 自动恢复
```

## 故障排除

### 连接问题
1. **检查集群状态**:
```bash
# Redis集群状态
docker exec redis-node-1 redis-cli -a 123456 cluster info

# RabbitMQ集群状态
docker exec rabbitmq-node-1 rabbitmqctl cluster_status
```

2. **检查端口占用**:
```bash
netstat -an | findstr "7000\|7001\|7002\|5672\|5673\|5674"
```

3. **查看服务日志**:
```bash
docker-compose logs -f redis-node-1
docker-compose logs -f rabbitmq-node-1
```

### 配置验证
- 启动应用前，确保配置文件中的地址和端口与实际集群环境匹配
- 检查用户名密码是否正确
- 确认网络连通性
