# IM系统调试脚本使用指南

## 📋 保留的调试脚本

经过整理，现在保留了以下核心调试脚本：

### 1. `debug.ps1` ⭐ 主要推荐
**功能**: 启动调试服务并显示实时日志
**特点**: 
- ✅ 实时显示服务日志
- ✅ 支持交互式选择服务
- ✅ 可以看到启动过程和错误信息
- ✅ 支持Ctrl+C停止服务

**使用方法**:
```powershell
# 交互式选择服务
.\debug.ps1

# 启动指定服务
.\debug.ps1 im-service
.\debug.ps1 im-tcp
.\debug.ps1 im-message-store

# 启动所有服务（会提供分别启动的建议）
.\debug.ps1 -All

# 查看帮助
.\debug.ps1 -Help
```

### 2. `debug-simple.ps1` 
**功能**: 快速启动调试服务（后台运行）
**特点**:
- ⚡ 启动速度快
- 🔇 后台运行，无日志显示
- 🎯 适合快速启动和调试

**使用方法**:
```powershell
# 启动所有服务
.\debug-simple.ps1

# 启动指定服务
.\debug-simple.ps1 im-service
.\debug-simple.ps1 im-tcp
.\debug-simple.ps1 im-message-store

# 非暂停模式启动
.\debug-simple.ps1 im-service -NoSuspend

# 查看帮助
.\debug-simple.ps1 -Help
```

### 3. `start-debug.ps1`
**功能**: 完整功能的调试启动脚本
**特点**:
- 🔧 高级功能和选项
- 📊 详细的状态输出
- 🔄 支持顺序/并行启动

**使用方法**:
```powershell
# 启动所有服务
.\start-debug.ps1

# 启动指定服务
.\start-debug.ps1 im-service im-tcp

# 顺序启动
.\start-debug.ps1 -Sequential

# 非暂停模式
.\start-debug.ps1 -NoSuspend

# 查看帮助
.\start-debug.ps1 -Help
```

### 4. `view-service-logs.ps1`
**功能**: 查看后台运行服务的状态
**使用方法**:
```powershell
# 查看服务状态
.\view-service-logs.ps1

# 查看详细信息
.\view-service-logs.ps1 -All

# 查看端口状态
.\view-service-logs.ps1 -Ports
```

## 🎯 推荐使用场景

### 场景1: 开发调试（需要看日志）
```powershell
# 使用 debug.ps1
.\debug.ps1 im-service
```
- 可以看到服务启动过程
- 实时查看日志输出
- 便于排查问题

### 场景2: 快速启动调试
```powershell
# 使用 debug-simple.ps1
.\debug-simple.ps1 im-service -NoSuspend
```
- 快速启动
- 后台运行
- 专注于代码调试

### 场景3: 多服务调试
```powershell
# 方法1: 在多个Terminal中分别启动
# Terminal 1: .\debug.ps1 im-service
# Terminal 2: .\debug.ps1 im-tcp

# 方法2: 快速启动所有服务
.\debug-simple.ps1 -NoSuspend
```

### 场景4: 状态监控
```powershell
# 启动服务
.\debug-simple.ps1 -NoSuspend

# 查看状态
.\view-service-logs.ps1 -All
```

## 🔧 VS Code任务集成

按 `Ctrl+Shift+P` → `Tasks: Run Task` → 选择：

- **Start Service with Logs (Interactive)** - 交互式选择并显示日志
- **Start Debug Service with Logs (All Services)** - 启动所有服务并显示日志
- **Start Debug Service with Logs (Business Service)** - 启动业务服务并显示日志
- **View Service Status and Logs** - 查看服务状态

## 📊 脚本对比

| 脚本 | 日志显示 | 启动速度 | 适用场景 | 推荐度 |
|------|----------|----------|----------|--------|
| `debug.ps1` | ✅ 实时显示 | 🐌 较慢 | 开发调试、问题排查 | ⭐⭐⭐⭐⭐ |
| `debug-simple.ps1` | ❌ 后台运行 | ⚡ 快速 | 快速启动、专注调试 | ⭐⭐⭐⭐ |
| `start-debug.ps1` | ❌ 后台运行 | ⚡ 快速 | 高级功能、批量管理 | ⭐⭐⭐ |

## 🎮 常用命令速查

```powershell
# 最常用 - 启动服务并查看日志
.\debug.ps1 im-service

# 快速启动
.\debug-simple.ps1 im-service -NoSuspend

# 查看运行状态
.\view-service-logs.ps1

# 停止所有服务
.\stop-im-en.ps1 -Force

# 构建项目
.\build.ps1

# 查看帮助
.\debug.ps1 -Help
```

## 🔍 等效的直接Java命令

如果您想直接使用Java命令：

```bash
# TCP服务 (端口5005)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml

# 业务服务 (端口5006)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5006 -jar im-service/target/im-service.jar

# 消息存储服务 (端口5007)
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5007 -jar im-message-store/target/im-message-store.jar
```

## 🎉 总结

现在您有了精简而强大的调试脚本集合：

1. **`debug.ps1`** - 主力脚本，支持日志查看和交互选择
2. **`debug-simple.ps1`** - 快速启动脚本
3. **`start-debug.ps1`** - 高级功能脚本
4. **`view-service-logs.ps1`** - 状态查看脚本

**推荐工作流**:
- 开发时使用 `debug.ps1` 查看日志
- 快速测试时使用 `debug-simple.ps1`
- 需要监控时使用 `view-service-logs.ps1`

这样既保持了功能的完整性，又避免了脚本冗余！🎯
