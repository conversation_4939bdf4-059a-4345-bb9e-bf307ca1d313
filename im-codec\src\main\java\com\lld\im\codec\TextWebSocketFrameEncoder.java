package com.lld.im.codec;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.MessagePack;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 文本WebSocket帧编码器
 * 将服务器的MessagePack对象编码为TextWebSocketFrame
 */
public class TextWebSocketFrameEncoder extends MessageToMessageEncoder<MessagePack> {
    
    private static final Logger logger = LoggerFactory.getLogger(TextWebSocketFrameEncoder.class);
    
    @Override
    protected void encode(ChannelHandlerContext ctx, MessagePack msg, List<Object> out) throws Exception {
        try {
            // 将MessagePack对象转换为JSON字符串
            String json = JSONObject.toJSONString(msg);
            
            // 创建文本WebSocket帧
            TextWebSocketFrame frame = new TextWebSocketFrame(json);
            
            logger.info("编码消息为TextWebSocketFrame: command={}", msg.getCommand());
            out.add(frame);
        } catch (Exception e) {
            logger.error("编码消息为TextWebSocketFrame失败: {}", e.getMessage());
        }
    }
} 