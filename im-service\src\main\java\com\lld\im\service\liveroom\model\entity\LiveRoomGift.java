package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间礼物实体类
 */
@Data
@TableName("im_live_room_gift")
public class LiveRoomGift {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 礼物ID
     */
    private String giftId;

    /**
     * 礼物名称
     */
    private String giftName;

    /**
     * 礼物图片
     */
    private String giftImg;

    /**
     * 礼物动画
     */
    private String giftAnimation;

    /**
     * 礼物价格(金币)
     */
    private Integer giftPrice;

    /**
     * 礼物类型 1-普通 2-特效 3-专属
     */
    private Integer giftType;

    /**
     * 状态 0-下架 1-上架
     */
    private Integer status;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 