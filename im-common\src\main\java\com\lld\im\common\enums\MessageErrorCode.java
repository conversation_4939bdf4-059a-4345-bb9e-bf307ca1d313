package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

public enum MessageErrorCode implements ApplicationExceptionEnum {


    FROMER_IS_MUTE(50002,"message.sender.muted"),

    FROMER_IS_FORBIBBEN(50003,"message.sender.forbidden"),


    MESSAGEBODY_IS_NOT_EXIST(50003,"message.body.not.exist"),

    MESSAGE_RECALL_TIME_OUT(50004,"message.recall.timeout"),

    MESSAGE_IS_RECALLED(50005,"message.already.recalled"),

    ;

    private int code;
    private String messageKey;

    MessageErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case FROMER_IS_MUTE:
                return "发送方被禁言";
            case FROMER_IS_FORBIBBEN:
                return "发送方被禁用";
            case MESSAGEBODY_IS_NOT_EXIST:
                return "消息体不存在";
            case MESSAGE_RECALL_TIME_OUT:
                return "消息已超过可撤回时间";
            case MESSAGE_IS_RECALLED:
                return "消息已被撤回";
            default:
                return this.messageKey;
        }
    }

}
