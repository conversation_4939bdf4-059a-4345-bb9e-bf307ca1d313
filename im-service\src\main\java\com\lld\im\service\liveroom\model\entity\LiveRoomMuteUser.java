package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间禁言用户实体类
 */
@Data
@TableName("im_live_room_mute_user")
public class LiveRoomMuteUser {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 操作者ID
     */
    private String operatorId;

    /**
     * 禁言到期时间
     */
    private Date muteEndTime;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 