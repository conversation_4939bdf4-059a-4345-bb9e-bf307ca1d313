package com.lld.im.common.enums;

public enum LiveRoomMessageType {
    
    TEXT(1, "文本消息"),
    IMAGE(2, "图片消息"),
    VOICE(3, "语音消息"),
    VIDEO(4, "视频消息"),
    EMOJI(5, "表情消息"),
    GIFT(6, "礼物消息"),
    LIKE(7, "点赞消息"),
    SYSTEM(8, "系统消息"),
    JOIN(9, "加入消息"),
    LEAVE(10, "离开消息");

    private int code;
    private String desc;

    LiveRoomMessageType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LiveRoomMessageType getByCode(int code) {
        for (LiveRoomMessageType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
} 