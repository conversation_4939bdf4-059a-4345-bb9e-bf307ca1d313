docker exec -it mysql_container mysql -u root -p


docker run --name mysql_container -v mysql-data:/var/lib/mysql -e MYSQL_ROOT_PASSWORD=root -e MYSQL_ROOT_HOST=% -d -p 3306:3306  mysql/mysql-server:5.7


CREATE DATABASE im_data;
CREATE DATABASE layim;



CREATE TABLE `im_message` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID，自增主键',
  `froms` varchar(255) DEFAULT NULL COMMENT '来源用户ID',
  `to` varchar(255) DEFAULT NULL COMMENT '目标用户ID/群ID',
  `cmd` int DEFAULT NULL COMMENT '命令码(11)int类型',
  `create_time` bigint DEFAULT NULL COMMENT '消息创建时间，long类型时间戳',
  `msg_type` int DEFAULT NULL COMMENT '消息类型(0:text、1:image、2:voice、3:video、4:music、5:news)',
  `chat_type` int DEFAULT NULL COMMENT '聊天类型(0:未知,1:公聊,2:私聊)',
  `group_id` varchar(255) DEFAULT NULL COMMENT '群组ID，仅在chatType为1时需要',
  `content` text COMMENT '消息内容',
  `extras` text COMMENT '扩展字段，JSON格式',
  `status` int DEFAULT '0' COMMENT '消息状态(0:未读,1:已读,2:撤回)',
  `is_del` tinyint(1) DEFAULT '0' COMMENT '是否删除(0:否,1:是)',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='即时通讯消息表';





docker pull redis

docker run --name myredis -it -p 6379:6379 -v /data/redis-data  redis --requirepass "123456"





docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 -e RABBITMQ_DEFAULT_USER=guest -e RABBITMQ_DEFAULT_PASS=guest rabbitmq:3-management

应用程序连接：
主机：localhost
端口：5672
用户名：guest
密码：guest
管理界面：
访问地址：http://localhost:15672
用户名：guest
密码：guest
这些配置与您的application.yml文件中的配置是匹配的，所以您的应用程序应该可以直接连接到这个RabbitMQ实例。
如果您需要停止或删除RabbitMQ容器，可以使用以下命令：
停止容器：docker stop rabbitmq
删除容器：docker rm rabbitmq



docker run -d --name zookeeper -p 2181:2181 -p 2888:2888 -p 3888:3888 zookeeper:3.8

Zookeeper已经成功运行起来了！让我解释一下这个命令的各个部分：
-d: 在后台运行容器
--name zookeeper: 给容器命名为zookeeper
-p 2181:2181: 映射Zookeeper的客户端连接端口
-p 2888:2888: 映射Zookeeper的follower连接leader的端口
-p 3888:3888: 映射Zookeeper的选举端口
这个配置与您的config.yml文件中的配置是匹配的：
主机：127.0.0.1
端口：2181
连接超时：5000ms



cd im-tcp; java -jar target/tcp-1.0.0-SNAPSHOT-jar-with-dependencies.jar src/main/resources/config.yml


cd D:\workSpace\sst-jim
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5005 -jar im-tcp/target/tcp-1.0.0-SNAPSHOT-jar-with-dependencies.jar im-tcp/src/main/resources/config.yml


java -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5005 -jar im-tcp/target/im-tcp.jar im-tcp/src/main/resources/config-docker-cluster.yml



cd D:\workSpace\sst-jim
java -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5006 -jar im-service/target/im-service.jar --spring.profiles.active=dev




java -agentlib:jdwp=transport=dt_socket,server=y,suspend=y,address=5007 -jar im-message-store/target/im-message-store.jar --spring.profiles.active=prod




  java -jar im-service.jar --spring.profiles.active=prod
  
  java -jar im-tcp/target/tcp-1.0.0-SNAPSHOT-jar-with-dependencies.jar im-tcp/src/main/resources/config-docker-cluster.yml
  
 java -jar im-message-store/target/im-message-store-1.0.0-SNAPSHOT.jar --spring.profiles.active=prod


 java -jar im-service/target/im-service-1.0.0-SNAPSHOT.jar --spring.profiles.active=prod



cd im-message-store; mvn spring-boot:run