package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 创建直播间请求
 */
@ApiModel(description = "创建直播间请求模型")
@Data
public class CreateLiveRoomReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = false, example = "room123", notes = "直播间唯一标识，如果不提供则自动生成")
    private String roomId;

    @ApiModelProperty(value = "直播间名称", required = true, example = "精彩直播间")
    @NotBlank(message = "{validation.room.name.not.blank}")
    private String roomName;

    @ApiModelProperty(value = "直播间封面图片URL", example = "https://example.com/cover.jpg")
    private String roomCover;

    @ApiModelProperty(value = "主播用户ID", required = true, example = "anchor123")
    @NotBlank(message = "{validation.anchor.id.not.blank}")
    private String anchorId;

    @ApiModelProperty(value = "直播间公告", example = "欢迎来到我的直播间")
    private String announcement;

    @ApiModelProperty(value = "是否开启全员禁言 (0:否 1:是)", example = "0", notes = "控制直播间是否开启全员禁言模式")
    @Pattern(regexp = "^[01]$", message = "{validation.mute.all.pattern}")
    private String muteAll = "0";

    @ApiModelProperty(value = "是否需要审核 (0:否 1:是)", example = "0", notes = "控制用户发言是否需要审核")
    @Pattern(regexp = "^[01]$", message = "{validation.need.review.pattern}")
    private String needReview = "0";

    @ApiModelProperty(value = "最大在线人数", example = "1000")
    private Integer maxOnlineCount = 1000;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000")
    @NotNull(message = "{validation.app.id.not.null}")
    private Integer appId;
}
