package com.lld.im.codec;


import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.MessagePack;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToMessageEncoder;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @author: Chackylee
 * @description:
 **/
public class WebSocketMessageEncoder extends MessageToMessageEncoder<MessagePack> {

    private static Logger log = LoggerFactory.getLogger(WebSocketMessageEncoder.class);

    @Override
    protected void encode(ChannelHandlerContext ctx, MessagePack msg, List<Object> out)  {

        try {
            String s = JSONObject.toJSONString(msg);
            // 明确指定UTF-8编码
            byte[] bytes = s.getBytes("UTF-8");
            ByteBuf byteBuf = Unpooled.directBuffer(8 + bytes.length);
            byteBuf.writeInt(msg.getCommand());
            byteBuf.writeInt(bytes.length);
            byteBuf.writeBytes(bytes);
            out.add(new BinaryWebSocketFrame(byteBuf));

            log.debug("📦 WebSocket消息编码成功: command={}, bodyLength={}, content={}",
                     msg.getCommand(), bytes.length, s);
        }catch (Exception e){
            log.error("❌ WebSocket消息编码失败: command={}", msg.getCommand(), e);
            e.printStackTrace();
        }

    }
}