# IM-Service 即时通讯服务

## 1. 项目概述

IM-Service是一个基于Spring Boot开发的即时通讯服务后端，提供完整的即时通讯核心功能实现。本服务是即时通讯系统的核心业务处理组件，负责处理用户管理、好友关系、群组管理、消息收发等业务逻辑，为客户端提供丰富的API接口。

### 主要功能
- 用户账号管理
- 好友关系管理
- 群组及群成员管理
- 消息发送与接收
- 会话管理
- 离线消息同步
- 多端消息同步
- 系统通知推送

## 2. 技术架构

### 开发环境
- Java 1.8
- Spring Boot
- MyBatis Plus
- MySQL (数据持久化)
- Redis (缓存、消息存储)
- RabbitMQ (消息队列)

### 系统架构
IM-Service是即时通讯系统中的业务处理服务，与其他组件协同工作：

- **im-service**: 核心业务服务，处理各类业务逻辑，提供RESTful API
- **im-tcp**: TCP长连接服务器，负责客户端连接管理和消息实时推送
- **im-codec**: 消息编解码模块，处理消息的序列化与反序列化
- **im-common**: 公共模块，包含常量、枚举、工具类等共享内容

## 3. API文档

### Swagger在线文档

本项目集成了Swagger API文档工具，提供在线API文档和接口测试功能。

#### 访问方式

1. **启动服务**：确保im-service服务正常启动（默认端口：8000）
2. **访问Swagger UI**：
   - 浏览器访问：`http://localhost:8000/swagger-ui.html`
   - 或访问：`http://localhost:8000/swagger-ui/`

#### 功能特性

- **完整的API文档**：包含所有Controller的接口文档
- **在线测试**：可直接在页面上测试API接口
- **参数说明**：详细的请求参数和响应格式说明
- **安全配置**：支持appId和identifier请求头配置

#### 主要API模块

- **用户管理**：用户导入、删除、登录、状态管理等
- **好友关系管理**：好友增删改查、黑名单管理等
- **群组管理**：群组创建、管理、消息发送等
- **群成员管理**：群成员的添加、删除、更新和权限管理等
- **消息管理**：点对点消息发送和同步等
- **会话管理**：会话的创建、更新、删除等
- **系统通知管理**：系统通知的发送、查询和管理等
- **直播间管理**：直播间创建、加入、消息发送和管理等

#### 使用说明

1. **环境要求**：Swagger仅在开发(dev)和测试(test)环境中启用
2. **认证配置**：大部分接口需要在请求头中设置appId和identifier
3. **接口测试**：可在Swagger UI中直接填写参数进行接口测试
4. **参数示例**：所有接口参数都提供了详细的示例值和说明
5. **响应格式**：统一的ResponseVO响应格式，包含状态码、消息和数据

#### HTTP方法映射规范

本项目已完成所有Controller的HTTP方法映射优化：

- **查询操作**：使用 `@GetMapping` 或 `@PostMapping`（复杂查询条件）
- **创建操作**：使用 `@PostMapping`
- **更新操作**：使用 `@PutMapping`
- **删除操作**：使用 `@DeleteMapping`

#### 已优化的Controller列表

1. **SystemNotificationController** - 系统通知管理
2. **LiveRoomController** - 直播间管理
3. **ConversationController** - 会话管理
4. **ImGroupController** - 群组管理
5. **ImGroupMemberController** - 群成员管理
6. **ImUserController** - 用户管理
7. **ImUserDataController** - 用户数据管理
8. **ImFriendShipController** - 好友关系管理
9. **ImFriendShipRequestController** - 好友请求管理
10. **ImFriendShipGroupController** - 好友分组管理
11. **MessageController** - 消息管理

#### 已完善的请求模型

所有主要的请求模型类都已添加完整的Swagger注解：

- **SystemNotification** - 系统通知模型
- **CreateLiveRoomReq** - 创建直播间请求
- **SendLiveRoomMsgReq** - 发送直播间消息请求
- **ImportUserReq** - 导入用户请求
- **LoginReq** - 用户登录请求
- **SendMessageReq** - 发送消息请求
- **AddFriendReq** - 添加好友请求
- **CheckFriendShipReq** - 检查好友关系请求
- **CreateGroupReq** - 创建群组请求

## 4. 数据模型

IM-Service主要涉及以下核心数据模型：

- **用户(User)**: 系统用户信息
- **好友关系(Friendship)**: 用户之间的好友关系
- **好友分组(FriendshipGroup)**: 好友的分组管理
- **好友请求(FriendshipRequest)**: 好友申请记录
- **群组(Group)**: 群信息
- **群成员(GroupMember)**: 群成员信息
- **消息体(MessageBody)**: 消息内容
- **会话(Conversation)**: 聊天会话

## 4. 核心模块介绍

### 4.1 用户模块
用户模块负责用户账号管理，包含用户注册、登录、资料管理等功能。

#### 主要API：
- `/v1/user/importUser`: 导入用户
- `/v1/user/deleteUser`: 删除用户
- `/v1/user/login`: 用户登录，返回IM连接信息
- `/v1/user/getUserSequence`: 获取用户序列号
- `/v1/user/subscribeUserOnlineStatus`: 订阅用户在线状态
- `/v1/user/queryUserOnlineStatus`: 查询用户在线状态

### 4.2 好友关系模块
负责用户间好友关系管理，包括添加好友、删除好友、好友分组等功能。

#### 主要API：
- `/v1/friendship/addFriend`: 添加好友
- `/v1/friendship/updateFriend`: 更新好友信息
- `/v1/friendship/deleteFriend`: 删除好友
- `/v1/friendship/getAllFriendShip`: 获取所有好友
- `/v1/friendship/checkFriend`: 检查好友关系
- `/v1/friendship/syncFriendshipList`: 同步好友列表

#### 好友请求相关API：
- `/v1/friendshipRequest/approveFriendRequest`: 审批好友请求
- `/v1/friendshipRequest/getFriendRequest`: 获取好友请求

### 4.3 群组模块
群组模块负责群组的创建、管理和群成员管理等功能。

#### 主要API：
- `/v1/group/createGroup`: 创建群组
- `/v1/group/getGroupInfo`: 获取群组信息
- `/v1/group/update`: 更新群组信息
- `/v1/group/getJoinedGroup`: 获取已加入的群组
- `/v1/group/destroyGroup`: 解散群组
- `/v1/group/transferGroup`: 转让群组
- `/v1/group/forbidSendMessage`: 群组禁言
- `/v1/group/sendMessage`: 发送群组消息

#### 群成员相关API：
- `/v1/group/member/add`: 添加群成员
- `/v1/group/member/remove`: 移除群成员
- `/v1/group/member/update`: 更新群成员信息

### 4.4 消息模块
消息模块负责消息的发送、接收、存储和同步等功能。

#### 主要API：
- `/v1/message/send`: 发送单聊消息
- `/v1/message/checkSend`: 检查消息发送权限
- `/v1/message/syncOfflineMessage`: 同步离线消息

### 4.5 会话模块
会话模块负责会话的管理，包括会话创建、更新、删除和同步等功能。

#### 主要API：
- `/v1/conversation/deleteConversation`: 删除会话
- `/v1/conversation/updateConversation`: 更新会话状态（置顶、免打扰等）
- `/v1/conversation/syncConversationList`: 同步会话列表

### 4.6 系统通知模块
系统通知模块负责系统级通知的发送、查询和管理等功能。

#### 主要API：
- `/v1/system/notification/send`: 发送系统通知
- `/v1/system/notification/unread`: 获取用户未读通知列表
- `/v1/system/notification/read`: 标记通知已读
- `/v1/system/notification/unread/count`: 获取未读通知数量

### 4.7 直播间模块
直播间模块负责直播间的创建、管理和实时聊天等功能。

#### 主要API：
- `/api/liveroom/create`: 创建直播间
- `/api/liveroom/join`: 加入直播间
- `/api/liveroom/leave`: 离开直播间
- `/api/liveroom/message/send`: 发送直播间消息
- `/api/liveroom/info`: 获取直播间信息
- `/api/liveroom/message/recent`: 获取直播间最近消息
- `/api/liveroom/mute/user`: 禁言/解禁用户
- `/api/liveroom/mute/all`: 全员禁言/解禁

## 5. 消息同步机制

IM-Service采用基于序列号的增量同步机制，确保消息和各类数据在多端之间高效同步：

1. **消息序列号**: 每个消息都有唯一的递增序列号，确保消息顺序
2. **增量同步**: 客户端根据本地最大序列号只拉取新消息
3. **离线消息存储**: 使用Redis的ZSet结构存储离线消息，按序列号排序
4. **多端同步**: 支持消息在用户的多个终端之间实时同步

## 6. 系统通知推送

系统支持多种类型的通知推送：

1. **好友通知**: 好友申请、申请通过/拒绝等
2. **群组通知**: 入群邀请、新成员加入、成员退出、群信息变更等
3. **消息通知**: 新消息提醒、消息已读回执、消息撤回等

通知推送通过MessageProducer发送到RabbitMQ，再由im-tcp服务推送给客户端。

## 7. 安全与权限控制

1. **好友验证机制**: 可配置是否需要验证才能添加好友
2. **黑名单机制**: 支持将用户加入黑名单，阻止消息接收
3. **群组权限**: 基于角色的群组权限控制（群主、管理员、普通成员）
4. **发言权限**: 支持群禁言等功能

## 8. 部署与配置

### 环境要求
- JDK 1.8+
- MySQL 5.7+
- Redis 5.0+
- RabbitMQ 3.8+

### 配置文件
核心配置文件位于`src/main/resources/application.yml`，包含数据库、Redis、RabbitMQ等配置。

### 构建与运行
```bash
# 编译打包
mvn clean package

# 运行服务
java -jar target/im-service.jar
```

## 9. API调用示例

### 用户登录
```http
POST /v1/user/login
Content-Type: application/json

{
  "userId": "test_user_1",
  "clientType": 1,
  "appId": 10000
}
```

### 发送消息
```http
POST /v1/message/send
Content-Type: application/json

{
  "fromId": "test_user_1",
  "toId": "test_user_2",
  "messageBody": "Hello, this is a test message",
  "appId": 10000
}
```

### 同步离线消息
```http
POST /v1/message/syncOfflineMessage
Content-Type: application/json

{
  "operater": "test_user_1",
  "lastSequence": 100,
  "maxLimit": 50,
  "appId": 10000
}
```

## 10. 扩展与定制

IM-Service设计了灵活的扩展点：

1. **回调机制**: 支持在关键业务节点进行回调，便于与其他系统集成
2. **消息类型扩展**: 可自定义消息类型，支持文本、图片、语音等多种消息
3. **水平扩展**: 支持集群部署，提高系统容量和可用性

## 11. 常见问题排查

1. **消息丢失问题**: 检查Redis离线消息存储、RabbitMQ连接状态
2. **多端同步问题**: 确认序列号机制正常工作，检查客户端同步逻辑
3. **性能问题**: 优化数据库查询、Redis缓存使用、消息推送策略


