package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @author: Chackylee
 * @description: 删除好友分组成员请求
 **/
@ApiModel(description = "删除好友分组成员请求模型")
@Data
public class DeleteFriendShipGroupMemberReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "发起删除分组成员操作的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "分组名称", required = true, example = "同事", notes = "要删除成员的好友分组名称")
    @NotBlank(message = "分组名称不能为空")
    private String groupName;

    @ApiModelProperty(value = "成员用户ID列表", required = true, example = "[\"user456\", \"user789\"]", notes = "要从分组中删除的好友用户ID列表")
    @NotEmpty(message = "请选择用户")
    private List<String> toIds;

}
