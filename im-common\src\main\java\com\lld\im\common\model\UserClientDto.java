package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户客户端信息DTO
 * @description: 用户客户端信息数据传输对象
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户客户端信息数据传输对象")
@Data
public class UserClientDto {

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "客户端类型 (0:webApi 1:web 2:ios 3:android 4:windows 5:mac)", required = true, example = "1", notes = "客户端设备类型标识")
    private Integer clientType;

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "用户的唯一标识")
    private String userId;

    @ApiModelProperty(value = "设备标识", required = true, example = "device123", notes = "设备的唯一标识符")
    private String imei;

}
