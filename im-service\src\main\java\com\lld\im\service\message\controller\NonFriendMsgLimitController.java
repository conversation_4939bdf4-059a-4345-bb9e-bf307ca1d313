package com.lld.im.service.message.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.message.service.NonFriendMsgLimitService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 非好友私信限制管理控制器
 * 
 * <AUTHOR>
 */
@Api(tags = "非好友私信限制管理", description = "非好友私信限制功能的管理接口")
@RestController
@RequestMapping("v1/message/non-friend-limit")
public class NonFriendMsgLimitController extends BaseController {

    @Autowired
    private NonFriendMsgLimitService nonFriendMsgLimitService;

    @ApiOperation(value = "获取非好友私信限制状态", notes = "查询指定用户对之间的非好友私信限制状态")
    @GetMapping("/status")
    public ResponseVO<Map<String, Object>> getNonFriendMsgLimitStatus(
            @ApiParam(value = "发送方用户ID", required = true) @RequestParam String fromId,
            @ApiParam(value = "接收方用户ID", required = true) @RequestParam String toId) {
        Integer appId = getCurrentAppId();
        Map<String, Object> status = nonFriendMsgLimitService.getNonFriendMsgLimitStatus(fromId, toId, appId);
        return ResponseVO.successResponse(status);
    }

    @ApiOperation(value = "清除非好友私信限制", notes = "清除指定用户对之间的非好友私信限制记录")
    @DeleteMapping("/clear")
    public ResponseVO clearNonFriendMsgLimit(
            @ApiParam(value = "发送方用户ID", required = true) @RequestParam String fromId,
            @ApiParam(value = "接收方用户ID", required = true) @RequestParam String toId) {
        Integer appId = getCurrentAppId();
        nonFriendMsgLimitService.clearNonFriendMsgLimit(fromId, toId, appId);
        return ResponseVO.successResponse();
    }

    @ApiOperation(value = "检查非好友私信限制", notes = "检查指定用户对是否受到非好友私信限制")
    @GetMapping("/check")
    public ResponseVO checkNonFriendMsgLimit(
            @ApiParam(value = "发送方用户ID", required = true) @RequestParam String fromId,
            @ApiParam(value = "接收方用户ID", required = true) @RequestParam String toId) {
        Integer appId = getCurrentAppId();
        return nonFriendMsgLimitService.checkNonFriendMsgLimit(fromId, toId, appId);
    }

    @ApiOperation(value = "模拟对方回复", notes = "模拟对方回复消息，重置发送方的消息限制（仅用于测试）")
    @PostMapping("/simulateReply")
    public ResponseVO simulateReply(
            @ApiParam(value = "回复方用户ID", required = true) @RequestParam String replyFromId,
            @ApiParam(value = "接收回复的用户ID", required = true) @RequestParam String replyToId) {
        Integer appId = getCurrentAppId();
        nonFriendMsgLimitService.recordNonFriendReply(replyFromId, replyToId, appId);
        return ResponseVO.successResponse();
    }

    @ApiOperation(value = "清除双向非好友私信限制", notes = "清除指定用户对之间的双向非好友私信限制记录")
    @DeleteMapping("/clear-both")
    public ResponseVO clearBothDirections(
            @ApiParam(value = "用户ID1", required = true) @RequestParam String userId1,
            @ApiParam(value = "用户ID2", required = true) @RequestParam String userId2) {
        Integer appId = getCurrentAppId();
        // 清除双向限制记录
        nonFriendMsgLimitService.clearNonFriendMsgLimit(userId1, userId2, appId);
        nonFriendMsgLimitService.clearNonFriendMsgLimit(userId2, userId1, appId);
        return ResponseVO.successResponse("已清除双向限制记录");
    }
}
