package com.lld.im.service.group.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.model.resp.BatchSpeakMemberResp;
import com.lld.im.service.group.model.resp.BatchRemoveGroupMemberResp;
import com.lld.im.service.group.model.resp.GroupApplyListResp;
import com.lld.im.service.group.model.resp.UserApplyListResp;
import com.lld.im.service.group.service.ImGroupMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 群成员管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "群成员管理", description = "群成员的添加、删除、更新和权限管理相关接口")
@RestController
@RequestMapping("v1/group/member")
public class ImGroupMemberController extends BaseController {

    @Autowired
    ImGroupMemberService groupMemberService;

    @ApiOperation(value = "导入群成员", notes = "批量导入群成员信息到指定群组")
    @PostMapping("/importGroupMember")
    public ResponseVO importGroupMember(
            @ApiParam(value = "导入群成员请求参数", required = true) @RequestBody @Validated ImportGroupMemberReq req)  {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return groupMemberService.importGroupMember(req);
    }

    @ApiOperation(value = "添加群成员", notes = "向群组中添加新的成员")
    @PostMapping("/add")
    public ResponseVO addMember(
            @ApiParam(value = "添加群成员请求参数", required = true) @RequestBody @Validated AddGroupMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.addMember(req);
    }

    @ApiOperation(value = "移除群成员", notes = "从群组中移除指定的成员")
    @DeleteMapping("/remove")
    public ResponseVO removeMember(
            @ApiParam(value = "移除群成员请求参数", required = true) @RequestBody @Validated RemoveGroupMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.removeMember(req);
    }

    @ApiOperation(value = "批量移除群成员", notes = "批量从群组中移除指定的成员")
    @DeleteMapping("/batchRemove")
    public ResponseVO<BatchRemoveGroupMemberResp> batchRemoveMember(
            @ApiParam(value = "批量移除群成员请求参数", required = true) @RequestBody @Validated BatchRemoveGroupMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.batchRemoveMember(req);
    }

    @ApiOperation(value = "更新群成员信息", notes = "更新群成员的角色、昵称等信息")
    @PutMapping("/update")
    public ResponseVO updateGroupMember(
            @ApiParam(value = "更新群成员请求参数", required = true) @RequestBody @Validated UpdateGroupMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.updateGroupMember(req);
    }

    @ApiOperation(value = "群成员禁言/解禁", notes = "对群成员进行禁言或解除禁言操作")
    @PutMapping("/speak")
    public ResponseVO speak(
            @ApiParam(value = "群成员禁言请求参数", required = true) @RequestBody @Validated SpeaMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.speak(req);
    }

    @ApiOperation(value = "批量群成员禁言/解禁", notes = "批量对群成员进行禁言或解除禁言操作")
    @PutMapping("/batchSpeak")
    public ResponseVO<BatchSpeakMemberResp> batchSpeak(
            @ApiParam(value = "批量群成员禁言请求参数", required = true) @RequestBody @Validated BatchSpeakMemberReq req)  {
        fillCommonParams(req);
        return groupMemberService.batchSpeak(req);
    }

    @ApiOperation(value = "申请加群", notes = "用户主动申请加入群组，需要群主或管理员审批。" +
            "适用于applyJoinType=1的群组，申请后需要等待群主或管理员审批。" +
            "系统会检查群成员数量限制、申请频率限制等业务规则。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "申请提交成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 40018, message = "群组已解散"),
            @ApiResponse(code = 40019, message = "该群禁止申请加入"),
            @ApiResponse(code = 40003, message = "该用户已经进入该群"),
            @ApiResponse(code = 40005, message = "群成员已达到上限"),
            @ApiResponse(code = 40020, message = "已存在待审批的申请"),
            @ApiResponse(code = 40021, message = "申请加群失败（频率限制等）")
    })
    @PostMapping("/apply")
    public ResponseVO applyJoinGroup(
            @ApiParam(value = "申请加群请求参数", required = true) @RequestBody @Validated ApplyJoinGroupReq req)  {
        fillCommonParams(req);
        return groupMemberService.applyJoinGroup(req);
    }

    @ApiOperation(value = "审批群申请", notes = "群主或管理员审批用户的加群申请。" +
            "只有群主和管理员有权限审批申请。审批结果会通过WebSocket实时通知申请人。" +
            "同意申请时会自动将用户添加到群组中，拒绝时可以填写拒绝理由。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "审批成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 40022, message = "申请记录不存在"),
            @ApiResponse(code = 40023, message = "申请状态错误"),
            @ApiResponse(code = 40007, message = "该操作只允许群主/管理员操作"),
            @ApiResponse(code = 40005, message = "群成员已达到上限"),
            @ApiResponse(code = 40024, message = "审批申请失败"),
            @ApiResponse(code = 90001, message = "参数校验错误")
    })
    @PostMapping("/approve")
    public ResponseVO approveGroupApply(
            @ApiParam(value = "审批群申请请求参数", required = true) @RequestBody @Validated ApproveGroupApplyReq req)  {
        fillCommonParams(req);
        return groupMemberService.approveGroupApply(req);
    }

    @ApiOperation(value = "自由加入群组", notes = "无需审批直接加入公开群组。" +
            "仅适用于applyJoinType=2的群组，用户可以直接加入无需等待审批。" +
            "系统会检查群成员数量限制等业务规则。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "加入成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 40018, message = "群组已解散"),
            @ApiResponse(code = 40019, message = "该群禁止申请加入"),
            @ApiResponse(code = 40003, message = "该用户已经进入该群"),
            @ApiResponse(code = 40005, message = "群成员已达到上限")
    })
    @PostMapping("/freeJoin")
    public ResponseVO freeJoinGroup(
            @ApiParam(value = "自由加入群组请求参数", required = true) @RequestBody @Validated FreeJoinGroupReq req)  {
        fillCommonParams(req);
        return groupMemberService.freeJoinGroup(req);
    }

    @ApiOperation(value = "查询群申请列表", notes = "查询指定群组的申请记录列表。" +
            "只有群主和管理员有权限查询申请列表。支持按申请状态过滤和分页查询。" +
            "返回申请人信息、申请时间、申请理由、审批状态等详细信息。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 40007, message = "该操作只允许群主/管理员操作")
    })
    @PostMapping("/applyList")
    public ResponseVO<GroupApplyListResp> getGroupApplyList(
            @ApiParam(value = "查询群申请列表请求参数", required = true) @RequestBody @Validated GetGroupApplyListReq req)  {
        fillCommonParams(req);
        return groupMemberService.getGroupApplyList(req);
    }

    @ApiOperation(value = "查询用户最近申请记录", notes = "查询当前用户在指定群组的最近申请记录。" +
            "用户只能查询自己的申请记录，返回最近一次申请的详细信息。" +
            "包含群组信息、申请状态、审批信息等详细内容。" +
            "权限控制：用户只能查询自己的申请记录，确保数据安全。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 90001, message = "参数校验错误")
    })
    @GetMapping("/applications")
    public ResponseVO<UserApplyListResp> getUserApplyList(
            @ApiParam(value = "要查询申请记录的群组唯一标识", required = true)
            @RequestParam String groupId) {

        // 创建请求对象
        GetUserApplyListReq req = new GetUserApplyListReq();
        req.setGroupId(groupId);
        fillCommonParams(req);

        return groupMemberService.getUserApplyList(req);
    }

    @ApiOperation(value = "退出群组", notes = "用户主动退出群组。" +
            "群成员可以主动退出群组，但群主需要先转让群主身份才能退出。" +
            "退出后用户将无法接收群消息，也无法查看群信息。" +
            "系统会向群内其他成员发送退出通知。")
    @ApiResponses({
            @ApiResponse(code = 200, message = "退出成功"),
            @ApiResponse(code = 40000, message = "群不存在"),
            @ApiResponse(code = 40018, message = "群组已解散"),
            @ApiResponse(code = 40003, message = "用户不在群内"),
            @ApiResponse(code = 40008, message = "群主不能直接退出，需要先转让群主身份"),
            @ApiResponse(code = 40009, message = "更新群成员信息失败")
    })
    @PostMapping("/exit")
    public ResponseVO<Object> exitGroup(
            @ApiParam(value = "退出群组请求参数", required = true) @RequestBody @Validated ExitGroupReq req)  {
        fillCommonParams(req);
        return groupMemberService.exitGroup(req);
    }

}
