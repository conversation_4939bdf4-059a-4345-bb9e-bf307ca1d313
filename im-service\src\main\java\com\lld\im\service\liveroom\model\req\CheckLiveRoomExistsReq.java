package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量检查直播间是否存在请求模型
 * 
 * @description: 批量检查多个直播间是否存在的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "批量检查直播间是否存在请求模型")
@Data
public class CheckLiveRoomExistsReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID列表", required = true,
                     example = "[\"room123\", \"room456\", \"room789\"]",
                     notes = "要检查是否存在的直播间ID列表，最多支持100个")
    @NotEmpty(message = "{validation.room.ids.not.empty}")
    @Size(max = 100, message = "{validation.room.ids.size}")
    private List<String> roomIds;

}
