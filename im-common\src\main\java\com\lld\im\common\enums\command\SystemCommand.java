package com.lld.im.common.enums.command;

/**
 * 系统通知相关命令
 */
public enum SystemCommand implements Command {

    //心跳 9999
    PING(0x270f),

    /**
     * 登录 9000
     */
    LOGIN(0x2328),

    //登录ack  9001
    LOGINACK(0x2329),

    //登出  9003
    LOGOUT(0x232b),

    //下线通知 用于多端互斥  9002
    MUTUALLOGIN(0x232a),
    
    /**
     * 系统通知推送
     */
    SYSTEM_NOTIFICATION(9004),
    
    /**
     * 系统通知已读回执
     */
    SYSTEM_NOTIFICATION_READ_RECEIPT(9005);

    private int command;

    SystemCommand(int command){
        this.command=command;
    }


    @Override
    public int getCommand() {
        return command;
    }
}
