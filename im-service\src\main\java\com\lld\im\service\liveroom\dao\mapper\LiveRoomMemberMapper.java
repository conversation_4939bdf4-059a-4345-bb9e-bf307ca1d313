package com.lld.im.service.liveroom.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.liveroom.model.entity.LiveRoomMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

/**
 * 直播间成员Mapper接口
 */
@Mapper
public interface LiveRoomMemberMapper extends BaseMapper<LiveRoomMember> {

    /**
     * 更新成员最后活跃时间
     *
     * @param roomId 直播间ID
     * @param userId 用户ID
     * @param appId  应用ID
     * @return 影响行数
     */
    @Update("UPDATE im_live_room_member SET last_active_time = NOW() WHERE room_id = #{roomId} AND user_id = #{userId} AND app_id = #{appId}")
    int updateLastActiveTime(@Param("roomId") String roomId, @Param("userId") String userId, @Param("appId") Integer appId);

    /**
     * 统计直播间在线人数
     *
     * @param roomId 直播间ID
     * @param appId  应用ID
     * @return 在线人数
     */
    @Select("SELECT COUNT(*) FROM im_live_room_member WHERE room_id = #{roomId} AND app_id = #{appId} AND last_active_time > DATE_SUB(NOW(), INTERVAL 5 MINUTE)")
    int countOnlineMembers(@Param("roomId") String roomId, @Param("appId") Integer appId);
} 