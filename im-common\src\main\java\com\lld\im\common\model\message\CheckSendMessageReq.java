package com.lld.im.common.model.message;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 检查发送消息请求
 * @description: 检查消息发送权限和条件的请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "检查发送消息请求模型")
@Data
public class CheckSendMessageReq {

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者用户ID", required = true, example = "user456", notes = "消息接收者的用户ID")
    private String toId;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000", notes = "应用的唯一标识")
    private Integer appId;

    @ApiModelProperty(value = "命令类型", required = true, example = "1001", notes = "消息命令类型标识")
    private Integer command;

}
