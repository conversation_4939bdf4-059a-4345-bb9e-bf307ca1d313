package com.lld.im.service.message.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.message.CheckSendMessageReq;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.message.model.req.GetConversationMessagesReq;
import com.lld.im.service.message.model.req.SearchMessageReq;
import com.lld.im.service.message.model.req.SendMessageReq;
import com.lld.im.service.message.model.resp.MessageHistoryResp;
import com.lld.im.service.message.model.resp.PageResult;
import com.lld.im.service.message.model.resp.SearchMessageResp;
import com.lld.im.service.message.service.MessageHistoryService;
import com.lld.im.service.message.service.MessageSyncService;
import com.lld.im.service.message.service.P2PMessageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 消息管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "消息管理", description = "点对点消息发送和同步相关接口")
@RestController
@RequestMapping("v1/message")
public class MessageController extends BaseController {

    @Autowired
    P2PMessageService p2PMessageService;

    @Autowired
    MessageSyncService messageSyncService;

    @Autowired
    MessageHistoryService messageHistoryService;

    @ApiOperation(value = "发送消息", notes = "发送点对点消息")
    @PostMapping("/send")
    public ResponseVO send(
            @ApiParam(value = "发送消息请求参数", required = true) @RequestBody @Validated SendMessageReq req)  {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return ResponseVO.successResponse(p2PMessageService.send(req));
    }

    @ApiOperation(value = "检查发送权限", notes = "检查用户是否有权限发送消息给目标用户")
    @PostMapping("/checkSend")
    public ResponseVO checkSend(
            @ApiParam(value = "检查发送权限请求参数", required = true) @RequestBody @Validated CheckSendMessageReq req)  {
        return p2PMessageService.imServerPermissionCheck(req.getFromId(),req.getToId()
                ,req.getAppId());
    }

    @ApiOperation(value = "同步离线消息", notes = "增量同步用户的离线消息数据")
    @PostMapping("/syncOfflineMessage")
    public ResponseVO syncOfflineMessage(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req)  {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return messageSyncService.syncOfflineMessage(req);
    }

    @ApiOperation(value = "查询会话历史消息", notes = "根据会话ID分页查询历史消息记录")
    @PostMapping("/getConversationMessages")
    public ResponseVO<PageResult<MessageHistoryResp>> getConversationMessages(
            @ApiParam(value = "查询会话历史消息请求参数", required = true) @RequestBody @Validated GetConversationMessagesReq req) {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return messageHistoryService.getConversationMessages(req);
    }

    @ApiOperation(value = "搜索消息内容", notes = "根据关键词模糊搜索会话聊天内容")
    @PostMapping("/searchMessages")
    public ResponseVO<List<SearchMessageResp>> searchMessages(
            @ApiParam(value = "搜索消息请求参数", required = true) @RequestBody @Validated SearchMessageReq req) {
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return messageHistoryService.searchMessages(req);
    }

}
