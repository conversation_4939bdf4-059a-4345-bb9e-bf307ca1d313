package com.lld.message.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;

import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.stereotype.Component;
import org.redisson.spring.data.connection.RedissonConnectionFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;

/**
 * Redis连接监听器
 * 用于监控和记录Redis集群连接状态
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisConnectionMonitor {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Value("${spring.redis.cluster.nodes:}")
    private String clusterNodes;

    @Value("${spring.redis.host:}")
    private String redisHost;

    @Value("${spring.redis.port:6379}")
    private int redisPort;

    @Value("${spring.redis.password:}")
    private String redisPassword;

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    /**
     * 应用启动完成后检查Redis连接状态
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        logRedisConnectionInfo();
    }

    /**
     * 记录Redis连接信息
     */
    private void logRedisConnectionInfo() {
        Instant startTime = Instant.now();
        
        try {
            log.info("=== IM-MESSAGE-STORE Redis 连接信息检查开始 ===");
            log.info("当前环境: {}", activeProfile);
            
            // 检查连接工厂类型和配置
            if (redisConnectionFactory instanceof RedissonConnectionFactory) {
                RedissonConnectionFactory redissonFactory = (RedissonConnectionFactory) redisConnectionFactory;
                logRedissonConnectionInfo(redissonFactory);
            } else if (redisConnectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;
                logLettuceConnectionInfo(lettuceFactory);
            } else {
                log.info("Redis连接工厂类型: {}", redisConnectionFactory.getClass().getSimpleName());
            }

            // 测试连接
            testRedisConnection(startTime);
            
        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("Redis连接检查失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage(), e);
        }
    }

    /**
     * 记录Redisson连接信息
     */
    private void logRedissonConnectionInfo(RedissonConnectionFactory factory) {
        log.info("Redis连接客户端: Redisson");

        // 动态检测Redis模式
        boolean isClusterMode = clusterNodes != null && !clusterNodes.trim().isEmpty();

        if (isClusterMode) {
            log.info("Redis模式: 集群模式");
            String[] nodes = clusterNodes.split(",");
            log.info("集群节点: {}", clusterNodes);
            log.info("解析的集群节点数量: {}", nodes.length);
            log.info("集群拓扑扫描: 已禁用");
        } else {
            log.info("Redis模式: 单机模式");
            log.info("服务器地址: {}:{}", redisHost, redisPort);
        }

        log.info("密码配置: {}", redisPassword != null && !redisPassword.isEmpty() ? "已配置" : "未配置");
    }

    /**
     * 记录Lettuce连接信息
     */
    private void logLettuceConnectionInfo(LettuceConnectionFactory factory) {
        log.info("Redis连接客户端: Lettuce");

        if (factory.getClusterConfiguration() != null) {
            // 集群模式
            RedisClusterConfiguration clusterConfig = factory.getClusterConfiguration();
            log.info("Redis模式: 集群模式");
            log.info("集群节点: {}", clusterNodes);
            log.info("最大重定向次数: {}", clusterConfig.getMaxRedirects());

            if (clusterConfig.getClusterNodes() != null) {
                log.info("解析的集群节点数量: {}", clusterConfig.getClusterNodes().size());
            }
        } else if (factory.getStandaloneConfiguration() != null) {
            // 单机模式
            RedisStandaloneConfiguration standaloneConfig = factory.getStandaloneConfiguration();
            log.info("Redis模式: 单机模式");
            log.info("Redis地址: {}:{}", standaloneConfig.getHostName(), standaloneConfig.getPort());
        }

        log.info("密码配置: {}", redisPassword != null && !redisPassword.isEmpty() ? "已配置" : "未配置");
    }



    /**
     * 测试Redis连接
     */
    private void testRedisConnection(Instant startTime) {
        try {
            log.info("开始测试Redis连接...");
            
            // 获取连接并测试
            redisConnectionFactory.getConnection().ping();
            
            Duration duration = Duration.between(startTime, Instant.now());
            log.info("Redis连接测试成功 - 总耗时: {}ms", duration.toMillis());
            log.info("=== IM-MESSAGE-STORE Redis 连接信息检查完成 ===");
            
        } catch (Exception e) {
            Duration duration = Duration.between(startTime, Instant.now());
            log.error("Redis连接测试失败 - 耗时: {}ms, 错误: {}", duration.toMillis(), e.getMessage());
            log.warn("请检查Redis服务状态和网络连接");
            log.info("=== IM-MESSAGE-STORE Redis 连接信息检查完成 ===");
        }
    }
}
