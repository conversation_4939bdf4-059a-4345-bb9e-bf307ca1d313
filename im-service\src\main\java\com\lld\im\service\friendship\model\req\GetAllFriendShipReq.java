package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 获取所有好友关系请求
 */
@ApiModel(description = "获取所有好友关系请求模型")
@Data
public class GetAllFriendShipReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要查询好友关系的用户唯一标识")
    @NotBlank(message = "用户id不能为空")
    private String fromId;
}
