package com.lld.im.service;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.redisson.spring.starter.RedissonAutoConfiguration;

@SpringBootApplication(scanBasePackages = {"com.lld.im.service",
        "com.lld.im.common"}, exclude = {RedissonAutoConfiguration.class, RedisAutoConfiguration.class})
@MapperScan("com.lld.im.service.*.dao.mapper")
//导入用户资料，删除用户资料，修改用户资料，查询用户资料
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }


}




