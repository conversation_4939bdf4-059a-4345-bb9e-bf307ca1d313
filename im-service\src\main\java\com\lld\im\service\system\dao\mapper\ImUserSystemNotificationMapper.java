package com.lld.im.service.system.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lld.im.service.system.model.dto.NotificationTypeLatestDto;
import com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface ImUserSystemNotificationMapper extends BaseMapper<ImUserSystemNotificationEntity> {

    /**
     * 按通知类型分页查询用户通知列表
     * @param page 分页参数
     * @param appId 应用ID
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 分页查询结果
     */
    IPage<ImUserSystemNotificationEntity> selectNotificationsByType(IPage<ImUserSystemNotificationEntity> page,
                                                                    @Param("appId") Integer appId,
                                                                    @Param("userId") String userId,
                                                                    @Param("notificationType") Integer notificationType);

    /**
     * 查询用户指定类型的未读通知列表
     * @param appId 应用ID
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 未读通知列表
     */
    List<ImUserSystemNotificationEntity> selectUnreadNotificationsByType(@Param("appId") Integer appId,
                                                                         @Param("userId") String userId,
                                                                         @Param("notificationType") Integer notificationType);

    // ==================== 优化查询方法 ====================

    /**
     * 获取用户有通知记录的所有通知类型列表（优化版本）
     * @param appId 应用ID
     * @param userId 用户ID
     * @return 通知类型列表
     */
    List<Integer> selectUserNotificationTypes(@Param("appId") Integer appId, @Param("userId") String userId);

    /**
     * 获取指定类型的最新通知记录（优化版本）
     * @param appId 应用ID
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 最新通知记录
     */
    NotificationTypeLatestDto selectLatestNotificationByType(@Param("appId") Integer appId,
                                                           @Param("userId") String userId,
                                                           @Param("notificationType") Integer notificationType);

    /**
     * 获取指定类型的未读数量（优化版本）
     * @param appId 应用ID
     * @param userId 用户ID
     * @param notificationType 通知类型
     * @return 未读数量
     */
    Integer selectUnreadCountByTypeOptimized(@Param("appId") Integer appId,
                                           @Param("userId") String userId,
                                           @Param("notificationType") Integer notificationType);


    /**
     * 增量同步查询通知列表
     * @param appId 应用ID
     * @param userId 用户ID
     * @param lastSequence 上次同步的序列号
     * @param limit 查询数量限制
     * @param onlyUnread 是否只查询未读通知：true-只查未读，false-查询所有
     * @return 通知列表
     */
    List<ImUserSystemNotificationEntity> selectNotificationsForSync(@Param("appId") Integer appId,
                                                                    @Param("userId") String userId,
                                                                    @Param("lastSequence") Long lastSequence,
                                                                    @Param("limit") Integer limit,
                                                                    @Param("onlyUnread") Boolean onlyUnread);

    /**
     * 批量查询通知详情（用于增量同步）
     * @param notificationIds 通知ID列表
     * @return 通知详情列表
     */
    List<NotificationTypeLatestDto> selectNotificationDetailsByIds(@Param("notificationIds") List<Long> notificationIds);

    /**
     * 批量插入用户通知关系（MyBatis-Plus扩展方法）
     * @param entityList 实体列表
     * @return 插入成功的记录数
     */
    Integer insertBatchSomeColumn(Collection<ImUserSystemNotificationEntity> entityList);
}