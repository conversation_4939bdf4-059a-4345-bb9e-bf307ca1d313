package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 直播间封禁用户实体类
 */
@Data
@TableName("im_live_room_blocked_user")
public class LiveRoomBlockedUser {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 操作者ID
     */
    private String operatorId;

    /**
     * 封禁到期时间
     */
    private Date blockEndTime;

    /**
     * 封禁原因
     */
    private String reason;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 