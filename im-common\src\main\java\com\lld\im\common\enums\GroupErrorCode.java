package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description:
 **/
public enum GroupErrorCode implements ApplicationExceptionEnum {

    GROUP_IS_NOT_EXIST(40000,"group.not.exist"),

    GROUP_IS_EXIST(40001,"group.already.exist"),

    GROUP_IS_HAVE_OWNER(40002,"group.already.have.owner"),

    USER_IS_JOINED_GROUP(40003,"group.member.already.exist"),

    USER_JOIN_GROUP_ERROR(40004,"group.member.add.failed"),

    GROUP_MEMBER_IS_BEYOND(40005,"group.member.limit.exceeded"),

    MEMBER_IS_NOT_JOINED_GROUP(40006,"group.member.not.exist"),

    THIS_OPERATE_NEED_MANAGER_ROLE(40007,"group.permission.need.manager"),

    THIS_OPERATE_NEED_APPMANAGER_ROLE(40008,"group.permission.need.app.manager"),

    THIS_OPERATE_NEED_OWNER_ROLE(40009,"group.permission.need.owner"),

    G<PERSON>UP_OWNER_IS_NOT_REMOVE(40010,"group.owner.cannot.remove"),

    UPDATE_GROUP_BASE_INFO_ERROR(40011,"group.update.info.failed"),

    THIS_GROUP_IS_MUTE(40012,"group.muted"),

    IMPORT_GROUP_ERROR(40013,"group.import.failed"),

    THIS_OPERATE_NEED_ONESELF(40014,"group.permission.need.self"),

    PRIVATE_GROUP_CAN_NOT_DESTORY(40015,"group.private.cannot.destroy"),

    PUBLIC_GROUP_MUST_HAVE_OWNER(40016,"group.public.must.have.owner"),

    GROUP_MEMBER_IS_SPEAK(40017,"group.member.muted"),

    GROUP_IS_DESTROY(40018,"group.destroyed"),

    GROUP_JOIN_TYPE_NOT_ALLOW(40019,"group.join.not.allowed"),

    GROUP_APPLY_ALREADY_EXISTS(40020,"group.apply.already.exists"),

    GROUP_APPLY_FAILED(40021,"group.apply.failed"),

    GROUP_APPLY_NOT_EXIST(40022,"group.apply.not.exist"),

    GROUP_APPLY_STATUS_ERROR(40023,"group.apply.status.error"),

    GROUP_APPLY_APPROVE_FAILED(40024,"group.apply.approve.failed"),

    GROUP_SPEAK_PERMISSION_DENIED(40025,"group.speak.permission.denied"),

    GROUP_SPEAK_PERMISSION_CONFIG_ERROR(40026,"group.speak.permission.config.error"),

    GROUP_SPEAK_PERMISSION_UPDATE_FAILED(40027,"group.speak.permission.update.failed"),

    GROUP_SPEAK_PERMISSION_INVALID_MEMBERS(40028,"group.speak.permission.invalid.members"),

    GROUP_JOIN_THRESHOLD_CONFIG_ERROR(40029,"group.join.threshold.config.error"),

    GROUP_JOIN_THRESHOLD_UPDATE_FAILED(40030,"group.join.threshold.update.failed"),

    GROUP_JOIN_THRESHOLD_NOT_FOLLOW(40031,"group.join.threshold.follow.not.enough"),

    GROUP_JOIN_THRESHOLD_FOLLOW_DAYS_NOT_ENOUGH(40032,"group.join.threshold.follow.days.not.enough"),

    GROUP_JOIN_THRESHOLD_CHECK_FAILED(40033,"group.join.threshold.check.failed"),

    ;

    private int code;
    private String messageKey;

    GroupErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        // 由于枚举项较多，这里只列出部分关键的，其他的返回messageKey
        switch (this) {
            case GROUP_IS_NOT_EXIST:
                return "群不存在";
            case GROUP_IS_EXIST:
                return "群已存在";
            case USER_IS_JOINED_GROUP:
                return "该用户已经进入该群";
            case MEMBER_IS_NOT_JOINED_GROUP:
                return "该用户不在群内";
            case GROUP_MEMBER_IS_BEYOND:
                return "群成员已达到上限";
            case THIS_OPERATE_NEED_MANAGER_ROLE:
                return "该操作只允许群主/管理员操作";
            case THIS_OPERATE_NEED_OWNER_ROLE:
                return "该操作只允许群主操作";
            case GROUP_OWNER_IS_NOT_REMOVE:
                return "群主无法移除";
            case THIS_GROUP_IS_MUTE:
                return "该群禁止发言";
            case GROUP_IS_DESTROY:
                return "群组已解散";
            default:
                return this.messageKey;
        }
    }

}
