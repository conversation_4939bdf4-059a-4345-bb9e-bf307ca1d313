package com.lld.im.service.friendship.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.model.SyncReq;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.friendship.model.req.*;
import com.lld.im.service.friendship.service.ImFriendService;
import com.lld.im.service.interceptor.UserPermissionCheck;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@Api(tags = "好友关系管理", description = "好友关系的增删改查和黑名单管理相关接口")
@RestController
@RequestMapping("v1/friendship")
public class ImFriendShipController extends BaseController {

    @Autowired
    ImFriendService imFriendShipService;

    @ApiOperation(value = "导入好友关系", notes = "批量导入用户的好友关系数据")
    @PostMapping("/importFriendShip")
    public ResponseVO importFriendShip(
            @ApiParam(value = "导入好友关系请求参数", required = true) @RequestBody @Validated ImporFriendShipReq req){
        // 自动填充公共参数（从请求头或ThreadLocal获取）
        fillCommonParams(req);
        return imFriendShipService.importFriendShip(req);
    }

    @ApiOperation(value = "添加好友", notes = "向指定用户发送好友申请或直接添加为好友")
    @PostMapping("/addFriend")
    @UserPermissionCheck("fromId")
    public ResponseVO addFriend(
            @ApiParam(value = "添加好友请求参数", required = true) @RequestBody @Validated AddFriendReq req){
        fillCommonParams(req);
        return imFriendShipService.addFriend(req);
    }

    @ApiOperation(value = "更新好友信息", notes = "更新好友的备注、分组等信息")
    @PutMapping("/updateFriend")
    @UserPermissionCheck("fromId")
    public ResponseVO updateFriend(
            @ApiParam(value = "更新好友请求参数", required = true) @RequestBody @Validated UpdateFriendReq req){
        fillCommonParams(req);
        return imFriendShipService.updateFriend(req);
    }

    @ApiOperation(value = "删除好友", notes = "删除指定的好友关系")
    @DeleteMapping("/deleteFriend")
    @UserPermissionCheck("fromId")
    public ResponseVO deleteFriend(
            @ApiParam(value = "删除好友请求参数", required = true) @RequestBody @Validated DeleteFriendReq req){
        fillCommonParams(req);
        return imFriendShipService.deleteFriend(req);
    }

    @ApiOperation(value = "删除所有好友", notes = "删除用户的所有好友关系")
    @DeleteMapping("/deleteAllFriend")
    @UserPermissionCheck("fromId")
    public ResponseVO deleteAllFriend(
            @ApiParam(value = "删除所有好友请求参数", required = true) @RequestBody @Validated DeleteFriendReq req){
        fillCommonParams(req);
        return imFriendShipService.deleteAllFriend(req);
    }

    @ApiOperation(value = "获取所有好友", notes = "获取用户的所有好友关系列表")
    @PostMapping("/getAllFriendShip")
    @UserPermissionCheck("fromId")
    public ResponseVO getAllFriendShip(
            @ApiParam(value = "获取好友列表请求参数", required = true) @RequestBody @Validated GetAllFriendShipReq req){
        fillCommonParams(req);
        return imFriendShipService.getAllFriendShip(req);
    }

    @ApiOperation(value = "获取好友关系", notes = "获取与指定用户的好友关系详情")
    @PostMapping("/getRelation")
    @UserPermissionCheck("fromId")
    public ResponseVO getRelation(
            @ApiParam(value = "获取好友关系请求参数", required = true) @RequestBody @Validated GetRelationReq req){
        fillCommonParams(req);
        return imFriendShipService.getRelation(req);
    }

    @ApiOperation(value = "检查好友关系", notes = "检查两个用户之间是否为好友关系")
    @PostMapping("/checkFriend")
    @UserPermissionCheck("fromId")
    public ResponseVO checkFriend(
            @ApiParam(value = "检查好友关系请求参数", required = true) @RequestBody @Validated CheckFriendShipReq req){
        fillCommonParams(req);
        return imFriendShipService.checkFriendship(req);
    }

    @ApiOperation(value = "添加黑名单", notes = "将指定用户添加到黑名单中")
    @PostMapping("/addBlack")
    @UserPermissionCheck("fromId")
    public ResponseVO addBlack(
            @ApiParam(value = "添加黑名单请求参数", required = true) @RequestBody @Validated AddFriendShipBlackReq req){
        fillCommonParams(req);
        return imFriendShipService.addBlack(req);
    }

    @ApiOperation(value = "移除黑名单", notes = "将指定用户从黑名单中移除")
    @DeleteMapping("/deleteBlack")
    @UserPermissionCheck("fromId")
    public ResponseVO deleteBlack(
            @ApiParam(value = "移除黑名单请求参数", required = true) @RequestBody @Validated DeleteBlackReq req){
        fillCommonParams(req);
        return imFriendShipService.deleteBlack(req);
    }

    @ApiOperation(value = "检查黑名单状态", notes = "检查指定用户是否在黑名单中")
    @PostMapping("/checkBlck")
    @UserPermissionCheck("fromId")
    public ResponseVO checkBlck(
            @ApiParam(value = "检查黑名单请求参数", required = true) @RequestBody @Validated CheckFriendShipReq req){
        fillCommonParams(req);
        return imFriendShipService.checkBlck(req);
    }

    @ApiOperation(value = "同步好友列表", notes = "增量同步用户的好友关系列表数据")
    @PostMapping("/syncFriendshipList")
    public ResponseVO syncFriendshipList(
            @ApiParam(value = "同步请求参数", required = true) @RequestBody @Validated SyncReq req){
        fillCommonParams(req);
        return imFriendShipService.syncFriendshipList(req);
    }
}
