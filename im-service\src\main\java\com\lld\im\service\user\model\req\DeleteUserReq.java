package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 删除用户请求
 */
@ApiModel(description = "删除用户请求模型")
@Data
public class DeleteUserReq extends RequestBase {

    @ApiModelProperty(value = "用户ID列表", required = true, example = "[\"user123\", \"user456\"]", notes = "要删除的用户ID列表")
    @NotEmpty(message = "用户id不能为空")
    private List<String> userId;
}
