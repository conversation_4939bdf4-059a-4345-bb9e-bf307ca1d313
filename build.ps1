# IM System Simple Build Script
param(
    [string[]]$Modules = @(),
    [switch]$Clean,
    [switch]$SkipTests,
    [switch]$Package,
    [switch]$Help
)

if ($Help) {
    Write-Host "IM System Build Script" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Usage:" -ForegroundColor Yellow
    Write-Host "  .\build-simple.ps1                    # Build all modules" -ForegroundColor White
    Write-Host "  .\build-simple.ps1 im-service         # Build specific module" -ForegroundColor White
    Write-Host "  .\build-simple.ps1 -Clean             # Clean build" -ForegroundColor White
    Write-Host "  .\build-simple.ps1 -SkipTests         # Skip tests" -ForegroundColor White
    Write-Host "  .\build-simple.ps1 -Package           # Build and package" -ForegroundColor White
    Write-Host ""
    exit 0
}

function Write-BuildLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        default { "Cyan" }
    }
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

Write-Host "IM System Build Script" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Cyan

# Check Maven
Write-BuildLog "Checking Maven installation..." "INFO"
try {
    $mvnOutput = & mvn -version 2>&1
    if ($LASTEXITCODE -eq 0 -and $mvnOutput) {
        $mvnVersion = $mvnOutput | Select-Object -First 1
        Write-BuildLog "Maven found: $($mvnVersion.ToString().Trim())" "SUCCESS"
    } else {
        Write-BuildLog "Maven not found or configured incorrectly" "ERROR"
        exit 1
    }
}
catch {
    Write-BuildLog "Maven not found or configured incorrectly" "ERROR"
    exit 1
}

# Check Java
Write-BuildLog "Checking Java installation..." "INFO"
try {
    $javaOutput = & java -version 2>&1
    if ($LASTEXITCODE -eq 0 -and $javaOutput) {
        $javaVersion = $javaOutput | Select-Object -First 1
        Write-BuildLog "Java found: $($javaVersion.ToString().Trim())" "SUCCESS"
    } else {
        Write-BuildLog "Java not found or configured incorrectly" "ERROR"
        exit 1
    }
}
catch {
    Write-BuildLog "Java not found or configured incorrectly" "ERROR"
    exit 1
}

# Check project structure
if (-not (Test-Path "pom.xml")) {
    Write-BuildLog "pom.xml not found. Please run from project root directory" "ERROR"
    exit 1
}

# Build Maven arguments
$mvnArgs = @()

if ($Clean) {
    $mvnArgs += "clean"
    Write-BuildLog "Clean build enabled" "INFO"
}

if ($Modules.Count -gt 0) {
    $mvnArgs += "package"
    $mvnArgs += "-pl"
    $mvnArgs += ($Modules -join ",")
    $mvnArgs += "-am"
    Write-BuildLog "Building modules: $($Modules -join ', ')" "INFO"
} else {
    $mvnArgs += "package"
    Write-BuildLog "Building all modules" "INFO"
}

if ($SkipTests) {
    $mvnArgs += "-DskipTests"
    Write-BuildLog "Skipping tests" "INFO"
}

$mvnArgs += "-Dmaven.compile.fork=true"

Write-BuildLog "Executing: mvn $($mvnArgs -join ' ')" "INFO"

# Execute build
$buildStart = Get-Date
try {
    & mvn $mvnArgs
    $buildResult = $LASTEXITCODE
}
catch {
    Write-BuildLog "Build execution failed: $($_.Exception.Message)" "ERROR"
    exit 1
}

$buildEnd = Get-Date
$buildDuration = $buildEnd - $buildStart

if ($buildResult -eq 0) {
    Write-BuildLog "Build completed successfully in $($buildDuration.ToString('mm\:ss'))" "SUCCESS"
    
    # Show build results
    Write-Host ""
    Write-Host "Build Results:" -ForegroundColor Cyan
    Write-Host "==============" -ForegroundColor Cyan
    
    $allModules = @("im-tcp", "im-service", "im-message-store")
    foreach ($module in $allModules) {
        $targetDir = "$module/target"
        if (Test-Path $targetDir) {
            $jars = Get-ChildItem "$targetDir/*.jar" -ErrorAction SilentlyContinue
            if ($jars) {
                foreach ($jar in $jars) {
                    $size = [math]::Round($jar.Length / 1MB, 1)
                    Write-BuildLog "Built: $module - $($jar.Name) ($size MB)" "SUCCESS"
                }
            } else {
                Write-BuildLog "No JAR found for: $module" "WARN"
            }
        } else {
            Write-BuildLog "Target directory not found for: $module" "WARN"
        }
    }
    
    # Auto package if requested
    if ($Package) {
        Write-BuildLog "Starting packaging..." "INFO"
        if (Test-Path "deploy_package.ps1") {
            if ($Modules.Count -gt 0) {
                & .\deploy_package.ps1 $Modules
            } else {
                & .\deploy_package.ps1
            }
        } else {
            Write-BuildLog "Package script not found: deploy_package.ps1" "WARN"
        }
    }
    
} else {
    Write-BuildLog "Build failed with exit code: $buildResult" "ERROR"
    exit 1
}

Write-BuildLog "Build script completed" "SUCCESS"
