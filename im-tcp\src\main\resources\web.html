<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>WebSocket客户端</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .section {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .section h3 {
            margin-top: 0;
            margin-bottom: 15px;
        }

        .guest-section {
            border-color: #007bff;
            background-color: #f8f9fa;
        }

        .guest-section h3 {
            color: #007bff;
        }

        .liveroom-section {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .liveroom-section h3 {
            color: #28a745;
        }

        .message-section {
            border-color: #ffc107;
            background-color: #fffdf5;
        }

        .message-section h3 {
            color: #856404;
        }

        input[type="button"] {
            padding: 8px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            vertical-align: middle;
        }

        textarea {
            margin: 5px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
            font-family: Arial, sans-serif;
            vertical-align: middle;
            resize: vertical;
        }

        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .form-group {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-group label {
            min-width: 80px;
            font-weight: bold;
            color: #555;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }

        .status-text {
            margin-left: 10px;
            color: #666;
            font-style: italic;
        }

        #responseText {
            width: 100%;
            height: 400px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            background-color: #f8f8f8;
            border: 1px solid #ccc;
            padding: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>WebSocket客户端</h1>
<script type="text/javascript">


    ByteBuffer = function (arrayBuf, offset) {

        var Type_Byte = 1;
        var Type_Short = 2;
        var Type_UShort = 3;
        var Type_Int32 = 4;
        var Type_UInt32 = 5;
        var Type_String = 6;//变长字符串，前两个字节表示长度
        var Type_VString = 7;//定长字符串
        var Type_Int64 = 8;
        var Type_Float = 9;
        var Type_Double = 10;
        var Type_ByteArray = 11;

        var _org_buf = arrayBuf ? (arrayBuf.constructor == DataView ? arrayBuf : (arrayBuf.constructor == Uint8Array ? new DataView(arrayBuf.buffer, offset) : new DataView(arrayBuf, offset))) : new DataView(new Uint8Array([]).buffer);
        var _offset = offset || 0;
        var _list = [];
        var _littleEndian = false;

        //指定字节序 为BigEndian
        this.bigEndian = function () {
            _littleEndian = false;
            return this;
        };

        //指定字节序 为LittleEndian
        this.littleEndian = function () {
            _littleEndian = true;
            return this;
        };

        if (!ArrayBuffer.prototype.slice) {
            ArrayBuffer.prototype.slice = function (start, end) {
                var that = new Uint8Array(this);
                if (end == undefined) end = that.length;
                var result = new ArrayBuffer(end - start);
                var resultArray = new Uint8Array(result);
                for (var i = 0; i < resultArray.length; i++)
                    resultArray[i] = that[i + start];
                return result;
            }
        }

        function utf8Write(view, offset, str) {
            var c = 0;
            for (var i = 0, l = str.length; i < l; i++) {
                c = str.charCodeAt(i);
                if (c < 0x80) {
                    view.setUint8(offset++, c);
                } else if (c < 0x800) {
                    view.setUint8(offset++, 0xc0 | (c >> 6));
                    view.setUint8(offset++, 0x80 | (c & 0x3f));
                } else if (c < 0xd800 || c >= 0xe000) {
                    view.setUint8(offset++, 0xe0 | (c >> 12));
                    view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);
                    view.setUint8(offset++, 0x80 | (c & 0x3f));
                } else {
                    i++;
                    c = 0x10000 + (((c & 0x3ff) << 10) | (str.charCodeAt(i) & 0x3ff));
                    view.setUint8(offset++, 0xf0 | (c >> 18));
                    view.setUint8(offset++, 0x80 | (c >> 12) & 0x3f);
                    view.setUint8(offset++, 0x80 | (c >> 6) & 0x3f);
                    view.setUint8(offset++, 0x80 | (c & 0x3f));
                }
            }
        }

        function utf8Read(view, offset, length) {
            var string = '', chr = 0;
            for (var i = offset, end = offset + length; i < end; i++) {
                var byte = view.getUint8(i);
                if ((byte & 0x80) === 0x00) {
                    string += String.fromCharCode(byte);
                    continue;
                }
                if ((byte & 0xe0) === 0xc0) {
                    string += String.fromCharCode(
                        ((byte & 0x0f) << 6) |
                        (view.getUint8(++i) & 0x3f)
                    );
                    continue;
                }
                if ((byte & 0xf0) === 0xe0) {
                    string += String.fromCharCode(
                        ((byte & 0x0f) << 12) |
                        ((view.getUint8(++i) & 0x3f) << 6) |
                        ((view.getUint8(++i) & 0x3f) << 0)
                    );
                    continue;
                }
                if ((byte & 0xf8) === 0xf0) {
                    chr = ((byte & 0x07) << 18) |
                        ((view.getUint8(++i) & 0x3f) << 12) |
                        ((view.getUint8(++i) & 0x3f) << 6) |
                        ((view.getUint8(++i) & 0x3f) << 0);
                    if (chr >= 0x010000) { // surrogate pair
                        chr -= 0x010000;
                        string += String.fromCharCode((chr >>> 10) + 0xD800, (chr & 0x3FF) + 0xDC00);
                    } else {
                        string += String.fromCharCode(chr);
                    }
                    continue;
                }
                throw new Error('Invalid byte ' + byte.toString(16));
            }
            return string;
        }

        function utf8Length(str) {
            var c = 0, length = 0;
            for (var i = 0, l = str.length; i < l; i++) {
                c = str.charCodeAt(i);
                if (c < 0x80) {
                    length += 1;
                } else if (c < 0x800) {
                    length += 2;
                } else if (c < 0xd800 || c >= 0xe000) {
                    length += 3;
                } else {
                    i++;
                    length += 4;
                }
            }
            return length;
        }

        this.byte = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getUint8(_offset, _littleEndian));
                _offset += 1;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Byte, d: val, l: 1});
                _offset += 1;
            }
            return this;
        };

        this.short = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getInt16(_offset, _littleEndian));
                _offset += 2;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Short, d: val, l: 2});
                _offset += 2;
            }
            return this;
        };

        this.ushort = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getUint16(_offset, _littleEndian));
                _offset += 2;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_UShort, d: val, l: 2});
                _offset += 2;
            }
            return this;
        };

        this.int32 = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getInt32(_offset, _littleEndian));
                _offset += 4;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Int32, d: val, l: 4});
                _offset += 4;
            }
            return this;
        };

        this.uint32 = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getUint32(_offset, _littleEndian));
                _offset += 4;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_UInt32, d: val, l: 4});
                _offset += 4;
            }
            return this;
        };

        /**
         * 新加的方法，获取bytebuffer的长度
         */
        this.blength = function () {
            return _offset;
        };

        /**
         * 变长字符串 前4个字节表示字符串长度
         **/
        this.string = function (val, index) {
            if (arguments.length == 0) {
                var len = _org_buf.getInt32(_offset, _littleEndian);
                _offset += 4;
                _list.push(utf8Read(_org_buf, _offset, len));
                _offset += len;
            } else {
                var len = 0;
                if (val) {
                    len = utf8Length(val);
                }
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_String, d: val, l: len});
                _offset += len + 4;
            }
            return this;
        };

        /**
         * 定长字符串 val为null时，读取定长字符串（需指定长度len）
         **/
        this.vstring = function (val, len, index) {
            if (!len) {
                throw new Error('vstring must got len argument');
                return this;
            }
            if (val == undefined || val == null) {
                var vlen = 0;//实际长度
                for (var i = _offset; i < _offset + len; i++) {
                    if (_org_buf.getUint8(i) > 0) vlen++;
                }
                _list.push(utf8Read(_org_buf, _offset, vlen));
                _offset += len;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_VString, d: val, l: len});
                _offset += len;
            }
            return this;
        };

        this.int64 = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getFloat64(_offset, _littleEndian));
                _offset += 8;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Int64, d: val, l: 8});
                _offset += 8;
            }
            return this;
        };

        this.float = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getFloat32(_offset, _littleEndian));
                _offset += 4;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Float, d: val, l: 4});
                _offset += 4;
            }
            return this;
        };

        this.double = function (val, index) {
            if (arguments.length == 0) {
                _list.push(_org_buf.getFloat64(_offset, _littleEndian));
                _offset += 8;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_Double, d: val, l: 8});
                _offset += 8;
            }
            return this;
        };

        /**
         * 写入或读取一段字节数组
         **/
        this.byteArray = function (val, len, index) {
            if (!len) {
                throw new Error('byteArray must got len argument');
                return this;
            }
            if (val == undefined || val == null) {
                var arr = new Uint8Array(_org_buf.buffer.slice(_offset, _offset + len));
                _list.push(arr);
                _offset += len;
            } else {
                _list.splice(index != undefined ? index : _list.length, 0, {t: Type_ByteArray, d: val, l: len});
                _offset += len;
            }
            return this;
        };

        /**
         * 解包成数据数组
         **/
        this.unpack = function () {
            return _list;
        };

        /**
         * 打包成二进制,在前面加上4个字节表示包长
         **/
        this.packWithHead = function () {
            return this.pack(true);
        };

        /**
         * 打包成二进制
         * @param ifHead 是否在前面加上4个字节表示包长
         **/
        this.pack = function (ifHead) {
            _org_buf = new DataView(new ArrayBuffer((ifHead) ? _offset + 4 : _offset));
            var offset = 0;
            if (ifHead) {
                _org_buf.setUint32(offset, _offset, _littleEndian);
                offset += 4;
            }
            for (var i = 0; i < _list.length; i++) {
                switch (_list[i].t) {
                    case Type_Byte:
                        _org_buf.setInt8(offset, _list[i].d);
                        offset += _list[i].l;
                        break;
                    case Type_Short:
                        _org_buf.setInt16(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_UShort:
                        _org_buf.setUint16(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_Int32:
                        _org_buf.setInt32(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_UInt32:
                        _org_buf.setUint32(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_String:
                        //前4个字节表示字符串长度
                        _org_buf.setUint32(offset, _list[i].l, _littleEndian);
                        offset += 4;
                        utf8Write(_org_buf, offset, _list[i].d);
                        offset += _list[i].l;
                        break;
                    case Type_VString:
                        utf8Write(_org_buf, offset, _list[i].d);
                        var vlen = utf8Length(_list[i].d);//字符串实际长度
                        //补齐\0
                        for (var j = offset + vlen; j < offset + _list[i].l; j++) {
                            _org_buf.setUint8(j, 0);
                        }
                        offset += _list[i].l;
                        break;
                    case Type_Int64:
                        _org_buf.setFloat64(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_Float:
                        _org_buf.setFloat32(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_Double:
                        _org_buf.setFloat64(offset, _list[i].d, _littleEndian);
                        offset += _list[i].l;
                        break;
                    case Type_ByteArray:
                        var indx = 0;
                        for (var j = offset; j < offset + _list[i].l; j++) {
                            if (indx < _list[i].d.length) {
                                _org_buf.setUint8(j, _list[i].d[indx]);
                            } else {//不够的话，后面补齐0x00
                                _org_buf.setUint8(j, 0);
                            }
                            indx++
                        }
                        offset += _list[i].l;
                        break;
                }
            }
            return _org_buf.buffer;
        };

        /**
         * 未读数据长度
         **/
        this.getAvailable = function () {
            if (!_org_buf) return _offset;
            return _org_buf.buffer.byteLength - _offset;
        };
    }

    function uuid() {
        var s = [];
        var hexDigits = "0123456789abcdef";
        for (var i = 0; i < 36; i++) {
            s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
        }
        s[14] = "4"; // bits 12-15 of the time_hi_and_version field to 0010
        s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1); // bits 6-7 of the clock_seq_hi_and_reserved to 01
        s[8] = s[13] = s[18] = s[23] = "-";

        var uuid = s.join("");
        return uuid;
    }

    var socket;
    var currentGuestId = null;  // 当前游客ID
    var currentRoomId = null;   // 当前直播间ID
    var isGuest = false;        // 是否为游客模式
    var debugMode = true;       // 调试模式开关
    var heartbeatTimer = null;  // 心跳定时器

    /**
     * 设置WebSocket地址
     */
    function setWebSocketUrl(url) {
        document.getElementById("websocketUrl").value = url;
        showInfo("WebSocket地址已设置为: " + url);
    }

    /**
     * 获取当前WebSocket地址
     */
    function getWebSocketUrl() {
        var urlInput = document.getElementById("websocketUrl");
        return urlInput ? urlInput.value.trim() : "wss://www.ano999.com/gateway-api/ws";
    }

    /**
     * 初始化WebSocket连接
     */
    function initWebSocket() {
        if (!window.WebSocket) {
            alert("浏览器不支持WebSocket！");
            return;
        }

        var wsUrl = getWebSocketUrl();
        if (!wsUrl) {
            showError("请输入有效的WebSocket地址");
            return;
        }

        try {
            console.log("正在连接WebSocket服务器: " + wsUrl);
            socket = new WebSocket(wsUrl);

            //客户端收到服务器消息的时候就会执行这个回调方法
            socket.onmessage = function (event) {
            var ta = document.getElementById("responseText");
            var userId = document.getElementById("userId").value;

            // 声明变量在函数作用域内
            var command, bodyLen, msgBody;

            try {
                var bytebuf = new ByteBuffer(event.data);
                let byteBuffer = bytebuf.int32().int32().unpack();

                command = byteBuffer[0];
                bodyLen = byteBuffer[1];

                // 添加详细的调试信息
                console.log("🔍 解析WebSocket消息详情:", {
                    command: command,
                    bodyLength: bodyLen,
                    totalDataLength: event.data.byteLength,
                    remainingBytes: event.data.byteLength - 8
                });

                // 检查数据长度是否匹配
                if (bodyLen > event.data.byteLength - 8) {
                    console.error("❌ 数据长度不匹配:", {
                        expectedBodyLength: bodyLen,
                        actualRemainingBytes: event.data.byteLength - 8
                    });
                    throw new Error(`数据长度不匹配: 期望${bodyLen}字节，实际剩余${event.data.byteLength - 8}字节`);
                }

                let unpack = bytebuf.vstring(null, bodyLen).unpack();
                msgBody = unpack[2];

                // 添加原始消息调试信息
                console.log("📨 WebSocket收到消息:", {
                    command: command,
                    bodyLength: bodyLen,
                    body: msgBody,
                    timestamp: new Date().toLocaleTimeString()
                });

                if (debugMode) {
                    console.log("WebSocket收到原始消息详情:", {
                        command: command,
                        bodyLength: bodyLen,
                        body: msgBody,
                        eventData: event.data
                    });
                }
            } catch (error) {
                console.error("解析WebSocket消息失败:", error);
                console.error("原始数据:", event.data);
                console.error("数据长度:", event.data.byteLength);

                // 打印前几个字节用于调试
                if (event.data.byteLength > 0) {
                    const view = new DataView(event.data);
                    const firstBytes = [];
                    for (let i = 0; i < Math.min(20, event.data.byteLength); i++) {
                        firstBytes.push('0x' + view.getUint8(i).toString(16).padStart(2, '0'));
                    }
                    console.error("前20个字节:", firstBytes.join(' '));
                }

                showError("解析消息失败: " + error.message);
                return;
            }
            var version = 1;

            var clientType = document.getElementById("clientType").value;
            clientType = parseInt(clientType);
            var messageType = 0x0;
            var appId = document.getElementById("appId").value;
            appId = parseInt(appId)

            var imei = document.getElementById("imei").value;
            var imeiLen = utf8Length(imei);

            if (debugMode) {
                console.log("收到服务端发来的消息: command=" + command + ", body=" + msgBody);
                showInfo("收到消息: command=" + command);
            }

            // 特别关注直播间相关消息
            if (command >= 5010 && command <= 5019) {
                console.log("🎬 收到直播间相关消息: command=" + command);
                showInfo("🎬 直播间消息: command=" + command);
            }

            // 确保必要的变量都已定义
            if (typeof command === 'undefined' || typeof msgBody === 'undefined') {
                console.error("消息解析不完整: command=" + command + ", msgBody=" + msgBody);
                showError("消息解析不完整，跳过处理");
                return;
            }

            if(command == 1103){
                var d = JSON.parse(msgBody)
                var data = d.data;
                let userId = document.getElementById('userId').value;

                if (debugMode) {
                    console.log("📨 收到私聊消息:", data);
                }

                if(data.fromId == userId){
                    ta.value = ta.value + "\n" + "自己(私聊)：" + data.messageBody;
                }else if(data.fromId != userId){
                    ta.value = ta.value + "\n" + data.fromId + "(私聊)：" + data.messageBody;
                }

                // 滚动到底部
                ta.scrollTop = ta.scrollHeight;

                // data.fromId = data.toId;
                // data.conversationId = "0_" + data.toId + "_" + data.fromId;
                // data.conversationType = 0;

                if(userId != data.fromId){
                    var rAck = {
                        "fromId":userId,
                        "toId":data.fromId,
                        "messageKey":data.messageKey,
                        "messageId":data.messageId,
                        "messageSequence":data.messageSequence,
                    }

                    let messageReciver = new ByteBuffer();
                    var jsonData = JSON.stringify(rAck);
                    // 修复：使用UTF-8字节长度计算
                    let bodyLen = utf8Length(jsonData);
                    messageReciver.int32(1107)
                        .int32(version).int32(clientType)
                        .int32(messageType).int32(appId)
                        .int32(imeiLen).int32(bodyLen).vstring(imei,imeiLen)
                        .vstring(jsonData, bodyLen);
                    socket.send(messageReciver.pack());
                }

                if(clientType == 1 ){
                    let messageReader = new ByteBuffer();
                    var toId = data.fromId;
                    if(data.fromId == userId){
                        toId = data.toId;
                    }
                    var readedData = {
                        "fromId":userId,
                        "toId":toId,
                        "conversationType":0,
                        "messageSequence":data.messageSequence,
                    }
                    var readData = JSON.stringify(readedData);
                    // 修复：使用UTF-8字节长度计算
                    let readBodyLen = utf8Length(readData);
                    messageReader.int32(1106)
                        .int32(version).int32(clientType)
                        .int32(messageType).int32(appId)
                        .int32(imeiLen).int32(readBodyLen).vstring(imei,imeiLen)
                        .vstring(readData, readBodyLen);
                    socket.send(messageReader.pack());
                }

            }else if(command == 2104){
                var d = JSON.parse(msgBody)
                var data = d.data;
                let userId = document.getElementById('userId').value;

                if (debugMode) {
                    console.log("👥 收到群聊消息:", data);
                }

                // 在服务器输出区域显示群聊消息
                if(data.fromId == userId){
                    ta.value = ta.value + "\n" + "自己(群聊)：" + data.messageBody;
                }else{
                    ta.value = ta.value + "\n" + data.fromId + "(群聊)：" + data.messageBody;
                }

                // 滚动到底部
                ta.scrollTop = ta.scrollHeight;

                if(clientType == 1){
                    let messageReader = new ByteBuffer();
                    var toId = data.fromId;
                    if(data.fromId == userId){
                        toId = data.toId;
                    }
                    var readedData = {
                        "fromId":userId,
                        "toId":data.fromId,
                        "groupId":data.groupId,
                        "conversationType":1,
                        "messageSequence":data.messageSequence,
                    }
                    var readData = JSON.stringify(readedData);
                    let readBodyLen = utf8Length(readData);
                    messageReader.int32(2106)
                        .int32(version).int32(clientType)
                        .int32(messageType).int32(appId)
                        .int32(imeiLen).int32(readBodyLen).vstring(imei,imeiLen)
                        .vstring(readData, readBodyLen);
                    socket.send(messageReader.pack());
                }
            }

            // 处理直播间消息
            else if(command == 5010){
                try {
                    var d = JSON.parse(msgBody);
                    var data = d.data || d;
                    let userId = document.getElementById('userId').value;

                    if (debugMode) {
                        console.log("收到直播间消息原始数据:", JSON.stringify(d, null, 2));
                        console.log("解析后的消息数据:", JSON.stringify(data, null, 2));
                        console.log("当前用户ID:", userId, "当前游客ID:", currentGuestId);
                    }

                    // 兼容不同的发送者ID字段名
                    let senderId = data.fromId || data.userId;
                    let currentUserId = currentGuestId || userId;

                    // 获取消息内容
                    let messageContent = data.content || data.messageBody || "无内容";

                    if(senderId == currentUserId){
                        ta.value = ta.value + "\n" + "自己：" + messageContent;
                        if (debugMode) {
                            showInfo("发送消息成功: " + messageContent);
                        }
                    } else {
                        let displayName = data.fromNickname || senderId;
                        ta.value = ta.value + "\n" + displayName + "：" + messageContent;
                        if (debugMode) {
                            showInfo("收到来自 " + displayName + " 的消息");
                        }
                    }

                    // 滚动到底部
                    ta.scrollTop = ta.scrollHeight;
                } catch (error) {
                    console.error("处理直播间消息失败:", error);
                    showError("处理直播间消息失败: " + error.message);
                }
            }
            // 处理直播间消息ACK
            else if(command == 5011){
                var d = JSON.parse(msgBody);
                console.log("直播间消息ACK:", d);
                // 消息发送确认，通常不需要特殊处理
            }
            // 处理加入直播间响应或通知
            else if(command == 5012){
                try {
                    var d = JSON.parse(msgBody);
                    var data = d.data || d;
                    console.log("加入直播间响应/通知:", JSON.stringify(data, null, 2));

                    // 判断是自己加入还是他人加入
                    let userId = document.getElementById('userId').value;
                    let joinUserId = data.userId || data.fromId;
                    let currentUserId = currentGuestId || userId;

                    if(joinUserId == currentUserId) {
                        showSuccess("成功加入直播间");
                        handleJoinRoomSuccess();
                    } else {
                        let displayName = data.fromNickname || data.nickname || joinUserId;
                        ta.value = ta.value + "\n" + "系统：" + displayName + " 加入了直播间";
                        ta.scrollTop = ta.scrollHeight;
                    }
                } catch (error) {
                    console.error("处理加入直播间消息失败:", error);
                    showError("处理加入直播间消息失败: " + error.message);
                }
            }
            // 处理离开直播间响应或通知
            else if(command == 5013){
                try {
                    var d = JSON.parse(msgBody);
                    var data = d.data || d;

                    // 处理双重嵌套的data结构
                    if (data.data && typeof data.data === 'object') {
                        data = data.data;
                    }

                    console.log("离开直播间响应/通知:", JSON.stringify(data, null, 2));

                    // 判断是自己离开还是他人离开
                    let userId = document.getElementById('userId').value;
                    let leaveUserId = data.userId || data.fromId;
                    let currentUserId = currentGuestId || userId;

                    if(leaveUserId == currentUserId) {
                        showSuccess("已离开直播间");
                        handleLeaveRoomSuccess();
                    } else {
                        let displayName = data.fromNickname || data.nickname || leaveUserId;
                        ta.value = ta.value + "\n" + "系统：" + displayName + " 离开了直播间";
                        ta.scrollTop = ta.scrollHeight;
                    }
                } catch (error) {
                    console.error("处理离开直播间消息失败:", error);
                    showError("处理离开直播间消息失败: " + error.message);
                }
            }
            // 处理创建直播间响应 (5020)
            else if(command == 5020){
                try {
                    var d = JSON.parse(msgBody);
                    console.log("创建直播间响应:", JSON.stringify(d, null, 2));

                    // 检查是否为创建成功的消息（包含action="create"和roomId）
                    if(d.data && d.data.action === "create" && d.data.roomId) {
                        let roomId = d.data.roomId;
                        let nickname = d.data.fromNickname || d.data.userId;
                        showSuccess("直播间创建成功，房间ID: " + roomId + "，主播: " + nickname);
                        // 自动设置当前房间ID
                        currentRoomId = roomId;
                        updateRoomStatus("已创建直播间: " + roomId);
                        // 自动填充房间ID到加入直播间的输入框
                        document.getElementById("roomId").value = roomId;
                    }
                    // 兼容旧的响应格式（如果有code字段）
                    else if(d.code === 200) {
                        let roomId = d.data;
                        showSuccess("直播间创建成功，房间ID: " + roomId);
                        currentRoomId = roomId;
                        updateRoomStatus("已创建直播间: " + roomId);
                        document.getElementById("roomId").value = roomId;
                    }
                    // 处理错误响应
                    else if(d.code && d.code !== 200) {
                        showError("创建直播间失败: " + (d.msg || "未知错误"));
                    }
                    // 其他情况，可能是普通的直播间消息，不做处理
                    else {
                        console.log("收到其他5020消息:", d);
                    }
                } catch (error) {
                    console.error("处理创建直播间响应失败:", error);
                    showError("处理创建直播间响应失败: " + error.message);
                }
            }
            // 处理关闭直播间响应 (5021)
            else if(command == 5021){
                try {
                    var d = JSON.parse(msgBody);
                    console.log("关闭直播间响应:", JSON.stringify(d, null, 2));

                    // 检查是否为关闭成功的消息（包含action="close"和roomId）
                    if(d.data && d.data.action === "close" && d.data.roomId) {
                        let roomId = d.data.roomId;
                        let reason = d.data.content || "直播间已关闭";
                        showSuccess("直播间关闭成功: " + reason);
                        updateRoomStatus("直播间已关闭");
                        currentRoomId = null;
                    }
                    // 兼容旧的响应格式（如果有code字段）
                    else if(d.code === 200) {
                        showSuccess("直播间关闭成功");
                        updateRoomStatus("直播间已关闭");
                        currentRoomId = null;
                    }
                    // 处理错误响应
                    else if(d.code && d.code !== 200) {
                        showError("关闭直播间失败: " + (d.msg || "未知错误"));
                    }
                    // 其他情况，可能是普通的直播间消息，不做处理
                    else {
                        console.log("收到其他5021消息:", d);
                    }
                } catch (error) {
                    console.error("处理关闭直播间响应失败:", error);
                    showError("处理关闭直播间响应失败: " + error.message);
                }
            }
            // 处理踢人响应或通知 (5015)
            else if(command == 5015){
                try {
                    var d = JSON.parse(msgBody);
                    var data = d.data || d;
                    console.log("踢人响应/通知:", JSON.stringify(data, null, 2));

                    // 判断是自己被踢还是他人被踢
                    let userId = document.getElementById('userId').value;
                    let kickedUserId = data.toId || data.userId;
                    let operatorId = data.userId || data.fromId;
                    let currentUserId = currentGuestId || userId;
                    let reason = data.kickReason || data.reason || "被管理员踢出直播间";

                    if(kickedUserId == currentUserId) {
                        showError("您已被踢出直播间: " + reason);
                        handleLeaveRoomSuccess();
                        ta.value = ta.value + "\n" + "❌ 系统：您已被踢出直播间 - " + reason;
                    } else {
                        let kickedUserName = data.toNickname || kickedUserId;
                        let operatorName = data.fromNickname || operatorId;
                        ta.value = ta.value + "\n" + "🚫 系统：" + kickedUserName + " 被 " + operatorName + " 踢出直播间 - " + reason;
                        showInfo("用户 " + kickedUserName + " 被踢出直播间");
                    }
                    ta.scrollTop = ta.scrollHeight;
                } catch (error) {
                    console.error("处理踢人消息失败:", error);
                    showError("处理踢人消息失败: " + error.message);
                }
            }
            // 处理登录成功响应
            else if(command == 9001){
                var d = JSON.parse(msgBody);
                console.log("登录成功响应:", d);
                showSuccess("登录成功");
                handleLoginSuccess();
            }
            // 处理退出指令响应 (通常服务器不会发送退出响应，连接会直接断开)
            else if(command == 9003){
                try {
                    var d = JSON.parse(msgBody);
                    console.log("退出指令响应:", d);
                    showSuccess("服务器确认退出");
                    // 注意：这里不调用handleLogoutSuccess()，因为退出指令已经在发送时处理了状态清理
                } catch (error) {
                    console.log("退出指令处理完成");
                    // 注意：这里不调用handleLogoutSuccess()，因为退出指令已经在发送时处理了状态清理
                }
            }
            // 处理系统通知
            else if(command == 9004){
                var d = JSON.parse(msgBody);
                var data = d.data || d;
                ta.value = ta.value + "\n" + "系统通知：" + (data.content || data.title || JSON.stringify(data));
            }
            else if(command == 9999){
                try {
                    var msg = JSON.parse(msgBody);

                    // 检查是否为心跳包响应
                    if (msg.timestamp) {
                        // 这是心跳包响应，计算延迟
                        var currentTime = Date.now();
                        var latency = currentTime - msg.timestamp;

                        // 只在调试模式下在控制台显示心跳包信息，不在服务器输出区域显示
                        if (debugMode) {
                            console.log("❤️ 收到心跳包响应，延迟:", latency + "ms");
                        }
                        // 移除服务器输出区域的心跳包响应信息，减少信息噪音
                    } else {
                        // 兼容旧的9999消息格式
                        if (msg["userId"] == "system") {
                            ta.value = ta.value + "\n 系统：" + msg.data;
                        } else if (msg["userId"] == userId) {
                            let msgInfo = msg.data;
                            if (typeof msgInfo === 'string') {
                                msgInfo = JSON.parse(msgInfo);
                            }
                            ta.value = ta.value + "\n" + "自己：" + msgInfo.msgBody;
                        } else {
                            let msgInfo = msg.data;
                            if (typeof msgInfo === 'string') {
                                msgInfo = JSON.parse(msgInfo);
                            }
                            ta.value = ta.value + "\n" + msg.toId + "：" + msgInfo.msgBody;
                        }
                    }
                } catch (error) {
                    console.error("处理9999消息失败:", error);
                    console.error("原始消息体:", msgBody);

                    // 兼容旧的eval方式
                    try {
                        var msg = eval("(" + msgBody + ")");
                        console.log(msg)
                        console.log(userId)

                        if (msg["userId"] == "system") {
                            ta.value = ta.value + "\n 系统：" + msg.data;
                        } else if (msg["userId"] == userId) {
                            let msgInfo = msg.data;
                            msgInfo = eval("(" + msgInfo + ")");
                            ta.value = ta.value + "\n" + "自己：" + msgInfo.msgBody;
                        } else {
                            let msgInfo = msg.data;
                            msgInfo = eval("(" + msgInfo + ")");
                            ta.value = ta.value + "\n" + msg.toId + "：" + msgInfo.msgBody;
                        }
                    } catch (evalError) {
                        console.error("使用eval处理9999消息也失败:", evalError);
                        showError("处理9999消息失败: " + error.message);
                    }
                }
            }
        }

        //连接建立的回调函数
        socket.onopen = function (event) {
            socket.binaryType = "arraybuffer";
            var ta = document.getElementById("responseText");
            ta.value = "连接开启";

            // 重置状态
            updateGuestStatus("");
            updateRoomStatus("");

            // 启动心跳保活
            startHeartbeat();
        }

        //连接断掉的回调函数
        socket.onclose = function (event) {
            var ta = document.getElementById("responseText");
            ta.value = ta.value + "\n" + "连接关闭 (code: " + event.code + ", reason: " + (event.reason || "未知原因") + ")";

            // 停止心跳保活
            stopHeartbeat();

            // 显示连接断开的详细信息
            console.log("WebSocket连接断开:", {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
                timestamp: new Date().toLocaleTimeString()
            });

            showError("WebSocket连接已断开 (code: " + event.code + ")");
        }

        } catch (error) {
            console.error("WebSocket连接失败:", error);
            showError("WebSocket连接失败: " + error.message);
        }
    }

    /**
     * 重新连接WebSocket
     */
    function reconnectWebSocket() {
        if (socket) {
            socket.close();
        }
        showInfo("正在重新连接WebSocket...");
        setTimeout(function() {
            initWebSocket();
        }, 1000);
    }

    // 页面加载时初始化WebSocket连接
    if (window.WebSocket) {
        initWebSocket();
    } else {
        alert("浏览器不支持WebSocket！");
    }

    //登录
    function login(userId, toUser,clientType,imei,appId) {
        if (!window.WebSocket) {
            return;
        }

        // 清除游客状态，切换为正式用户
        currentGuestId = null;
        isGuest = false;

        console.log(userId + " " + toUser);
        //当websocket状态打开
        if (socket.readyState == WebSocket.OPEN) {

            var command = 9000;
            var version = 1;
            if(clientType == null){
                clientType = 1;
            }
            clientType = parseInt(clientType);

            var messageType = 0x0;

            if(imei == null){
                imei = "web";
            }

            if(appId == null){
                appId = '10000'
            }
            appId = parseInt(appId);

            var userId = userId;

            var data = {"userId": userId, "appId": 10000, "clientType": clientType, "imei": imei,"customStatus":null,"customClientName":"", "nickname": ""};
            var jsonData = JSON.stringify(data);
            console.log(jsonData);

            // 修复：使用UTF-8字节长度计算，解决中文昵称问题
            var bodyLen = utf8Length(jsonData);
            var imeiLen = utf8Length(imei);
            let loginMsg = new ByteBuffer();
            loginMsg.int32(command).int32(version)
                .int32(clientType).int32(messageType)
                .int32(appId).int32(imeiLen)
                .int32(bodyLen).vstring(imei,imeiLen)
                .vstring(jsonData, bodyLen);
            socket.send(loginMsg.pack());
        } else {
            alert("连接没有开启");
        }
    }

    // *               私有协议规则，
    // *               4位表示Command表示消息的开始，
    // *               4位表示version
    // *               4位表示clientType
    // *               4位表示messageType
    // *               4位表示appId(待定)
    // *               4位表示数据长度
    // *               后续将解码方式加到数据头根据不同的解码方式解码，如pb，json，现在用json字符串
    //发消息
    function sendMsg(userId, toId, command,msg,clientType,imei,appId) {
        if (!window.WebSocket) {
            return;
        }
        console.log(msg)
        //当websocket状态打开
        if (socket.readyState == WebSocket.OPEN) {
            // debugger;
            //var command = 1103;


            // var command = 9000;
            var version = 1;
            if(clientType == null ){
                clientType = 1;
            }

            clientType = parseInt(clientType);

            var messageType = 0x0;

            var userId = userId;

            if(command == null || command == ''){
                command = 1103;
            }

            if(imei == null){
                imei = "web"
            }
            if(appId == null){
                appId = '10000'
            }
            appId = parseInt(appId);
            var data = {
                "userId": userId,
                "groupId": toId,
                "appId": appId,
                "clientType": 1,
                "imei": imei,
                "command": command
            }

            var messageId = uuid();
            if(msg === 'lld'){
                messageId = msg;
            }
            var messageData = {};
            if(command == 1103){
                messageData = {
                    "messageId": messageId,
                    "fromId": userId,
                    "toId": toId,
                    "appId": appId,
                    "clientType": clientType,
                    "imei": imei,
                    "messageBody": msg,
                    "extra": "1"
                }
            }else if (command == 2104){
                messageData = {
                    "messageId": messageId,
                    "fromId": userId,
                    "groupId": toId,
                    "appId": appId,
                    "clientType": clientType,
                    "imei": imei,
                    "messageBody": msg,
                    "extra": "1"
                }
            }else{
                messageData = JSON.parse(msg)
            }
            // data.data = messageData;

            var jsonData = JSON.stringify(messageData);
            console.log(jsonData)
            // 修复：使用准确的UTF-8字节长度计算，解决中文乱码问题
            var bodyLen = utf8Length(jsonData);
            var imeiLen = utf8Length(imei);
            let sendMsg = new ByteBuffer();
            sendMsg.int32(command).int32(version)
                .int32(clientType).int32(messageType)
                .int32(appId).int32(imeiLen)
                .int32(bodyLen).vstring(imei,imeiLen)
                .vstring(jsonData, bodyLen);
            console.log(sendMsg.pack());
            socket.send(sendMsg.pack());
        } else {
            alert("连接没有开启");
        }
    }

    function getLen(str) {
        var len = 0;
        for (var i = 0; i < str.length; i++) {
            var c = str.charCodeAt(i);
            //单字节加1
            if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                len++;
            } else {
                len += 3;
            }
        }
        return len;
    }

    // 准确计算UTF-8编码的字节长度（用于直播间消息）
    function utf8Length(str) {
        var c = 0, length = 0;
        for (var i = 0, l = str.length; i < l; i++) {
            c = str.charCodeAt(i);
            if (c < 0x80) {
                length += 1;
            } else if (c < 0x800) {
                length += 2;
            } else if (c < 0xd800 || c >= 0xe000) {
                length += 3;
            } else {
                i++;
                length += 4;
            }
        }
        return length;
    }

    // ==================== 心跳包功能函数 ====================

    /**
     * 发送心跳包
     */
    function sendHeartbeat() {
        if (!window.WebSocket || !socket || socket.readyState !== WebSocket.OPEN) {
            // 只在调试模式下显示心跳包发送失败的错误
            if (debugMode) {
                console.log("❤️ 心跳包发送失败: WebSocket未连接");
            }
            return;
        }

        var command = 9999;
        var version = 1;
        var clientType = 1;
        var messageType = 0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "timestamp": Date.now()
        };

        var jsonData = JSON.stringify(data);
        // 移除心跳包发送的详细日志，只在调试模式下在控制台显示
        if (debugMode) {
            console.log("❤️ 发送心跳包:", jsonData);
        }

        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let heartbeatMsg = new ByteBuffer();
        heartbeatMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);

        socket.send(heartbeatMsg.pack());

        // 移除服务器输出区域的心跳包发送信息，减少信息噪音
        // 心跳包发送成功不再显示任何信息
    }

    /**
     * 启动心跳定时器
     */
    function startHeartbeat() {
        // 清除现有定时器
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
        }

        // 每10秒发送一次心跳包
        heartbeatTimer = setInterval(sendHeartbeat, 10000);

        // 只在调试模式下显示心跳启动信息
        if (debugMode) {
            console.log("❤️ 心跳定时器已启动，每10秒发送一次心跳包");
            showInfo("❤️ 心跳保活已启动 (10秒间隔)");
        }
    }

    /**
     * 停止心跳定时器
     */
    function stopHeartbeat() {
        if (heartbeatTimer) {
            clearInterval(heartbeatTimer);
            heartbeatTimer = null;

            // 只在调试模式下显示心跳停止信息
            if (debugMode) {
                console.log("❤️ 心跳定时器已停止");
                showInfo("❤️ 心跳保活已停止");
            }
        }
    }

    // ==================== 退出指令功能函数 ====================

    /**
     * 发送退出指令
     * 注意：只发送退出指令到服务器，不会自动停止心跳或断开WebSocket连接
     */
    function sendLogout() {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (!socket || socket.readyState !== WebSocket.OPEN) {
            showError("WebSocket连接未开启");
            return;
        }

        try {
            var command = 9003; // LOGOUT指令码
            var version = 1;
            var clientType = parseInt(document.getElementById("clientType").value) || 1;
            var messageType = 0x0;
            var appId = parseInt(document.getElementById("appId").value) || 10000;
            var imei = document.getElementById("imei").value || "web";

            // 退出指令的数据包体为空对象
            var data = {};
            var jsonData = JSON.stringify(data);

            var bodyLen = utf8Length(jsonData);
            var imeiLen = utf8Length(imei);

            let logoutMsg = new ByteBuffer();
            logoutMsg.int32(command).int32(version)
                .int32(clientType).int32(messageType)
                .int32(appId).int32(imeiLen)
                .int32(bodyLen).vstring(imei, imeiLen)
                .vstring(jsonData, bodyLen);

            socket.send(logoutMsg.pack());

            console.log("📤 发送退出指令:", {
                command: command,
                userId: currentGuestId || document.getElementById("userId").value,
                isGuest: isGuest,
                timestamp: new Date().toLocaleTimeString()
            });

            showInfo("已发送退出指令");

            // 立即处理退出成功状态（不延迟，不关闭连接）
            handleLogoutSuccess();

        } catch (error) {
            console.error("发送退出指令失败:", error);
            showError("发送退出指令失败: " + error.message);
        }
    }

    /**
     * 处理退出成功后的清理工作
     */
    function handleLogoutSuccess() {
        try {
            // 清理状态（不停止心跳，不断开连接）
            currentGuestId = null;
            currentRoomId = null;
            isGuest = false;

            // 更新界面状态
            updateGuestStatus("");
            updateRoomStatus("");

            showSuccess("已成功退出登录");

            var ta = document.getElementById("responseText");
            ta.value = ta.value + "\n" + "🚪 已退出登录 - " + new Date().toLocaleTimeString();
            ta.scrollTop = ta.scrollHeight;

        } catch (error) {
            console.error("退出清理失败:", error);
            showError("退出清理失败: " + error.message);
        }
    }

    /**
     * 强制断开连接（不发送退出指令）
     */
    function forceDisconnect() {
        try {
            // 停止心跳
            stopHeartbeat();

            // 清理状态
            currentGuestId = null;
            currentRoomId = null;
            isGuest = false;

            // 更新界面状态
            updateGuestStatus("");
            updateRoomStatus("");

            // 直接关闭WebSocket连接
            if (socket) {
                socket.close();
                socket = null;
            }

            showInfo("已强制断开连接");

            var ta = document.getElementById("responseText");
            ta.value = ta.value + "\n" + "⚡ 强制断开连接 - " + new Date().toLocaleTimeString();
            ta.scrollTop = ta.scrollHeight;

        } catch (error) {
            console.error("强制断开失败:", error);
            showError("强制断开失败: " + error.message);
        }
    }

    // ==================== 直播间功能函数 ====================

    /**
     * 生成游客ID
     */
    function generateGuestId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substring(2, 8);
        return `guest_${timestamp}${random}`;
    }

    /**
     * 游客登录
     */
    function guestLogin(nickname) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!nickname || nickname.trim() === '') {
            showError("请输入游客昵称");
            return;
        }

        // 生成游客ID
        currentGuestId = generateGuestId();
        isGuest = true;

        // 设置界面显示
        document.getElementById("userId").value = currentGuestId;
        document.getElementById("guestNickname").value = nickname || "游客";

        var command = 9000;
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "userId": currentGuestId,
            "appId": appId,
            "clientType": clientType,
            "imei": imei,
            "isGuest": true,
            "customStatus": null,
            "customClientName": "",
            "nickname": nickname || "游客"  // 添加nickname字段支持中文昵称
        };

        var jsonData = JSON.stringify(data);
        console.log("游客登录数据:", jsonData);

        // 修复：使用UTF-8字节长度计算，解决中文昵称问题
        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let loginMsg = new ByteBuffer();
        loginMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(loginMsg.pack());

        showInfo("游客登录请求已发送: " + currentGuestId);
    }

    /**
     * 加入直播间
     */
    function joinLiveRoom(roomId, nickname) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        // 检查是否已登录（游客或正式用户）
        var userId = document.getElementById('userId').value;
        if (!currentGuestId && !userId) {
            showError("请先登录（游客登录或普通登录）");
            return;
        }

        if (!roomId || roomId.trim() === '') {
            showError("请输入直播间ID");
            return;
        }

        currentRoomId = roomId;
        var command = 5012; // LIVE_ROOM_JOIN
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        // 确定用户信息
        var finalUserId = currentGuestId || userId;
        var userNickname = nickname || document.getElementById("guestNickname").value || finalUserId || "用户";
        // 动态判断是否为游客：有游客ID就是游客
        var userIsGuest = !!currentGuestId;
        var userRole = userIsGuest ? 5 : 4; // 5-游客, 4-普通用户

        var data = {
            "roomId": roomId,
            "userId": finalUserId,
            "nickname": userNickname,
            "avatar": "",
            "role": userRole,
            "isGuest": userIsGuest
        };

        var jsonData = JSON.stringify(data);
        console.log("加入直播间数据:", jsonData);

        // 修复：使用UTF-8字节长度计算，解决中文昵称问题
        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let joinMsg = new ByteBuffer();
        joinMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(joinMsg.pack());

        showInfo("正在加入直播间: " + roomId);
    }

    /**
     * 离开直播间
     */
    function leaveLiveRoom() {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!currentRoomId) {
            showError("当前未在任何直播间中");
            return;
        }

        var command = 5013; // LIVE_ROOM_LEAVE
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "roomId": currentRoomId,
            "userId": currentGuestId || document.getElementById("userId").value,
            "isGuest": !!currentGuestId  // 动态判断是否为游客
        };

        var jsonData = JSON.stringify(data);
        console.log("离开直播间数据:", jsonData);

        // 修复：使用UTF-8字节长度计算，解决中文昵称问题
        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let leaveMsg = new ByteBuffer();
        leaveMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(leaveMsg.pack());

        showInfo("正在离开直播间: " + currentRoomId);
    }

    /**
     * 发送直播间消息
     */
    function sendLiveRoomMessage(content) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!currentRoomId) {
            showError("请先加入直播间");
            return;
        }

        if (!content || content.trim() === '') {
            showError("消息内容不能为空");
            return;
        }

        var command = 5010; // LIVE_ROOM_MSG
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        // 确定发送者信息
        var fromId = currentGuestId || document.getElementById("userId").value;
        var fromNickname = document.getElementById("guestNickname").value || fromId || "用户";
        // 动态判断是否为游客：有游客ID就是游客
        var messageIsGuest = !!currentGuestId;

        var data = {
            "messageId": uuid(),
            "roomId": currentRoomId,
            "fromId": fromId,
            "messageType": 1, // 1-文本消息
            "content": content.trim(),
            "isGuest": messageIsGuest,
            "fromNickname": fromNickname
        };

        var jsonData = JSON.stringify(data);
        console.log("📤 发送直播间消息:", jsonData);

        // 修复：使用准确的UTF-8字节长度计算
        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);

        console.log("📏 消息长度信息:", {
            jsonData: jsonData,
            charLength: jsonData.length,
            byteLength: bodyLen,
            imei: imei,
            imeiLength: imeiLen
        });

        let msgBuffer = new ByteBuffer();
        msgBuffer.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);

        console.log("📦 发送消息包:", {
            command: command,
            version: version,
            clientType: clientType,
            messageType: messageType,
            appId: appId,
            imeiLen: imeiLen,
            bodyLen: bodyLen
        });

        socket.send(msgBuffer.pack());

        // 清空输入框
        document.getElementById("liveRoomMessageInput").value = "";
    }

    /**
     * 更新游客状态显示
     */
    function updateGuestStatus(status) {
        var statusElement = document.getElementById("guestStatus");
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * 更新直播间状态显示
     */
    function updateRoomStatus(status) {
        var statusElement = document.getElementById("roomStatus");
        if (statusElement) {
            statusElement.textContent = status;
        }
    }

    /**
     * 处理登录成功响应
     */
    function handleLoginSuccess() {
        if (isGuest) {
            updateGuestStatus("游客登录成功: " + currentGuestId);
        }
    }

    /**
     * 处理加入直播间成功响应
     */
    function handleJoinRoomSuccess() {
        updateRoomStatus("已加入直播间: " + currentRoomId);
    }

    /**
     * 处理离开直播间成功响应
     */
    function handleLeaveRoomSuccess() {
        updateRoomStatus("已离开直播间");
        currentRoomId = null;
    }

    /**
     * 支持回车键发送消息
     */
    document.addEventListener('DOMContentLoaded', function() {
        // 为直播间消息输入框添加回车键监听
        var liveRoomInput = document.getElementById("liveRoomMessageInput");
        if (liveRoomInput) {
            liveRoomInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendLiveRoomMessage(this.value);
                }
            });
        }

        // 设置默认值
        document.getElementById("appId").value = "10000";
        document.getElementById("clientType").value = "1";
        document.getElementById("imei").value = "web";
    });

    /**
     * 显示错误消息
     */
    function showError(message) {
        var ta = document.getElementById("responseText");
        ta.value = ta.value + "\n" + "❌ 错误：" + message;
        ta.scrollTop = ta.scrollHeight;
    }

    /**
     * 显示成功消息
     */
    function showSuccess(message) {
        var ta = document.getElementById("responseText");
        ta.value = ta.value + "\n" + "✅ " + message;
        ta.scrollTop = ta.scrollHeight;
    }

    /**
     * 显示信息消息
     */
    function showInfo(message) {
        var ta = document.getElementById("responseText");
        ta.value = ta.value + "\n" + "ℹ️ " + message;
        ta.scrollTop = ta.scrollHeight;
    }

    /**
     * 切换调试模式
     */
    function toggleDebugMode(enabled) {
        debugMode = enabled;
        if (enabled) {
            showInfo("调试模式已启用");
        } else {
            showInfo("调试模式已关闭");
        }
    }

    /**
     * 测试WebSocket连接
     */
    function testWebSocketConnection() {
        if (!socket) {
            showError("WebSocket对象不存在");
            return;
        }

        let status = "";
        switch(socket.readyState) {
            case WebSocket.CONNECTING:
                status = "正在连接";
                break;
            case WebSocket.OPEN:
                status = "已连接";
                break;
            case WebSocket.CLOSING:
                status = "正在关闭";
                break;
            case WebSocket.CLOSED:
                status = "已关闭";
                break;
            default:
                status = "未知状态";
        }

        showInfo("WebSocket状态: " + status + " (readyState=" + socket.readyState + ")");
    }

    /**
     * 创建直播间 (5020)
     */
    function createLiveRoom(roomId, roomName, anchorId) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!roomName || roomName.trim() === '') {
            showError("请输入直播间名称");
            return;
        }

        if (!anchorId || anchorId.trim() === '') {
            showError("请输入主播ID");
            return;
        }

        var command = 5020; // LIVE_ROOM_CREATE
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "roomName": roomName.trim(),
            "anchorId": anchorId.trim(),
            "roomCover": "https://example.com/cover.jpg",
            "announcement": "欢迎来到" + roomName.trim(),
            "muteAll": "0",
            "needReview": "0",
            "maxOnlineCount": 1000,
            "appId": appId
        };

        // 如果提供了roomId，则添加到数据中
        if (roomId && roomId.trim() !== '') {
            data.roomId = roomId.trim();
            showInfo("正在创建指定ID的直播间: " + roomId.trim() + " (" + roomName + ")");
        } else {
            showInfo("正在创建直播间: " + roomName + " (系统自动生成ID)");
        }

        var jsonData = JSON.stringify(data);
        console.log("创建直播间数据:", jsonData);

        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let createMsg = new ByteBuffer();
        createMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(createMsg.pack());
    }

    /**
     * 关闭直播间 (5021)
     */
    function closeLiveRoom(roomId, operater, reason) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!roomId || roomId.trim() === '') {
            showError("请输入直播间ID");
            return;
        }

        if (!operater || operater.trim() === '') {
            showError("请输入操作者ID");
            return;
        }

        var command = 5021; // LIVE_ROOM_CLOSE
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "roomId": roomId.trim(),
            "operater": operater.trim(),
            "reason": reason || "直播结束"
        };

        var jsonData = JSON.stringify(data);
        console.log("关闭直播间数据:", jsonData);

        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let closeMsg = new ByteBuffer();
        closeMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(closeMsg.pack());

        showInfo("正在关闭直播间: " + roomId);
    }

    /**
     * 踢出用户 (5015)
     */
    function kickUser(roomId, targetUserId, operater, reason) {
        if (!window.WebSocket) {
            showError("浏览器不支持WebSocket");
            return;
        }

        if (socket.readyState != WebSocket.OPEN) {
            showError("WebSocket连接未开启，请先建立连接");
            return;
        }

        if (!roomId || roomId.trim() === '') {
            showError("请输入直播间ID");
            return;
        }

        if (!targetUserId || targetUserId.trim() === '') {
            showError("请输入被踢用户ID");
            return;
        }

        if (!operater || operater.trim() === '') {
            showError("请输入操作者ID");
            return;
        }

        var command = 5015; // LIVE_ROOM_KICK
        var version = 1;
        var clientType = 1;
        var messageType = 0x0;
        var appId = 10000;
        var imei = "web";

        var data = {
            "roomId": roomId.trim(),
            "userId": targetUserId.trim(),
            "operater": operater.trim(),
            "reason": reason || "违反直播间规则"
        };

        var jsonData = JSON.stringify(data);
        console.log("踢人操作数据:", jsonData);

        var bodyLen = utf8Length(jsonData);
        var imeiLen = utf8Length(imei);
        let kickMsg = new ByteBuffer();
        kickMsg.int32(command).int32(version)
            .int32(clientType).int32(messageType)
            .int32(appId).int32(imeiLen)
            .int32(bodyLen).vstring(imei, imeiLen)
            .vstring(jsonData, bodyLen);
        socket.send(kickMsg.pack());

        showInfo("正在踢出用户: " + targetUserId + " (原因: " + (reason || "违反直播间规则") + ")");
    }

    /**
     * 自动填充当前房间ID到踢人表单
     */
    function autoFillKickRoomId() {
        if (currentRoomId) {
            document.getElementById("kickRoomId").value = currentRoomId;
            showInfo("已自动填充当前房间ID: " + currentRoomId);
        } else {
            showError("当前未在任何直播间中");
        }
    }

    /**
     * 自动填充当前用户ID到操作者表单
     */
    function autoFillKickOperater() {
        var currentUserId = currentGuestId || document.getElementById("userId").value;
        if (currentUserId) {
            document.getElementById("kickOperater").value = currentUserId;
            showInfo("已自动填充操作者ID: " + currentUserId);
        } else {
            showError("请先登录");
        }
    }

    /**
     * 显示连接信息
     */
    function showConnectionInfo() {
        let info = [];
        info.push("配置的WebSocket地址: " + getWebSocketUrl());
        info.push("实际连接状态: " + (socket ? (socket.readyState === WebSocket.OPEN ? "已连接" : "连接中/已断开") : "未连接"));
        info.push("连接URL: " + (socket ? socket.url : "未连接"));
        info.push("当前用户ID: " + (document.getElementById('userId').value || "未设置"));
        info.push("游客ID: " + (currentGuestId || "未设置"));
        info.push("游客模式: " + (isGuest ? "是" : "否"));
        info.push("当前直播间: " + (currentRoomId || "未加入"));
        info.push("调试模式: " + (debugMode ? "开启" : "关闭"));

        showInfo("连接信息:\n" + info.join("\n"));
    }

    /**
     * 导出日志
     */
    function exportLogs() {
        var logs = document.getElementById("responseText").value;
        var blob = new Blob([logs], { type: 'text/plain' });
        var url = window.URL.createObjectURL(blob);
        var a = document.createElement('a');
        a.href = url;
        a.download = 'websocket_logs_' + new Date().getTime() + '.txt';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        showInfo("日志已导出");
    }



    /**
     * 监控WebSocket消息接收
     */
    function startMessageMonitoring() {
        if (!socket) {
            showError("WebSocket未连接");
            return;
        }

        // 记录原始的onmessage处理器
        var originalOnMessage = socket.onmessage;

        // 包装onmessage处理器，添加监控
        socket.onmessage = function(event) {
            console.log("🔍 WebSocket消息监控:", {
                timestamp: new Date().toISOString(),
                dataType: typeof event.data,
                dataSize: event.data.byteLength || event.data.length,
                data: event.data
            });

            // 调用原始处理器
            if (originalOnMessage) {
                originalOnMessage.call(this, event);
            }
        };

        showInfo("WebSocket消息监控已启动");
    }




</script>
    <form onsubmit="return false">
        <!-- 基础连接配置 -->
        <div class="section">
            <h3>基础连接配置</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>WebSocket地址:</label>
                    <textarea placeholder="WebSocket服务器地址" id="websocketUrl" name="websocketUrl" style="width: 300px;height: 30px">wss://www.ano999.com/gateway-api/ws</textarea>
                    <div class="button-group" style="margin-left: 10px;">
                        <input type="button" value="生产环境" onclick="setWebSocketUrl('wss://www.ano999.com/gateway-api/ws')" style="background-color: #28a745; color: white; font-size: 12px; padding: 4px 8px;">
                        <input type="button" value="本地环境" onclick="setWebSocketUrl('ws://localhost:19000/ws')" style="background-color: #17a2b8; color: white; font-size: 12px; padding: 4px 8px;">
                        <input type="button" value="重新连接" onclick="reconnectWebSocket()" style="background-color: #fd7e14; color: white; font-size: 12px; padding: 4px 8px;">
                    </div>
                </div>
            </div>
            <div style="font-size: 12px; color: #888; margin-top: 5px; padding: 5px; background-color: #f8f9fa; border-radius: 4px;">
                🔗 <strong>地址格式</strong>：支持 <code>ws://</code> (非加密) 和 <code>wss://</code> (SSL加密) 协议
                <span style="margin-left: 15px;">💡 修改地址后点击"重新连接"生效</span>
            </div>
           <!-- <div style="font-size: 12px; color: #666; margin-top: 8px; padding: 8px; background-color: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
                🚪 <strong>退出指令说明</strong>：<br>
                • <strong>发送退出指令</strong>：发送LOGOUT指令(9003)到服务器，服务器会清理用户Session和Redis数据，然后断开连接<br>
                • <strong>强制断开</strong>：直接关闭WebSocket连接，不发送退出指令，服务器会在连接断开时自动清理<br>
                • <strong>适用场景</strong>：正式用户和游客用户都可以使用退出指令，服务器会根据用户类型执行不同的清理逻辑<br>
                • <strong>自动清理</strong>：退出后会自动停止心跳、清理本地状态、从直播间移除并通知其他用户
            </div>-->
            <div class="form-row">
                <div class="form-group">
                    <label>用户ID:</label>
                    <textarea placeholder="输入登录id" id="userId" name="userId" style="width: 120px;height: 30px"></textarea>
                </div>
                <div class="form-group">
                    <label>接收者:</label>
                    <textarea placeholder="接受者ID或群组ID" name="toUser" style="width: 120px;height: 30px"></textarea>
                </div>
                <div class="form-group">
                    <label>AppID:</label>
                    <textarea placeholder="appId" id="appId" name="appId" style="width: 80px;height: 30px"></textarea>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>客户端类型:</label>
                    <textarea placeholder="clientType" id="clientType" name="clientType" style="width: 80px;height: 30px"></textarea>
                </div>
                <div class="form-group">
                    <label>设备标识:</label>
                    <textarea placeholder="imei" id="imei" name="imei" style="width: 100px;height: 30px"></textarea>
                </div>
                <div class="button-group">
                    <input type="button" value="正式用户登录" onclick="login(this.form.userId.value,this.form.toUser.value,this.form.clientType.value
                    ,this.form.imei.value,this.form.appId.value);" style="background-color: #6c757d; color: white;">
                    <input type="button" value="发送退出指令" onclick="sendLogout();" style="background-color: #dc3545; color: white;" title="发送LOGOUT指令(9003)，仅清理服务器状态，不断开连接">
                    <input type="button" value="强制断开" onclick="forceDisconnect();" style="background-color: #6f42c1; color: white;" title="直接断开连接，不发送退出指令">
                </div>
            </div>
        </div>

        <!-- 普通消息发送区域 -->
        <div class="section message-section">
            <h3>📨 普通消息发送</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>命令:</label>
                    <textarea placeholder="群聊:2104, 私聊:1103(默认)" name="command" style="width: 250px;height: 30px"></textarea>
                    <span style="font-size: 12px; color: #666; margin-left: 5px;">💡 空白时默认私聊(1103)</span>
                </div>
                <div class="form-group">
                    <label>消息内容:</label>
                    <textarea placeholder="输入要发送的内容" name="message" style="width: 250px;height: 30px"></textarea>
                </div>
                <div class="button-group">
                    <input type="button" value="发送数据"
                           onclick="sendMsg(this.form.userId.value,this.form.toUser.value,this.form.command.value,this.form.message.value
                           ,this.form.clientType.value,this.form.imei.value,this.form.appId.value);" style="background-color: #ffc107; color: #212529;">
                </div>
            </div>
            <div style="font-size: 12px; color: #888; margin-top: 8px; padding: 8px; background-color: #f8f9fa; border-radius: 4px;">
                📋 <strong>命令说明</strong>：
                <span style="margin-left: 10px;"><strong>1103</strong> - 私聊消息（默认）</span>
                <span style="margin-left: 15px;"><strong>2104</strong> - 群聊消息</span>
                <span style="font-size: 12px; color: #666; margin-left: 5px;">💡 发送消息前，请先登录</span>

            </div>
        </div>

        <!-- 游客登录区域 -->
        <div class="section guest-section">
            <h3>🎭 游客登录</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>游客昵称:</label>
                    <textarea placeholder="游客昵称" id="guestNickname" name="guestNickname" style="width: 150px;height: 30px"></textarea>
                </div>
                <div class="button-group">
                    <input type="button" value="游客登录" onclick="guestLogin(this.form.guestNickname.value);" style="background-color: #007bff; color: white;">
                    <input type="button" value="游客退出" onclick="sendLogout();" style="background-color: #dc3545; color: white;" title="游客发送退出指令，仅清理服务器状态，不断开连接">
                </div>
                <span id="guestStatus" class="status-text"></span>
            </div>
        </div>

        <!-- 直播间操作区域 -->
        <div class="section liveroom-section">
            <h3>🎬 直播间操作</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>直播间ID:</label>
                    <textarea placeholder="直播间ID" id="roomId" name="roomId" style="width: 150px;height: 30px"></textarea>
                </div>
                <div class="button-group">
                    <input type="button" value="加入直播间" onclick="joinLiveRoom(this.form.roomId.value, this.form.guestNickname.value);" style="background-color: #28a745; color: white;">
                    <input type="button" value="离开直播间" onclick="leaveLiveRoom();" style="background-color: #dc3545; color: white;">
                </div>
                <span id="roomStatus" class="status-text">已加入直播间</span>
            </div>
            <div class="form-row">
                <div class="input-group">
                    <textarea placeholder="输入直播间消息..." id="liveRoomMessageInput" name="liveRoomMessageInput" style="width: 300px;height: 30px"></textarea>
                    <input type="button" value="发送消息" onclick="sendLiveRoomMessage(this.form.liveRoomMessageInput.value);" style="background-color: #17a2b8; color: white;">
                </div>
            </div>

            <!-- 直播间管理功能 -->
            <div style="border-top: 1px solid #ddd; margin-top: 15px; padding-top: 15px;">
                <h4 style="color: #dc3545; margin-bottom: 10px;">🎛️ 直播间管理功能</h4>

                <!-- 创建直播间 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>直播间ID:</label>
                        <textarea placeholder="可选，不填则自动生成" id="createRoomId" name="createRoomId" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="form-group">
                        <label>直播间名称:</label>
                        <textarea placeholder="输入直播间名称" id="createRoomName" name="createRoomName" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="form-group">
                        <label>主播ID:</label>
                        <textarea placeholder="输入主播用户ID" id="createAnchorId" name="createAnchorId" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="button-group">
                        <input type="button" value="创建直播间 (5020)" onclick="createLiveRoom(this.form.createRoomId.value, this.form.createRoomName.value, this.form.createAnchorId.value);" style="background-color: #007bff; color: white;">
                    </div>
                </div>

                <!-- 关闭直播间 -->
                <div class="form-row">
                    <div class="form-group">
                        <label>直播间ID:</label>
                        <textarea placeholder="输入要关闭的直播间ID" id="closeRoomId" name="closeRoomId" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="form-group">
                        <label>操作者ID:</label>
                        <textarea placeholder="输入操作者用户ID" id="closeOperater" name="closeOperater" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="form-group">
                        <label>关闭原因:</label>
                        <textarea placeholder="输入关闭原因(可选)" id="closeReason" name="closeReason" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="button-group">
                        <input type="button" value="关闭直播间 (5021)" onclick="closeLiveRoom(this.form.closeRoomId.value, this.form.closeOperater.value, this.form.closeReason.value);" style="background-color: #dc3545; color: white;">
                    </div>
                </div>

                <!-- 踢出用户 -->
                <div class="form-row" style="border-top: 1px solid #eee; padding-top: 10px; margin-top: 10px;">
                    <div class="form-group">
                        <label>直播间ID:</label>
                        <textarea placeholder="输入直播间ID" id="kickRoomId" name="kickRoomId" style="width: 150px;height: 30px"></textarea>
                        <input type="button" value="自动填充" onclick="autoFillKickRoomId();" style="background-color: #6c757d; color: white; font-size: 12px; padding: 4px 8px;">
                    </div>
                    <div class="form-group">
                        <label>被踢用户ID:</label>
                        <textarea placeholder="输入被踢用户ID" id="kickTargetUserId" name="kickTargetUserId" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="form-group">
                        <label>操作者ID:</label>
                        <textarea placeholder="输入操作者用户ID" id="kickOperater" name="kickOperater" style="width: 150px;height: 30px"></textarea>
                        <input type="button" value="自动填充" onclick="autoFillKickOperater();" style="background-color: #6c757d; color: white; font-size: 12px; padding: 4px 8px;">
                    </div>
                    <div class="form-group">
                        <label>踢人原因:</label>
                        <textarea placeholder="输入踢人原因(可选)" id="kickReason" name="kickReason" style="width: 150px;height: 30px"></textarea>
                    </div>
                    <div class="button-group">
                        <input type="button" value="踢出用户 (5015)" onclick="kickUser(this.form.kickRoomId.value, this.form.kickTargetUserId.value, this.form.kickOperater.value, this.form.kickReason.value);" style="background-color: #fd7e14; color: white;">
                    </div>
                </div>

                <div style="font-size: 12px; color: #666; margin-top: 10px;">
                    💡 提示：<br>
                    • <strong>创建直播间</strong>：直播间ID可选，不填则系统自动生成唯一ID<br>
                    • 创建成功后，房间ID会自动填充到"加入直播间"的输入框中<br>
                    • 自定义ID便于记忆和管理，但需确保唯一性<br>
                    • <strong>踢人操作</strong>：需要管理员或主播权限，被踢用户会收到踢人通知并自动离开直播间<br>
                    • 点击"自动填充"按钮可快速填入当前房间ID和操作者ID<br>
                    • <strong>测试流程</strong>：创建直播间 → 多用户加入 → 管理员踢人 → 观察消息通知
                </div>
            </div>
        </div>

        <!-- 调试控制区域 -->
        <div class="section" style="border-color: #6c757d; background-color: #f8f9fa;">
            <h3>🔧 调试控制</h3>
            <div class="form-row">
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="debugMode" checked onchange="toggleDebugMode(this.checked);">
                        启用调试模式（显示详细日志）
                    </label>
                </div>
                <div class="button-group">
                    <input type="button" onclick="testWebSocketConnection()" value="测试连接" style="background-color: #17a2b8; color: white;">
                    <input type="button" onclick="showConnectionInfo()" value="连接信息" style="background-color: #28a745; color: white;">
                    <input type="button" onclick="startMessageMonitoring()" value="消息监控" style="background-color: #fd7e14; color: white;">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>❤️ 心跳保活控制：</label>
                </div>
                <div class="button-group">
                    <input type="button" onclick="sendHeartbeat()" value="发送心跳包" style="background-color: #e83e8c; color: white;">
                    <input type="button" onclick="startHeartbeat()" value="启动心跳" style="background-color: #20c997; color: white;">
                    <input type="button" onclick="stopHeartbeat()" value="停止心跳" style="background-color: #6c757d; color: white;">
                </div>
            </div>
        </div>

        <h3>📋 服务器输出：</h3>
        <textarea id="responseText"></textarea>
        <div class="form-row" style="margin-top: 10px;">
            <div class="button-group">
                <input type="button" onclick="javascript:document.getElementById('responseText').value=''" value="清空数据" style="background-color: #6c757d; color: white;">
                <input type="button" onclick="exportLogs()" value="导出日志" style="background-color: #007bff; color: white;">
            </div>
        </div>
    </form>
</div>
</body>
</html>