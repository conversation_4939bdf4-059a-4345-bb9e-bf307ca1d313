package com.lld.im.service.config;

import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;

import java.util.Arrays;
import java.util.Locale;

/**
 * 国际化配置类
 * 
 * @description: 配置Spring Boot国际化支持，包括消息源、语言解析器等
 * @author: IM System
 * @version: 1.0
 */
@Configuration
public class I18nConfig implements WebMvcConfigurer {

    /**
     * 配置消息源，支持多语言资源文件
     * 
     * @return MessageSource实例
     */
    @Bean
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = 
            new ReloadableResourceBundleMessageSource();
        
        // 设置资源文件基础名称
        messageSource.setBasenames(
            "classpath:i18n/messages",  // 通用消息
            "classpath:i18n/errors"     // 错误消息
        );
        
        // 设置编码格式
        messageSource.setDefaultEncoding("UTF-8");
        
        // 设置缓存时间（秒）
        // -1: 永久缓存（生产环境推荐，性能最佳）
        // 0: 不缓存（开发环境调试，实时性最好）
        // >0: 定时缓存（平衡性能和实时性）
        // 缓存到期后会自动检查文件修改时间，如有变化则重新加载
        messageSource.setCacheSeconds(-1);
        
        // 设置当找不到消息时是否使用消息键作为默认值
        messageSource.setUseCodeAsDefaultMessage(true);
        
        // 设置是否回退到系统默认语言环境
        messageSource.setFallbackToSystemLocale(false);
        
        return messageSource;
    }

    /**
     * 配置语言环境解析器
     * 基于HTTP请求头Accept-Language来确定用户的语言环境
     * 
     * @return LocaleResolver实例
     */
    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        
        // 设置默认语言环境为中文简体
        resolver.setDefaultLocale(Locale.SIMPLIFIED_CHINESE);
        
        // 设置支持的语言环境列表
        resolver.setSupportedLocales(Arrays.asList(
            Locale.SIMPLIFIED_CHINESE,  // zh-CN 中文简体
            Locale.ENGLISH,             // en 英文
            new Locale("vi", "VI")      // vi-VI 越南语
        ));
        
        return resolver;
    }

    /**
     * 注册拦截器
     * 
     * @param registry 拦截器注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册语言切换拦截器
        registry.addInterceptor(new LocaleInterceptor())
                .addPathPatterns("/v1/**")  // 拦截所有API路径
                .excludePathPatterns("/v1/health", "/v1/actuator/**"); // 排除健康检查等路径
    }
}
