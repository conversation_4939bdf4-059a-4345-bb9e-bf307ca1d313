package com.lld.im.service.config;

import com.lld.im.common.constant.Constants;
import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RabbitMQ配置类
 * 专门用于配置系统通知相关的交换机、队列和绑定关系
 */
@Configuration
public class RabbitMQConfig {

    /**
     * 系统通知交换机
     */
    @Bean
    public DirectExchange systemNotificationExchange() {
        return new DirectExchange(Constants.RabbitConstants.SystemNotification, true, false);
    }

    /**
     * 广播消息交换机
     */
    @Bean
    public DirectExchange broadcastMessageExchange() {
        return new DirectExchange(Constants.RabbitConstants.BroadcastMessage, true, false);
    }

    /**
     * 普通用户系统通知队列
     */
    @Bean
    public Queue normalUserNotificationQueue() {
        return QueueBuilder.durable(Constants.RabbitConstants.SystemNotification + ".normal")
                .build();
    }

    /**
     * 游客系统通知队列
     */
    @Bean
    public Queue guestUserNotificationQueue() {
        return QueueBuilder.durable(Constants.RabbitConstants.SystemNotification + ".guest")
                .build();
    }

    /**
     * 所有用户广播消息队列
     */
    @Bean
    public Queue allUserBroadcastQueue() {
        return QueueBuilder.durable(Constants.RabbitConstants.BroadcastMessage + ".all")
                .build();
    }

    /**
     * 将普通用户系统通知队列绑定到系统通知交换机
     */
    @Bean
    public Binding normalUserNotificationBinding() {
        return BindingBuilder.bind(normalUserNotificationQueue())
                .to(systemNotificationExchange())
                .with(Constants.RabbitConstants.NormalUserRoutingKey);
    }

    /**
     * 将游客系统通知队列绑定到系统通知交换机
     */
    @Bean
    public Binding guestUserNotificationBinding() {
        return BindingBuilder.bind(guestUserNotificationQueue())
                .to(systemNotificationExchange())
                .with(Constants.RabbitConstants.GuestUserRoutingKey);
    }

    /**
     * 将所有用户广播消息队列绑定到广播消息交换机
     */
    @Bean
    public Binding allUserBroadcastBinding() {
        return BindingBuilder.bind(allUserBroadcastQueue())
                .to(broadcastMessageExchange())
                .with(Constants.RabbitConstants.AllUserRoutingKey);
    }
} 