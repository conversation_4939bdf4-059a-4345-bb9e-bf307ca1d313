package com.lld.im.service.interceptor;

import com.lld.im.common.exception.PermissionDeniedException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

@Aspect
@Component
public class UserPermissionAspect {

    @Around("@annotation(com.lld.im.service.interceptor.UserPermissionCheck)")
    public Object checkUserPermission(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        UserPermissionCheck annotation = method.getAnnotation(UserPermissionCheck.class);
        String[] paramNamesToCheck = annotation.value();

        String paramUserId = null;
        String[] paramNames = signature.getParameterNames();
        Object[] args = joinPoint.getArgs();

        // 1. 先直接查找参数名
        outer:
        for (String nameToCheck : paramNamesToCheck) {
            for (int i = 0; i < paramNames.length; i++) {
                if (nameToCheck.equals(paramNames[i]) && args[i] != null) {
                    paramUserId = args[i].toString();
                    break outer;
                }
            }
        }

        // 2. 如果没找到，查找参数对象的getXxx方法
        if (paramUserId == null) {
            for (Object arg : args) {
                if (arg == null) continue;
                Class<?> clazz = arg.getClass();
                for (String nameToCheck : paramNamesToCheck) {
                    try {
                        Method getter = clazz.getMethod("get" + Character.toUpperCase(nameToCheck.charAt(0)) + nameToCheck.substring(1));
                        Object value = getter.invoke(arg);
                        if (value != null) {
                            paramUserId = value.toString();
                            break;
                        }
                    } catch (Exception ignored) {}
                }
                if (paramUserId != null) break;
            }
        }

        String currentUser = GateWayInterceptor.getCurrentUser();
        if (paramUserId == null || !paramUserId.equals(currentUser)) {
            throw new PermissionDeniedException(403,"无权操作他人数据");
        }

        return joinPoint.proceed();
    }
} 