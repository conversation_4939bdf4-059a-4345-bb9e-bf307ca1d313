package com.lld.im.service.liveroom.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

/**
 * 直播间成员实体类
 */
@Data
@TableName("im_live_room_member")
public class LiveRoomMember {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户角色 1-主播 2-管理员 3-VIP用户 4-普通用户 5-游客
     */
    private Integer role;

    /**
     * 是否被禁言 0-否 1-是
     */
    private Integer mute;

    /**
     * 禁言到期时间
     */
    private Date muteEndTime;

    /**
     * 应用ID
     */
    private Integer appId;

    /**
     * 进入时间
     */
    private Date joinTime;

    /**
     * 最后活跃时间
     */
    private Date lastActiveTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 用户昵称
     */
    @TableField("nickname")
    private String nickname;

    /**
     * 用户头像
     */
    @TableField("avatar")
    private String avatar;
} 