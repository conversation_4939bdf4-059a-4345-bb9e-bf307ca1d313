package com.lld.im.service.utils;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.pack.group.*;
import com.lld.im.common.ClientType;
import com.lld.im.common.enums.command.Command;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.service.group.model.req.GroupMemberDto;
import com.lld.im.service.group.service.ImGroupMemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.ArrayList;
import java.util.Objects;

/**
 * 群组消息分发器
 * 负责向群组成员发送各类群组事件通知
 *
 * @description: 群组消息分发器，支持智能分发策略，避免操作者重复收到通知
 * @author: lld
 * @version: 1.0
 */
@Component
public class GroupMessageProducer {

    private static final Logger logger = LoggerFactory.getLogger(GroupMessageProducer.class);

    @Autowired
    private MessageProducer messageProducer;

    @Autowired
    private ImGroupMemberService imGroupMemberService;

    /**
     * 群组消息分发入口
     *
     * @param userId 操作者用户ID
     * @param command 群组事件命令
     * @param data 消息数据
     * @param clientInfo 客户端信息
     */
    public void producer(String userId, Command command, Object data, ClientInfo clientInfo) {
        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);
        String groupId = dataJson.getString("groupId");

        logger.debug("开始分发群组消息: groupId={}, command={}, operatorId={}",
                    groupId, command.getCommand(), userId);

        // 根据不同的群组事件类型，采用不同的分发策略
        switch (command.getCommand()) {
            case 2001: // ADDED_MEMBER - 添加群成员
                handleAddedMember(userId, command, data, clientInfo, groupId, dataJson);
                break;
            case 2006: // DELETED_MEMBER - 删除群成员
                handleDeletedMember(userId, command, data, clientInfo, groupId, dataJson);
                break;
            case 2005: // UPDATED_MEMBER - 更新群成员
                handleUpdatedMember(userId, command, data, clientInfo, groupId, dataJson);
                break;
            case 2011: // APPLY_JOIN_GROUP - 申请加群
                handleApplyJoinGroup(userId, command, data, clientInfo, groupId);
                break;
            case 2012: // APPROVE_GROUP_APPLY - 审批申请
                handleApproveGroupApply(userId, command, data, clientInfo, dataJson);
                break;
            case 2004: // EXIT_GROUP - 退出群组
                handleExitGroup(userId, command, data, clientInfo, groupId, dataJson);
                break;
            default:
                // 默认分发策略：发送给所有群成员
                handleDefaultGroupNotification(userId, command, data, clientInfo, groupId);
                break;
        }

        logger.debug("群组消息分发完成: groupId={}, command={}", groupId, command.getCommand());
    }

    /**
     * 处理添加群成员通知
     * 通知范围：群主+管理员 + 所有成员
     */
    private void handleAddedMember(String userId, Command command, Object data,
                                 ClientInfo clientInfo, String groupId, JSONObject dataJson) {
        logger.debug("处理添加群成员通知: groupId={}, operatorId={}", groupId, userId);

        // 获取所有成员列表
        List<String> groupManagers = imGroupMemberService.getGroupMemberId(groupId, clientInfo.getAppId());
        // 通知所有成员
        sendToUserList(groupManagers, userId, command, data, clientInfo);
    }

    /**
     * 处理删除群成员通知
     * 通知范围：所有群成员 + 被删除成员
     */
    private void handleDeletedMember(String userId, Command command, Object data,
                                   ClientInfo clientInfo, String groupId, JSONObject dataJson) {
        logger.debug("处理删除群成员通知: groupId={}, operatorId={}", groupId, userId);

        RemoveGroupMemberPack pack = dataJson.toJavaObject(RemoveGroupMemberPack.class);
        String removedMember = pack.getMember();

        // 获取所有群成员
        List<String> allMembers = imGroupMemberService.getGroupMemberId(groupId, clientInfo.getAppId());
        // 添加被删除的成员（确保被删除的成员也能收到通知）
        allMembers.add(removedMember);

        sendToUserList(allMembers, userId, command, data, clientInfo);
    }

    /**
     * 处理更新群成员通知
     * 通知范围：群主+管理员 + 被更新成员
     */
    private void handleUpdatedMember(String userId, Command command, Object data,
                                   ClientInfo clientInfo, String groupId, JSONObject dataJson) {
        logger.debug("处理更新群成员通知: groupId={}, operatorId={}", groupId, userId);

        UpdateGroupMemberPack pack = dataJson.toJavaObject(UpdateGroupMemberPack.class);
        String updatedMemberId = pack.getMemberId();

        // 获取管理员列表
        List<GroupMemberDto> groupManagers = imGroupMemberService.getGroupManager(groupId, clientInfo.getAppId());
        List<String> targetUsers = extractMemberIds(groupManagers);

        // 确保被更新的成员也在通知列表中
        if (!targetUsers.contains(updatedMemberId)) {
            targetUsers.add(updatedMemberId);
        }

        sendToUserList(targetUsers, userId, command, data, clientInfo);
    }

    /**
     * 处理申请加群通知
     * 通知范围：群主和管理员
     */
    private void handleApplyJoinGroup(String userId, Command command, Object data,
                                    ClientInfo clientInfo, String groupId) {
        logger.debug("处理申请加群通知: groupId={}, applicantId={}", groupId, userId);

        // 获取管理员列表
        List<GroupMemberDto> groupManagers = imGroupMemberService.getGroupManager(groupId, clientInfo.getAppId());
        sendToUserList(extractMemberIds(groupManagers), userId, command, data, clientInfo);
    }

    /**
     * 处理审批申请结果通知
     * 通知范围：申请人
     */
    private void handleApproveGroupApply(String userId, Command command, Object data,
                                       ClientInfo clientInfo, JSONObject dataJson) {
        ApproveGroupApplyPack pack = dataJson.toJavaObject(ApproveGroupApplyPack.class);
        String applicantId = pack.getApplicantId();

        logger.debug("处理审批申请结果通知: applicantId={}, operatorId={}", applicantId, userId);

        // 只通知申请人
        sendToSingleUser(applicantId, userId, command, data, clientInfo);
    }

    /**
     * 处理退出群组通知
     * 通知范围：所有群成员（包括退出者本人的所有设备）
     * 特殊处理：退出者本人的所有设备都需要收到通知以更新本地数据
     */
    private void handleExitGroup(String userId, Command command, Object data,
                               ClientInfo clientInfo, String groupId, JSONObject dataJson) {
        logger.debug("处理退出群组通知: groupId={}, exitUserId={}", groupId, userId);

        ExitGroupPack pack = dataJson.toJavaObject(ExitGroupPack.class);
        String removedMember = pack.getMemberId();

        // 获取所有群成员
        List<String> groupMemberIds = imGroupMemberService.getGroupMemberId(groupId, clientInfo.getAppId());
        groupMemberIds.add(removedMember);
        // 对于退出群组事件，所有用户（包括退出者本人）的所有设备都需要收到通知
        // 这样可以确保本地数据同步
        for (String memberId : groupMemberIds) {
            // 退出者本人：发送到所有设备（包括当前操作设备）
            // 其他成员：发送到所有设备
            messageProducer.sendToUser(memberId, command, data, clientInfo.getAppId());
            logger.debug("向用户发送退出群组通知(所有设备): userId={}, command={}", memberId, command.getCommand());
        }
    }

    /**
     * 处理默认群组通知
     * 通知范围：所有群成员
     */
    private void handleDefaultGroupNotification(String userId, Command command, Object data,
                                              ClientInfo clientInfo, String groupId) {
        logger.debug("处理默认群组通知: groupId={}, command={}, operatorId={}",
                    groupId, command.getCommand(), userId);

        List<String> groupMemberIds = imGroupMemberService.getGroupMemberId(groupId, clientInfo.getAppId());
        sendToUserList(groupMemberIds, userId, command, data, clientInfo);
    }

    /**
     * 向用户列表发送消息
     *
     * @param targetUsers 目标用户ID列表
     * @param operatorId 操作者ID
     * @param command 命令
     * @param data 数据
     * @param clientInfo 客户端信息
     */
    private void sendToUserList(List<String> targetUsers, String operatorId, Command command,
                               Object data, ClientInfo clientInfo) {
        for (String targetUserId : targetUsers) {
            sendToSingleUser(targetUserId, operatorId, command, data, clientInfo);
        }
    }

    /**
     * 向单个用户发送消息
     * 核心逻辑：如果是操作者本人且非WEBAPI调用，则排除当前客户端，避免重复通知
     *
     * @param targetUserId 目标用户ID
     * @param operatorId 操作者ID
     * @param command 命令
     * @param data 数据
     * @param clientInfo 客户端信息
     */
    private void sendToSingleUser(String targetUserId, String operatorId, Command command,
                                 Object data, ClientInfo clientInfo) {
 /*       if (shouldExcludeCurrentClient(targetUserId, operatorId, clientInfo)) {
            // 操作者本人：排除当前操作的客户端，避免重复通知
            messageProducer.sendToUserExceptClient(targetUserId, command, data, clientInfo);
            logger.debug("向用户发送消息(排除当前客户端): userId={}, command={}", targetUserId, command.getCommand());
        } else {

        }*/
        // 其他用户或WEBAPI操作：发送到所有在线设备
        messageProducer.sendToUser(targetUserId, command, data, clientInfo.getAppId());
        logger.debug("向用户发送消息(所有设备): userId={}, command={}", targetUserId, command.getCommand());
    }

    /**
     * 判断是否应该排除当前客户端
     *
     * @param targetUserId 目标用户ID
     * @param operatorId 操作者ID
     * @param clientInfo 客户端信息
     * @return true-排除当前客户端，false-发送到所有设备
     */
    private boolean shouldExcludeCurrentClient(String targetUserId, String operatorId, ClientInfo clientInfo) {
        // 只有当目标用户是操作者本人，且不是WEBAPI调用时，才排除当前客户端
        return targetUserId.equals(operatorId) &&
               clientInfo.getClientType() != null &&
               !Objects.equals(ClientType.WEBAPI.getCode(), clientInfo.getClientType());
    }

    /**
     * 从GroupMemberDto列表中提取用户ID列表
     */
    private List<String> extractMemberIds(List<GroupMemberDto> groupMembers) {
        List<String> memberIds = new ArrayList<>();
        for (GroupMemberDto member : groupMembers) {
            memberIds.add(member.getMemberId());
        }
        return memberIds;
    }
}
