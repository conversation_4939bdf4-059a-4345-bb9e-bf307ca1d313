package com.lld.im.tcp;

import com.lld.im.codec.config.BootstrapConfig;
import com.lld.im.tcp.reciver.LiveRoomMsgReceiver;
import com.lld.im.tcp.reciver.MessageReciver;
import com.lld.im.tcp.reciver.SystemNotificationReceiver;
import com.lld.im.tcp.redis.RedisManager;
import com.lld.im.tcp.register.RegistryNacos;
import com.lld.im.tcp.register.NacosKit;
import com.lld.im.tcp.server.LimServer;
import com.lld.im.tcp.server.LimWebSocketServer;
import com.lld.im.tcp.task.ConnectionCleanupTask;
import com.lld.im.tcp.utils.MqFactory;
import com.alibaba.nacos.api.naming.NamingFactory;
import com.alibaba.nacos.api.naming.NamingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.yaml.snakeyaml.Yaml;

import java.io.FileInputStream;
import java.io.InputStream;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.Properties;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
public class Starter {

    private static final Logger logger = LoggerFactory.getLogger(Starter.class);

//    HTTP GET POST PUT DELETE 1.0 1.1 2.0
    //client IOS 安卓 pc(windows mac) web //支持json 也支持 protobuf
    //appId
    //28 + imei + body
    //请求头（指令 版本 clientType 消息解析类型 imei长度 appId bodylen）+ imei号 + 请求体
    //len+body


    public static void main(String[] args)  {
        logger.info("IM-TCP 服务器启动中...");
        if(args.length > 0){
            logger.info("正在加载配置文件: {}", args[0]);
            start(args[0]);
        } else {
            logger.error("缺少配置文件路径参数");
            System.exit(1);
        }
    }

    private static void start(String path){
        try {
            logger.info("使用配置文件启动 IM-TCP 服务器: {}", path);

            Yaml yaml = new Yaml();
            InputStream inputStream = new FileInputStream(path);
            BootstrapConfig bootstrapConfig = yaml.loadAs(inputStream, BootstrapConfig.class);

            logger.info("配置文件加载成功");
            logger.info("TCP 端口: {}, WebSocket 端口: {}, 代理节点ID: {}",
                    bootstrapConfig.getLim().getTcpPort(),
                    bootstrapConfig.getLim().getWebSocketPort(),
                    bootstrapConfig.getLim().getBrokerId());

            logger.info("正在启动 TCP 服务器...");
            new LimServer(bootstrapConfig.getLim()).start();
            logger.info("TCP 服务器启动成功，端口: {}", bootstrapConfig.getLim().getTcpPort());

            logger.info("正在启动 WebSocket 服务器...");
            new LimWebSocketServer(bootstrapConfig.getLim()).start();
            logger.info("WebSocket 服务器启动成功，端口: {}", bootstrapConfig.getLim().getWebSocketPort());

            logger.info("正在初始化 Redis 管理器...");
            RedisManager.init(bootstrapConfig);
            logger.info("Redis 管理器初始化完成");

            logger.info("正在初始化 MQ 工厂...");
            MqFactory.init(bootstrapConfig.getLim().getRabbitmq());
            logger.info("MQ 工厂初始化完成");

            logger.info("正在初始化消息接收器...");
            MessageReciver.init(bootstrapConfig.getLim().getBrokerId()+"");
            SystemNotificationReceiver.init(bootstrapConfig.getLim().getBrokerId()+"");
            LiveRoomMsgReceiver.init(bootstrapConfig);
            logger.info("消息接收器初始化完成");

            logger.info("正在注册 Nacos 服务...");
            registerNacos(bootstrapConfig);

            logger.info("正在启动连接清理任务...");
            ConnectionCleanupTask.start();
            logger.info("连接清理任务启动完成");

            // 添加优雅关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("🛑 IM-TCP 服务器正在关闭...");
                try {
                    // 停止连接清理任务
                    ConnectionCleanupTask.stop();
                    logger.info("✅ IM-TCP 服务器关闭完成");
                } catch (Exception e) {
                    logger.error("❌ IM-TCP 服务器关闭时发生异常", e);
                }
            }));

            logger.info("IM-TCP 服务器启动成功！");

        }catch (Exception e){
            logger.error("IM-TCP 服务器启动失败", e);
            System.exit(500);
        }
    }

    /**
     * 注册Nacos服务，替代registerZK方法
     */
    public static void registerNacos(BootstrapConfig config) throws UnknownHostException {
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();

            // 创建Nacos NamingService，包含认证信息
            Properties properties = new Properties();
            properties.setProperty("serverAddr", config.getLim().getNacosConfig().getServerAddr());
            properties.setProperty("namespace", config.getLim().getNacosConfig().getNamespace());

            // 添加认证配置
            if (config.getLim().getNacosConfig().getUsername() != null) {
                properties.setProperty("username", config.getLim().getNacosConfig().getUsername());
            }
            if (config.getLim().getNacosConfig().getPassword() != null) {
                properties.setProperty("password", config.getLim().getNacosConfig().getPassword());
            }

            // 添加超时配置
            if (config.getLim().getNacosConfig().getConnectTimeout() != null) {
                properties.setProperty("namingRequestTimeout", String.valueOf(config.getLim().getNacosConfig().getConnectTimeout()));
            }

            NamingService namingService = NamingFactory.createNamingService(properties);

            // 创建NacosKit和注册器
            NacosKit nacosKit = new NacosKit(namingService);
            RegistryNacos registryNacos = new RegistryNacos(nacosKit, hostAddress, config.getLim());

            // 启动注册线程
            Thread thread = new Thread(registryNacos);
            thread.start();

        } catch (Exception e) {
            logger.error("Nacos 服务注册失败: {}", e.getMessage(), e);
            throw new RuntimeException("Nacos 服务注册失败", e);
        }
    }
}
