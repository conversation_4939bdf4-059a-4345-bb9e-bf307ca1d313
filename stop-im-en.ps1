param([switch]$Force)

Write-Host "IM Service Termination Script" -ForegroundColor Cyan

$javaProcesses = Get-CimInstance Win32_Process | Where-Object { $_.Name -eq "java.exe" }
$imProcesses = @()

foreach ($proc in $javaProcesses) {
    $cmd = $proc.CommandLine
    if ($cmd) {
        if ($cmd -match "im-tcp") {
            $imProcesses += @{ Name = "im-tcp"; PID = $proc.ProcessId }
            Write-Host "Found im-tcp process: PID $($proc.ProcessId)" -ForegroundColor Yellow
        }
        elseif ($cmd -match "im-service") {
            $imProcesses += @{ Name = "im-service"; PID = $proc.ProcessId }
            Write-Host "Found im-service process: PID $($proc.ProcessId)" -ForegroundColor Yellow
        }
        elseif ($cmd -match "im-message-store") {
            $imProcesses += @{ Name = "im-message-store"; PID = $proc.ProcessId }
            Write-Host "Found im-message-store process: PID $($proc.ProcessId)" -ForegroundColor Yellow
        }
    }
}

if ($imProcesses.Count -eq 0) {
    Write-Host "No IM service processes found" -ForegroundColor Green
    exit 0
}

if (-not $Force) {
    $answer = Read-Host "Do you want to terminate these processes? (Y/N)"
    if ($answer -eq "N" -or $answer -eq "n") {
        Write-Host "Cancelled" -ForegroundColor Yellow
        exit 0
    }
}

$success = 0
$failed = 0

foreach ($proc in $imProcesses) {
    try {
        Write-Host "Terminating $($proc.Name) PID:$($proc.PID)" -ForegroundColor White
        Stop-Process -Id $proc.PID -Force
        Write-Host "Successfully terminated $($proc.Name)" -ForegroundColor Green
        $success++
    }
    catch {
        Write-Host "Failed to terminate $($proc.Name)" -ForegroundColor Red
        $failed++
    }
}

Write-Host "Result: Success $success, Failed $failed" -ForegroundColor Cyan
