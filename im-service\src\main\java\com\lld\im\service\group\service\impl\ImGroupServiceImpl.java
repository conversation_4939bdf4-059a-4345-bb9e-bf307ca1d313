package com.lld.im.service.group.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.lld.im.codec.pack.group.CreateGroupPack;
import com.lld.im.codec.pack.group.DestroyGroupPack;
import com.lld.im.codec.pack.group.UpdateGroupInfoPack;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.config.AppConfig;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.*;
import com.lld.im.common.enums.command.GroupEventCommand;
import com.lld.im.common.exception.ApplicationException;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.SyncResp;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import com.lld.im.service.group.dao.ImGroupEntity;
import com.lld.im.service.group.dao.mapper.ImGroupMapper;
import com.lld.im.service.group.dao.mapper.ImGroupMemberMapper;
import com.lld.im.service.group.model.callback.DestroyGroupCallbackDto;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.model.resp.GetGroupResp;
import com.lld.im.service.group.model.resp.GetGroupSpeakPermissionResp;
import com.lld.im.service.group.model.resp.GetGroupJoinThresholdResp;
import com.lld.im.service.group.model.resp.GetJoinedGroupResp;
import com.lld.im.service.group.model.resp.GetRoleInGroupResp;
import com.lld.im.service.group.model.resp.GroupSyncResp;
import com.lld.im.service.group.model.resp.GroupCreatedByUserResp;
import com.lld.im.service.group.model.resp.GroupsCreatedByUserPageResp;
import com.lld.im.service.group.service.ImGroupMemberService;
import com.lld.im.service.group.service.ImGroupService;
import com.lld.im.service.group.utils.GroupSpeakPermissionChecker;
import com.lld.im.service.group.utils.GroupJoinThresholdChecker;
import com.lld.im.service.group.model.GroupSpeakPermissionConfig;
import com.lld.im.service.group.model.GroupJoinThresholdConfig;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.utils.CallbackService;
import com.lld.im.service.utils.GroupMessageProducer;
import com.lld.im.service.utils.MessageUtils;
import com.lld.im.service.user.service.ImUserService;
import com.lld.im.service.group.service.GroupMessageService;
import com.lld.im.common.model.message.GroupChatMessageContent;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
public class ImGroupServiceImpl implements ImGroupService {

    private static final Logger logger = LoggerFactory.getLogger(ImGroupServiceImpl.class);

    @Autowired
    ImGroupMapper imGroupDataMapper;

    @Autowired
    ImGroupMemberMapper imGroupMemberMapper;

    @Autowired
    ImGroupMemberService groupMemberService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    CallbackService callbackService;

    @Autowired
    GroupMessageProducer groupMessageProducer;

    @Autowired
    RedisSeq redisSeq;

    @Autowired
    GroupSpeakPermissionChecker permissionChecker;

    @Autowired
    GroupJoinThresholdChecker thresholdChecker;

    @Autowired
    ImUserService imUserService;

    @Autowired
    GroupMessageService groupMessageService;

    @Autowired
    MessageUtils messageUtils;

    @Override
    public ResponseVO importGroup(ImportGroupReq req) {

        //1.判断群id是否存在
        QueryWrapper<ImGroupEntity> query = new QueryWrapper<>();

        if (StringUtils.isEmpty(req.getGroupId())) {
            req.setGroupId(UUID.randomUUID().toString().replace("-", ""));
        } else {
            query.eq("group_id", req.getGroupId());
            query.eq("app_id", req.getAppId());
            Integer integer = imGroupDataMapper.selectCount(query);
            if (integer > 0) {
                throw new ApplicationException(GroupErrorCode.GROUP_IS_EXIST);
            }
        }

        ImGroupEntity imGroupEntity = new ImGroupEntity();

        if (req.getGroupType() == GroupTypeEnum.PUBLIC.getCode() && StringUtils.isBlank(req.getOwnerId())) {
            throw new ApplicationException(GroupErrorCode.PUBLIC_GROUP_MUST_HAVE_OWNER);
        }

        if (req.getCreateTime() == null) {
            imGroupEntity.setCreateTime(System.currentTimeMillis());
        }
        imGroupEntity.setStatus(GroupStatusEnum.NORMAL.getCode());
        BeanUtils.copyProperties(req, imGroupEntity);
        int insert = imGroupDataMapper.insert(imGroupEntity);

        if (insert != 1) {
            throw new ApplicationException(GroupErrorCode.IMPORT_GROUP_ERROR);
        }

        return ResponseVO.successResponse();
    }

    @Override
    @Transactional
    public ResponseVO createGroup(CreateGroupReq req) {

        /*boolean isAdmin = false;

        if (!isAdmin) {

        }*/
        req.setOwnerId(req.getOperater());

        //1.判断群id是否存在
        QueryWrapper<ImGroupEntity> query = new QueryWrapper<>();

        if (StringUtils.isEmpty(req.getGroupId())) {
            req.setGroupId(UUID.randomUUID().toString().replace("-", ""));
        } else {
            query.eq("group_id", req.getGroupId());
            query.eq("app_id", req.getAppId());
            Integer integer = imGroupDataMapper.selectCount(query);
            if (integer > 0) {
                throw new ApplicationException(GroupErrorCode.GROUP_IS_EXIST);
            }
        }

        if (req.getGroupType() == GroupTypeEnum.PUBLIC.getCode() && StringUtils.isBlank(req.getOwnerId())) {
            throw new ApplicationException(GroupErrorCode.PUBLIC_GROUP_MUST_HAVE_OWNER);
        }

        ImGroupEntity imGroupEntity = new ImGroupEntity();
        long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);
        imGroupEntity.setSequence(seq);
        imGroupEntity.setCreateTime(System.currentTimeMillis());
        imGroupEntity.setStatus(GroupStatusEnum.NORMAL.getCode());
        BeanUtils.copyProperties(req, imGroupEntity);
        int insert = imGroupDataMapper.insert(imGroupEntity);

        GroupMemberDto groupMemberDto = new GroupMemberDto();
        groupMemberDto.setMemberId(req.getOwnerId());
        groupMemberDto.setRole(GroupMemberRoleEnum.OWNER.getCode());
        groupMemberDto.setJoinTime(System.currentTimeMillis());
        groupMemberService.addGroupMember(req.getGroupId(), req.getAppId(), groupMemberDto);

        //插入群成员
        for (GroupMemberDto dto : req.getMember()) {
            groupMemberService.addGroupMember(req.getGroupId(), req.getAppId(), dto);
        }

        if(appConfig.isCreateGroupAfterCallback()){
            callbackService.callback(req.getAppId(), Constants.CallbackCommand.CreateGroupAfter,
                    JSONObject.toJSONString(imGroupEntity));
        }

        CreateGroupPack createGroupPack = new CreateGroupPack();
        BeanUtils.copyProperties(imGroupEntity, createGroupPack);
        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.CREATED_GROUP, createGroupPack
                , new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));
        return ResponseVO.successResponse(imGroupEntity.getGroupId());
    }

    /**
     * @return com.lld.im.common.ResponseVO
     * @description 修改群基础信息，如果是后台管理员调用，则不检查权限，如果不是则检查权限，如果是私有群（微信群）任何人都可以修改资料，公开群只有管理员可以修改
     * 如果是群主或者管理员可以修改其他信息。
     * <AUTHOR>
     */
    @Override
    @Transactional
    public ResponseVO updateBaseGroupInfo(UpdateGroupReq req) {

        //1.判断群id是否存在
        QueryWrapper<ImGroupEntity> query = new QueryWrapper<>();
        query.eq("group_id", req.getGroupId());
        query.eq("app_id", req.getAppId());
        ImGroupEntity imGroupEntity = imGroupDataMapper.selectOne(query);
        if (imGroupEntity == null) {
            throw new ApplicationException(GroupErrorCode.GROUP_IS_EXIST);
        }

        if(imGroupEntity.getStatus() == GroupStatusEnum.DESTROY.getCode()){
            throw new ApplicationException(GroupErrorCode.GROUP_IS_DESTROY);
        }

/*        boolean isAdmin = false;
        if (!isAdmin) {
        }*/

        //不是后台调用需要检查权限
        ResponseVO<GetRoleInGroupResp> role = groupMemberService.getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());

        if (!role.isOk()) {
            return role;
        }

        GetRoleInGroupResp data = role.getData();
        Integer roleInfo = data.getRole();

        boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode() || roleInfo == GroupMemberRoleEnum.OWNER.getCode();

        //允许群主/管理员修改 && GroupTypeEnum.PUBLIC.getCode() == imGroupEntity.getGroupType()
        if (!isManager) {
            throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 保存旧的群名称和群公告，用于检测是否发生变化
        String oldGroupName = imGroupEntity.getGroupName();
        String oldNotification = imGroupEntity.getNotification();

        ImGroupEntity update = new ImGroupEntity();
        long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);
        BeanUtils.copyProperties(req, update);
        update.setUpdateTime(System.currentTimeMillis());
        update.setSequence(seq);
        int row = imGroupDataMapper.update(update, query);
        if (row != 1) {
            throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 检查群名称是否发生变化，如果变化则发送群聊系统消息
        if (req.getGroupName() != null && !req.getGroupName().equals(oldGroupName)) {
            sendGroupNameChangeMessage(req, oldGroupName, req.getGroupName());
        }

        // 检查群公告是否发生变化，如果变化则发送群聊系统消息
        if (req.getNotification() != null && !req.getNotification().equals(oldNotification)) {
            sendGroupNotificationChangeMessage(req, oldNotification, req.getNotification());
        }

        if(appConfig.isModifyGroupAfterCallback()){
            callbackService.callback(req.getAppId(),Constants.CallbackCommand.UpdateGroupAfter,
                    JSONObject.toJSONString(imGroupDataMapper.selectOne(query)));
        }

        UpdateGroupInfoPack pack = new UpdateGroupInfoPack();
        BeanUtils.copyProperties(req, pack);
        pack.setSequence(seq);
        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.UPDATED_GROUP,
                pack, new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));


        return ResponseVO.successResponse();
    }

    /**
     * @return com.lld.im.common.ResponseVO
     * @description 获取用户加入的群组
     * <AUTHOR>
     */
    @Override
    public ResponseVO getJoinedGroup(GetJoinedGroupReq req) {

        ResponseVO<Collection<String>> memberJoinedGroup = groupMemberService.getMemberJoinedGroup(req);
        if (memberJoinedGroup.isOk()) {

            GetJoinedGroupResp resp = new GetJoinedGroupResp();

            if (CollectionUtils.isEmpty(memberJoinedGroup.getData())) {
                resp.setTotalCount(0);
                resp.setGroupList(new ArrayList<>());
                return ResponseVO.successResponse(resp);
            }

            QueryWrapper<ImGroupEntity> query = new QueryWrapper<>();
            query.eq("app_id", req.getAppId());
            query.in("group_id", memberJoinedGroup.getData());

            if (CollectionUtils.isNotEmpty(req.getGroupType())) {
                query.in("group_type", req.getGroupType());
            }

            List<ImGroupEntity> groupList = imGroupDataMapper.selectList(query);
            resp.setGroupList(groupList);
            if (req.getLimit() == null) {
                resp.setTotalCount(groupList.size());
            } else {
                resp.setTotalCount(imGroupDataMapper.selectCount(query));
            }
            return ResponseVO.successResponse(resp);
        } else {
            return memberJoinedGroup;
        }
    }


    /**
     * @return com.lld.im.common.ResponseVO
     * @description 解散群组，只支持后台管理员和群主解散
     * <AUTHOR>
     */
    @Override
    @Transactional
    public ResponseVO destroyGroup(DestroyGroupReq req) {

        boolean isAdmin = false;

        QueryWrapper<ImGroupEntity> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("group_id", req.getGroupId());
        objectQueryWrapper.eq("app_id", req.getAppId());
        ImGroupEntity imGroupEntity = imGroupDataMapper.selectOne(objectQueryWrapper);
        if (imGroupEntity == null) {
            throw new ApplicationException(GroupErrorCode.PRIVATE_GROUP_CAN_NOT_DESTORY);
        }

        if(imGroupEntity.getStatus() == GroupStatusEnum.DESTROY.getCode()){
            throw new ApplicationException(GroupErrorCode.GROUP_IS_DESTROY);
        }

        if (!isAdmin) {
            if (imGroupEntity.getGroupType() == GroupTypeEnum.PUBLIC.getCode()) {
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
            }

            if (imGroupEntity.getGroupType() == GroupTypeEnum.PUBLIC.getCode() &&
                    !imGroupEntity.getOwnerId().equals(req.getOperater())) {
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
            }
        }

        ImGroupEntity update = new ImGroupEntity();
        long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);

        update.setStatus(GroupStatusEnum.DESTROY.getCode());
        update.setSequence(seq);
        int update1 = imGroupDataMapper.update(update, objectQueryWrapper);
        if (update1 != 1) {
            throw new ApplicationException(GroupErrorCode.UPDATE_GROUP_BASE_INFO_ERROR);
        }

        if(appConfig.isModifyGroupAfterCallback()){
            DestroyGroupCallbackDto dto = new DestroyGroupCallbackDto();
            dto.setGroupId(req.getGroupId());
            callbackService.callback(req.getAppId()
                    ,Constants.CallbackCommand.DestoryGroupAfter,
                    JSONObject.toJSONString(dto));
        }

        DestroyGroupPack pack = new DestroyGroupPack();
        pack.setSequence(seq);
        pack.setGroupId(req.getGroupId());
        groupMessageProducer.producer(req.getOperater(),
                GroupEventCommand.DESTROY_GROUP, pack, new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));

        return ResponseVO.successResponse();
    }

    @Override
    @Transactional
    public ResponseVO transferGroup(TransferGroupReq req) {

        ResponseVO<GetRoleInGroupResp> roleInGroupOne = groupMemberService.getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleInGroupOne.isOk()) {
            return roleInGroupOne;
        }

        if (roleInGroupOne.getData().getRole() != GroupMemberRoleEnum.OWNER.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
        }

        ResponseVO<GetRoleInGroupResp> newOwnerRole = groupMemberService.getRoleInGroupOne(req.getGroupId(), req.getOwnerId(), req.getAppId());
        if (!newOwnerRole.isOk()) {
            return newOwnerRole;
        }

        QueryWrapper<ImGroupEntity> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("group_id", req.getGroupId());
        objectQueryWrapper.eq("app_id", req.getAppId());
        ImGroupEntity imGroupEntity = imGroupDataMapper.selectOne(objectQueryWrapper);
        if(imGroupEntity.getStatus() == GroupStatusEnum.DESTROY.getCode()){
            throw new ApplicationException(GroupErrorCode.GROUP_IS_DESTROY);
        }

        ImGroupEntity updateGroup = new ImGroupEntity();
        updateGroup.setOwnerId(req.getOwnerId());
        UpdateWrapper<ImGroupEntity> updateGroupWrapper = new UpdateWrapper<>();
        updateGroupWrapper.eq("app_id", req.getAppId());
        updateGroupWrapper.eq("group_id", req.getGroupId());
        imGroupDataMapper.update(updateGroup, updateGroupWrapper);
        groupMemberService.transferGroupMember(req.getOwnerId(), req.getGroupId(), req.getAppId());

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO getGroup(String groupId, Integer appId) {

        QueryWrapper<ImGroupEntity> query = new QueryWrapper<>();
        query.eq("app_id", appId);
        query.eq("group_id", groupId);
        ImGroupEntity imGroupEntity = imGroupDataMapper.selectOne(query);

        if (imGroupEntity == null) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_IS_NOT_EXIST);
        }
        return ResponseVO.successResponse(imGroupEntity);
    }

    @Override
    public ResponseVO getGroup(GetGroupReq req) {

        ResponseVO group = this.getGroup(req.getGroupId(), req.getAppId());

        if(!group.isOk()){
            return group;
        }

        GetGroupResp getGroupResp = new GetGroupResp();
        ImGroupEntity groupEntity = (ImGroupEntity) group.getData();
        BeanUtils.copyProperties(groupEntity, getGroupResp);

        // 解析进群门槛配置和发言权限配置
        String extraField = groupEntity.getExtra();

        // 解析进群门槛配置
        if (StringUtils.isNotBlank(extraField) && thresholdChecker.hasThresholdConfig(extraField)) {
            try {
                GroupJoinThresholdConfig thresholdConfig = thresholdChecker.parseThresholdConfig(extraField);
                getGroupResp.setJoinThresholdType(thresholdConfig.getType().getCode());
            } catch (Exception e) {
                logger.warn("解析进群门槛配置失败，设置为null: groupId={}", req.getGroupId(), e);
                getGroupResp.setJoinThresholdType(null);
            }
        } else {
            getGroupResp.setJoinThresholdType(null);
        }

        // 解析发言权限配置
        if (StringUtils.isNotBlank(extraField) && permissionChecker.hasPermissionConfig(extraField)) {
            try {
                GroupSpeakPermissionConfig permissionConfig = permissionChecker.parsePermissionConfig(extraField);
                getGroupResp.setSpeakPermissionType(permissionConfig.getType().getCode());
            } catch (Exception e) {
                logger.warn("解析发言权限配置失败，设置为null: groupId={}", req.getGroupId(), e);
                getGroupResp.setSpeakPermissionType(null);
            }
        } else {
            getGroupResp.setSpeakPermissionType(null);
        }

        try {
            ResponseVO<List<GroupMemberDto>> groupMember = groupMemberService.getGroupMember(req.getGroupId(), req.getAppId());
            if (groupMember.isOk()) {
                getGroupResp.setMemberList(groupMember.getData());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseVO.successResponse(getGroupResp);
    }

    @Override
    public ResponseVO muteGroup(MuteGroupReq req) {

        ResponseVO<ImGroupEntity> groupResp = getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }

        if(groupResp.getData().getStatus() == GroupStatusEnum.DESTROY.getCode()){
            throw new ApplicationException(GroupErrorCode.GROUP_IS_DESTROY);
        }

        boolean isadmin = false;

        if (!isadmin) {
            //不是后台调用需要检查权限
            ResponseVO<GetRoleInGroupResp> role = groupMemberService.getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());

            if (!role.isOk()) {
                return role;
            }

            GetRoleInGroupResp data = role.getData();
            Integer roleInfo = data.getRole();

            boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode() || roleInfo == GroupMemberRoleEnum.OWNER.getCode();

            //公开群只能群主修改资料
            if (!isManager) {
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
            }
        }

        ImGroupEntity update = new ImGroupEntity();
        update.setMute(req.getMute());

        UpdateWrapper<ImGroupEntity> wrapper = new UpdateWrapper<>();
        wrapper.eq("group_id",req.getGroupId());
        wrapper.eq("app_id",req.getAppId());
        imGroupDataMapper.update(update,wrapper);

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO syncJoinedGroupList(SyncReq req) {
        // 1. 参数验证和默认值设置
        if (req.getMaxLimit() == null || req.getMaxLimit() > 100) {
            req.setMaxLimit(100);
        }
        if (req.getLastSequence() == null) {
            req.setLastSequence(0L);
        }

        SyncResp<ImGroupEntity> resp = new SyncResp<>();

        // 2. 获取用户加入的群组列表
        ResponseVO<Collection<String>> memberJoinedGroup = groupMemberService.syncMemberJoinedGroup(req.getOperater(), req.getAppId());
        if (memberJoinedGroup.isOk()) {
            Collection<String> data = memberJoinedGroup.getData();

            // 如果用户没有加入任何群组，直接返回空结果
            if (CollectionUtils.isEmpty(data)) {
                resp.setMaxSequence(0L);
                resp.setDataList(new ArrayList<>());
                resp.setCompleted(true);
                return ResponseVO.successResponse(resp);
            }

            // 3. 获取服务端当前最大序列号
            Long serverMaxSeq = imGroupDataMapper.getGroupMaxSeq(data, req.getAppId());

            // 4. 提前终止优化：如果客户端的lastSequence已经是最新的，直接返回空结果
            if (req.getLastSequence() >= serverMaxSeq) {
                resp.setMaxSequence(serverMaxSeq);
                resp.setDataList(new ArrayList<>());
                resp.setCompleted(true);
                return ResponseVO.successResponse(resp);
            }

            // 5. 查询增量数据：sequence > req.getLastSequence limit maxLimit
            QueryWrapper<ImGroupEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", req.getAppId());
            queryWrapper.in("group_id", data);
            queryWrapper.gt("sequence", req.getLastSequence());
            queryWrapper.last(" limit " + req.getMaxLimit());
            queryWrapper.orderByAsc("sequence");

            List<ImGroupEntity> list = imGroupDataMapper.selectList(queryWrapper);

            if (!CollectionUtils.isEmpty(list)) {
                ImGroupEntity maxSeqEntity = list.get(list.size() - 1);
                resp.setDataList(list);

                // 6. 设置当前批次的最大序列号作为maxSequence（修正语义）
                Long currentBatchMaxSeq = maxSeqEntity.getSequence();
                resp.setMaxSequence(currentBatchMaxSeq);

                // 7. 判断是否同步完成：当前批次最后一条记录的序列号 >= 服务端最大序列号
                resp.setCompleted(currentBatchMaxSeq >= serverMaxSeq);
                return ResponseVO.successResponse(resp);
            }

            // 8. 没有查询到数据，说明已经同步完成
            resp.setMaxSequence(serverMaxSeq);
            resp.setCompleted(true);
            return ResponseVO.successResponse(resp);
        }

        // 9. 获取用户群组列表失败，返回空结果
        resp.setMaxSequence(0L);
        resp.setDataList(new ArrayList<>());
        resp.setCompleted(true);
        return ResponseVO.successResponse(resp);
    }

    /**
     * 增量同步群组列表（包含退出群组信息）
     * 修复版本：能够返回用户退出的群组信息，确保客户端数据一致性
     * @param req 同步请求
     * @return 群组同步响应，包含群组信息和用户状态
     */
    @Override
    public ResponseVO<SyncResp<GroupSyncResp>> syncJoinedGroupListV2(SyncReq req) {
        // 1. 参数验证和默认值设置
        if (req.getMaxLimit() == null || req.getMaxLimit() > 100) {
            req.setMaxLimit(100);
        }
        if (req.getLastSequence() == null) {
            req.setLastSequence(0L);
        }

        SyncResp<GroupSyncResp> resp = new SyncResp<>();

        // 2. 获取服务端当前最大序列号（包括用户所有相关群组）
        Long serverMaxSeq = imGroupDataMapper.getUserGroupMaxSeq(req.getAppId(), req.getOperater());

        // 3. 提前终止优化：如果客户端的lastSequence已经是最新的，直接返回空结果
        if (req.getLastSequence() >= serverMaxSeq) {
            resp.setMaxSequence(serverMaxSeq);
            resp.setDataList(new ArrayList<>());
            resp.setCompleted(true);
            return ResponseVO.successResponse(resp);
        }

        // 4. 查询增量数据：包含群组信息和用户在群组中的状态
        List<GroupSyncResp> list = imGroupMemberMapper.syncGroupWithMemberStatus(
                req.getAppId(), req.getOperater(), req.getLastSequence(), req.getMaxLimit());

        if (!CollectionUtils.isEmpty(list)) {
            GroupSyncResp maxSeqEntity = list.get(list.size() - 1);
            resp.setDataList(list);

            // 5. 设置当前批次的最大序列号作为maxSequence
            Long currentBatchMaxSeq = maxSeqEntity.getGroupSequence();
            resp.setMaxSequence(currentBatchMaxSeq);

            // 6. 判断是否同步完成：当前批次最后一条记录的序列号 >= 服务端最大序列号
            resp.setCompleted(currentBatchMaxSeq >= serverMaxSeq);
            return ResponseVO.successResponse(resp);
        }

        // 7. 没有查询到数据，说明已经同步完成
        resp.setMaxSequence(serverMaxSeq);
        resp.setCompleted(true);
        return ResponseVO.successResponse(resp);
    }

    @Override
    public Long getUserGroupMaxSeq(String userId, Integer appId) {

        ResponseVO<Collection<String>> memberJoinedGroup = groupMemberService.syncMemberJoinedGroup(userId, appId);
        if(!memberJoinedGroup.isOk()){
            throw new ApplicationException(500,"");
        }
        Long maxSeq =
                imGroupDataMapper.getGroupMaxSeq(memberJoinedGroup.getData(),
                        appId);
        return maxSeq;
    }

    @Override
    @Transactional
    public ResponseVO setGroupSpeakPermission(SetGroupSpeakPermissionReq req) {
        // 1. 参数验证
        if (req.getPermissionType() == null || req.getPermissionType() < 0 || req.getPermissionType() > 3) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_SPEAK_PERMISSION_CONFIG_ERROR);
        }

        // 2. 检查群组是否存在
        ResponseVO<ImGroupEntity> groupResp = getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }
        ImGroupEntity group = groupResp.getData();

        // 3. 检查操作者权限（只有群主和管理员可以设置）
        ResponseVO<GetRoleInGroupResp> roleResp = groupMemberService.getRoleInGroupOne(
            req.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleResp.isOk()) {
            return roleResp;
        }
        GetRoleInGroupResp operatorRole = roleResp.getData();
        if (operatorRole.getRole() != GroupMemberRoleEnum.OWNER.getCode()
            && operatorRole.getRole() != GroupMemberRoleEnum.MAMAGER.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 4. 验证指定成员列表（如果权限类型为指定成员）
        if (req.getPermissionType() == 2) { // SPECIFIED_MEMBERS
            if (CollectionUtils.isEmpty(req.getAllowedMembers())) {
                return ResponseVO.errorResponse(GroupErrorCode.GROUP_SPEAK_PERMISSION_INVALID_MEMBERS);
            }
            // 验证成员是否都在群内
            for (String memberId : req.getAllowedMembers()) {
                ResponseVO<GetRoleInGroupResp> memberResp = groupMemberService.getRoleInGroupOne(
                    req.getGroupId(), memberId, req.getAppId());
                if (!memberResp.isOk()) {
                    return ResponseVO.errorResponse(GroupErrorCode.GROUP_SPEAK_PERMISSION_INVALID_MEMBERS);
                }
            }
        }

        // 5. 构建权限配置
        GroupSpeakPermissionConfig config = new GroupSpeakPermissionConfig();
        config.setType(com.lld.im.common.enums.GroupSpeakPermissionEnum.getByCode(req.getPermissionType()));
        config.setAllowedMembers(req.getAllowedMembers());
        config.setUpdateTime(System.currentTimeMillis());
        config.setUpdatedBy(req.getOperater());

        // 6. 更新群组extra字段
        String newExtra = permissionChecker.buildPermissionExtra(group.getExtra(), config);
        UpdateWrapper<ImGroupEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("group_id", req.getGroupId())
                    .eq("app_id", req.getAppId())
                    .set("extra", newExtra);

        int updateResult = imGroupDataMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_SPEAK_PERMISSION_UPDATE_FAILED);
        }

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO<GetGroupSpeakPermissionResp> getGroupSpeakPermission(String groupId, Integer appId) {
        // 1. 检查群组是否存在
        ResponseVO<ImGroupEntity> groupResp = getGroup(groupId, appId);
        if (!groupResp.isOk()) {
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 解析权限配置
        GroupSpeakPermissionConfig config = permissionChecker.parsePermissionConfig(group.getExtra());

        // 3. 构建响应
        GetGroupSpeakPermissionResp resp = new GetGroupSpeakPermissionResp();
        resp.setGroupId(groupId);
        resp.setPermissionType(config.getType().getCode());
        resp.setPermissionDescription(config.getType().getDescription());
        resp.setAllowedMembers(config.getAllowedMembers());
        resp.setUpdateTime(config.getUpdateTime());
        resp.setUpdatedBy(config.getUpdatedBy());

        return ResponseVO.successResponse(resp);
    }

    @Override
    @Transactional
    public ResponseVO setGroupJoinThreshold(SetGroupJoinThresholdReq req) {
        // 1. 参数验证
        if (req.getThresholdType() == null || !GroupJoinThresholdEnum.isValidCode(req.getThresholdType())) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_CONFIG_ERROR);
        }

        // 2. 检查群组是否存在
        ResponseVO<ImGroupEntity> groupResp = getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }
        ImGroupEntity group = groupResp.getData();

        // 3. 检查操作者权限（只有群主和管理员可以设置）
        ResponseVO<GetRoleInGroupResp> roleResp = groupMemberService.getRoleInGroupOne(
            req.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleResp.isOk()) {
            return roleResp;
        }

        GetRoleInGroupResp memberInfo = roleResp.getData();
        if (memberInfo.getRole() != GroupMemberRoleEnum.OWNER.getCode() &&
            memberInfo.getRole() != GroupMemberRoleEnum.MAMAGER.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 4. 构建门槛配置
        GroupJoinThresholdConfig config = new GroupJoinThresholdConfig();
        config.setType(com.lld.im.common.enums.GroupJoinThresholdEnum.getByCode(req.getThresholdType()));
        config.setUpdateTime(System.currentTimeMillis());
        config.setUpdatedBy(req.getOperater());

        // 5. 更新群组extra字段
        String newExtra = thresholdChecker.buildThresholdExtra(group.getExtra(), config);
        UpdateWrapper<ImGroupEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("group_id", req.getGroupId())
                    .eq("app_id", req.getAppId())
                    .set("extra", newExtra);

        int updateResult = imGroupDataMapper.update(null, updateWrapper);
        if (updateResult <= 0) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_UPDATE_FAILED);
        }

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO<GetGroupJoinThresholdResp> getGroupJoinThreshold(String groupId, Integer appId) {
        // 1. 检查群组是否存在
        ResponseVO<ImGroupEntity> groupResp = getGroup(groupId, appId);
        if (!groupResp.isOk()) {
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 解析门槛配置
        GroupJoinThresholdConfig config = thresholdChecker.parseThresholdConfig(group.getExtra());

        // 3. 构建响应
        GetGroupJoinThresholdResp resp = new GetGroupJoinThresholdResp();
        resp.setGroupId(groupId);
        resp.setThresholdType(config.getType().getCode());
        resp.setThresholdDescription(config.getType().getDescription());
        resp.setUpdateTime(config.getUpdateTime());
        resp.setUpdatedBy(config.getUpdatedBy());

        return ResponseVO.successResponse(resp);
    }

    @Override
    public ResponseVO<GroupsCreatedByUserPageResp> getGroupsCreatedByUser(String targetUserId, Integer appId, Integer pageNum, Integer pageSize, String currentUserId) {
        try {
            // 1. 参数验证
            if (StringUtils.isBlank(targetUserId) || appId == null || pageNum == null || pageSize == null) {
                return ResponseVO.errorResponse(400, "参数不能为空");
            }

            if (pageNum < 1) {
                pageNum = 1;
            }

            if (pageSize < 1 || pageSize > 100) {
                pageSize = 20; // 默认每页20条，最大100条
            }
            // 2. 分页查询用户创建的群组
            Page<ImGroupEntity> page = new Page<>(pageNum, pageSize);
            QueryWrapper<ImGroupEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId);
            queryWrapper.eq("owner_id", targetUserId);
            queryWrapper.eq("status", GroupStatusEnum.NORMAL.getCode()); // 只查询正常状态的群组
            queryWrapper.orderByDesc("create_time"); // 按创建时间倒序排列

            IPage<ImGroupEntity> pageResult = imGroupDataMapper.selectPage(page, queryWrapper);

            // 3. 转换为响应对象
            List<GroupCreatedByUserResp> groupList = new ArrayList<>();
            for (ImGroupEntity entity : pageResult.getRecords()) {
                GroupCreatedByUserResp groupResp = new GroupCreatedByUserResp();
                BeanUtils.copyProperties(entity, groupResp);

                // 查询当前成员数量
                Integer memberCount = imGroupDataMapper.getGroupMemberCount(entity.getGroupId(), appId);
                groupResp.setCurrentMemberCount(memberCount != null ? memberCount : 0);

                groupList.add(groupResp);
            }

            // 5. 构建分页响应
            GroupsCreatedByUserPageResp response = GroupsCreatedByUserPageResp.of(
                groupList,
                pageResult.getTotal(),
                pageNum,
                pageSize
            );

            return ResponseVO.successResponse(response);

        } catch (Exception e) {
            logger.error("获取用户创建的群组列表失败, targetUserId: {}, appId: {}, error: {}", targetUserId, appId, e.getMessage(), e);
            return ResponseVO.errorResponse(500, messageUtils.getMessage("error.group.get.list.failed"));
        }
    }

    /**
     * 发送群名称修改的群聊系统消息
     * @param req 更新群组请求
     * @param oldGroupName 旧群名称
     * @param newGroupName 新群名称
     */
    private void sendGroupNameChangeMessage(UpdateGroupReq req, String oldGroupName, String newGroupName) {
        try {
            // 获取修改者昵称
            String operatorNickname = getUserNickname(req.getOperater(), req.getAppId());

            // 构建系统消息内容,前端处理国际化
            String messageContent = operatorNickname + ":" + newGroupName;

            // 创建群聊系统消息
            GroupChatMessageContent systemMessage = new GroupChatMessageContent();

            systemMessage.setMessageId(UUID.randomUUID().toString());
            systemMessage.setFromId(req.getOperater());
            systemMessage.setGroupId(req.getGroupId());
            systemMessage.setMessageBody(messageContent);
            systemMessage.setMessageTime(System.currentTimeMillis());
            systemMessage.setAppId(req.getAppId());
            systemMessage.setClientType(req.getClientType() != null ? req.getClientType() : 0);
            systemMessage.setImei(req.getImei() != null ? req.getImei() : "system");

            // 设置消息类型为系统消息
            systemMessage.setExtra("80");

            // 使用系统消息专用方法发送（绕过权限验证）
            groupMessageService.processSystemMessage(systemMessage);

            logger.info("群名称修改系统消息发送成功，群组: {}, 操作者: {}, 旧名称: {}, 新名称: {}",
                    req.getGroupId(), req.getOperater(), oldGroupName, newGroupName);

        } catch (Exception e) {
            logger.error("发送群名称修改系统消息失败，群组: {}, 操作者: {}, 错误: {}",
                    req.getGroupId(), req.getOperater(), e.getMessage(), e);
            // 消息发送失败不影响群组更新的主流程
        }
    }

    /**
     * 获取用户昵称
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户昵称，如果获取失败则返回默认值
     */
    private String getUserNickname(String userId, Integer appId) {
        if (StringUtils.isEmpty(userId)) {
            return messageUtils.getMessage("user.nickname.default");
        }

        try {
            ResponseVO<Map<String, Object>> userInfoResp = imUserService.getUserInfo(userId, appId);
            if (userInfoResp.isOk() && userInfoResp.getData() != null) {
                String nickname = (String) userInfoResp.getData().get("nickName");
                return StringUtils.isNotEmpty(nickname) ? nickname : userId;
            }
        } catch (Exception e) {
            logger.warn("获取用户昵称失败: userId={}, appId={}, error={}", userId, appId, e.getMessage());
        }

        // 如果获取失败，返回userId作为默认值
        return userId;
    }

    /**
     * 发送群公告修改的群聊系统消息
     * @param req 更新群组请求
     * @param oldNotification 旧群公告
     * @param newNotification 新群公告
     */
    private void sendGroupNotificationChangeMessage(UpdateGroupReq req, String oldNotification, String newNotification) {
        try {
            // 获取修改者昵称
            String operatorNickname = getUserNickname(req.getOperater(), req.getAppId());

            // 构建系统消息内容，前端处理国际化
            String messageContent = operatorNickname;

            // 创建群聊系统消息
            GroupChatMessageContent systemMessage = new GroupChatMessageContent();
            systemMessage.setMessageId(UUID.randomUUID().toString());
            systemMessage.setFromId(req.getOperater());
            systemMessage.setGroupId(req.getGroupId());
            systemMessage.setMessageBody(messageContent);
            systemMessage.setMessageTime(System.currentTimeMillis());
            systemMessage.setAppId(req.getAppId());
            systemMessage.setClientType(req.getClientType() != null ? req.getClientType() : 0);
            systemMessage.setImei(req.getImei() != null ? req.getImei() : "system");

            // 设置消息类型为系统消息
            systemMessage.setExtra("81");

            // 使用系统消息专用方法发送（绕过权限验证）
            groupMessageService.processSystemMessage(systemMessage);

            logger.info("群公告修改系统消息发送成功，群组: {}, 操作者: {}, 旧公告: {}, 新公告: {}",
                    req.getGroupId(), req.getOperater(), oldNotification, newNotification);

        } catch (Exception e) {
            logger.error("发送群公告修改系统消息失败，群组: {}, 操作者: {}, 错误: {}",
                    req.getGroupId(), req.getOperater(), e.getMessage(), e);
            // 消息发送失败不影响群组更新的主流程
        }
    }

}
