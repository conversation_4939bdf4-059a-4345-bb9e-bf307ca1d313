package com.lld.im.service.friendship.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel(description = "添加好友请求模型")
@Data
public class AddFriendReq extends RequestBase {

    @ApiModelProperty(value = "发起者用户ID", required = true, example = "user123")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "目标好友信息", required = true)
    @NotNull(message = "toItem不能为空")
    private FriendDto toItem;

}
