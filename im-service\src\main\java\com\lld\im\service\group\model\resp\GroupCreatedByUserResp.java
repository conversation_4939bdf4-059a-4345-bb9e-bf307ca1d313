package com.lld.im.service.group.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户创建的群组信息响应
 * @description: 群组基本信息模型，用于返回用户创建的群组列表
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "用户创建的群组信息响应")
@Data
public class GroupCreatedByUserResp {

    @ApiModelProperty(value = "群组ID", example = "group123", notes = "群组的唯一标识")
    private String groupId;

    @ApiModelProperty(value = "群组名称", example = "技术交流群", notes = "群组的显示名称")
    private String groupName;

    @ApiModelProperty(value = "群组类型", example = "1", notes = "群类型：1私有群（类似微信）2公开群（类似qq）")
    private Integer groupType;

    @ApiModelProperty(value = "群组头像", example = "http://example.com/avatar.jpg", notes = "群组头像URL")
    private String photo;

    @ApiModelProperty(value = "群组简介", example = "这是一个技术交流群", notes = "群组的简介信息")
    private String introduction;

    @ApiModelProperty(value = "群组公告", example = "欢迎大家加入", notes = "群组的公告信息")
    private String notification;

    @ApiModelProperty(value = "最大成员数", example = "500", notes = "群组允许的最大成员数量")
    private Integer maxMemberCount;

    @ApiModelProperty(value = "当前成员数", example = "128", notes = "群组当前的成员数量")
    private Integer currentMemberCount;

    @ApiModelProperty(value = "群组状态", example = "0", notes = "群状态：0正常 1解散")
    private Integer status;

    @ApiModelProperty(value = "是否全员禁言", example = "0", notes = "是否全员禁言：0不禁言 1全员禁言")
    private Integer mute;

    @ApiModelProperty(value = "申请加群类型", example = "1", notes = "申请加群选项：0禁止任何人申请加入 1需要群主或管理员审批 2允许无需审批自由加入群组")
    private Integer applyJoinType;

    @ApiModelProperty(value = "创建时间", example = "1640995200000", notes = "群组创建时间戳")
    private Long createTime;

    @ApiModelProperty(value = "更新时间", example = "1640995200000", notes = "群组最后更新时间戳")
    private Long updateTime;
}
