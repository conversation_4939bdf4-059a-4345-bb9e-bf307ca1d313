package com.lld.im.service.message.util;

import com.lld.im.service.message.model.resp.SearchMessageResp;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文本高亮工具类
 * @description: 用于处理搜索结果中关键词的高亮显示
 * @author: lld
 * @version: 1.0
 */
public class HighlightUtil {

    /**
     * 默认高亮标签
     */
    private static final String DEFAULT_HIGHLIGHT_START_TAG = "<em>";
    private static final String DEFAULT_HIGHLIGHT_END_TAG = "</em>";
    
    /**
     * 默认片段长度（关键词前后各显示的字符数）
     */
    private static final int DEFAULT_FRAGMENT_LENGTH = 50;

    /**
     * 对消息内容进行高亮处理
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @return 高亮处理后的消息内容
     */
    public static String highlightText(String messageBody, String keyword) {
        return highlightText(messageBody, keyword, DEFAULT_HIGHLIGHT_START_TAG, DEFAULT_HIGHLIGHT_END_TAG);
    }

    /**
     * 对消息内容进行高亮处理（自定义标签）
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @param startTag 高亮开始标签
     * @param endTag 高亮结束标签
     * @return 高亮处理后的消息内容
     */
    public static String highlightText(String messageBody, String keyword, String startTag, String endTag) {
        if (StringUtils.isBlank(messageBody) || StringUtils.isBlank(keyword)) {
            return messageBody;
        }

        // 使用正则表达式进行忽略大小写的替换
        String escapedKeyword = Pattern.quote(keyword);
        Pattern pattern = Pattern.compile(escapedKeyword, Pattern.CASE_INSENSITIVE);
        
        return pattern.matcher(messageBody).replaceAll(startTag + "$0" + endTag);
    }

    /**
     * 生成高亮片段列表
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @return 高亮片段列表
     */
    public static List<SearchMessageResp.HighlightFragment> generateHighlightFragments(String messageBody, String keyword) {
        return generateHighlightFragments(messageBody, keyword, DEFAULT_FRAGMENT_LENGTH);
    }

    /**
     * 生成高亮片段列表（自定义片段长度）
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @param fragmentLength 片段长度（关键词前后各显示的字符数）
     * @return 高亮片段列表
     */
    public static List<SearchMessageResp.HighlightFragment> generateHighlightFragments(String messageBody, String keyword, int fragmentLength) {
        List<SearchMessageResp.HighlightFragment> fragments = new ArrayList<>();
        
        if (StringUtils.isBlank(messageBody) || StringUtils.isBlank(keyword)) {
            return fragments;
        }

        // 使用正则表达式查找所有匹配的关键词位置
        String escapedKeyword = Pattern.quote(keyword);
        Pattern pattern = Pattern.compile(escapedKeyword, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(messageBody);

        while (matcher.find()) {
            int matchStart = matcher.start();
            int matchEnd = matcher.end();
            
            // 计算片段的起始和结束位置
            int fragmentStart = Math.max(0, matchStart - fragmentLength);
            int fragmentEnd = Math.min(messageBody.length(), matchEnd + fragmentLength);
            
            // 提取片段内容
            String fragment = messageBody.substring(fragmentStart, fragmentEnd);
            
            // 对片段进行高亮处理
            String highlightedFragment = highlightText(fragment, keyword);
            
            // 创建高亮片段对象
            SearchMessageResp.HighlightFragment highlightFragment = new SearchMessageResp.HighlightFragment();
            highlightFragment.setFragment(fragment);
            highlightFragment.setHighlightedFragment(highlightedFragment);
            highlightFragment.setStartIndex(fragmentStart);
            highlightFragment.setEndIndex(fragmentEnd);
            
            fragments.add(highlightFragment);
        }

        return fragments;
    }

    /**
     * 生成摘要片段（只显示第一个匹配的关键词周围的内容）
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @param maxLength 摘要最大长度
     * @return 摘要片段
     */
    public static String generateSummaryFragment(String messageBody, String keyword, int maxLength) {
        if (StringUtils.isBlank(messageBody) || StringUtils.isBlank(keyword)) {
            return messageBody;
        }

        // 查找第一个匹配的关键词位置
        String lowerMessageBody = messageBody.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();
        int keywordIndex = lowerMessageBody.indexOf(lowerKeyword);
        
        if (keywordIndex == -1) {
            // 如果没有找到关键词，返回消息开头的内容
            return messageBody.length() > maxLength ? 
                messageBody.substring(0, maxLength) + "..." : messageBody;
        }

        // 计算摘要的起始位置，尽量让关键词居中
        int summaryStart = Math.max(0, keywordIndex - (maxLength - keyword.length()) / 2);
        int summaryEnd = Math.min(messageBody.length(), summaryStart + maxLength);
        
        // 如果从中间开始，添加省略号
        String summary = messageBody.substring(summaryStart, summaryEnd);
        if (summaryStart > 0) {
            summary = "..." + summary;
        }
        if (summaryEnd < messageBody.length()) {
            summary = summary + "...";
        }

        return summary;
    }

    /**
     * 检查文本是否包含关键词（忽略大小写）
     * 
     * @param text 文本内容
     * @param keyword 关键词
     * @return 是否包含关键词
     */
    public static boolean containsKeyword(String text, String keyword) {
        if (StringUtils.isBlank(text) || StringUtils.isBlank(keyword)) {
            return false;
        }
        return text.toLowerCase().contains(keyword.toLowerCase());
    }

    /**
     * 转义HTML特殊字符
     * 
     * @param text 原始文本
     * @return 转义后的文本
     */
    public static String escapeHtml(String text) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }

    /**
     * 对消息内容进行HTML转义后再高亮处理
     * 
     * @param messageBody 原始消息内容
     * @param keyword 搜索关键词
     * @return 转义并高亮处理后的消息内容
     */
    public static String highlightTextWithHtmlEscape(String messageBody, String keyword) {
        if (StringUtils.isBlank(messageBody) || StringUtils.isBlank(keyword)) {
            return escapeHtml(messageBody);
        }

        // 先转义HTML特殊字符
        String escapedMessageBody = escapeHtml(messageBody);
        String escapedKeyword = escapeHtml(keyword);
        
        // 再进行高亮处理
        return highlightText(escapedMessageBody, escapedKeyword);
    }
}
