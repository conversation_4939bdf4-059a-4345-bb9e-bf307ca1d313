package com.lld.im.service.group.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 获取群组发言权限响应
 * @author: lld
 * @description: 获取群组发言权限配置的响应模型
 */
@ApiModel(description = "获取群组发言权限响应模型")
@Data
public class GetGroupSpeakPermissionResp {
    
    @ApiModelProperty(value = "群组ID", example = "group123", notes = "群组的唯一标识")
    private String groupId;
    
    @ApiModelProperty(value = "权限类型", example = "1", 
                     notes = "0-所有人可发言 1-仅群主/管理员可发言 2-指定成员可发言 3-禁止发言")
    private Integer permissionType;
    
    @ApiModelProperty(value = "权限类型描述", example = "仅群主/管理员可发言", notes = "权限类型的文字描述")
    private String permissionDescription;
    
    @ApiModelProperty(value = "允许发言的成员列表", example = "[\"user123\", \"user456\"]", 
                     notes = "当权限类型为2时返回，指定允许发言的成员用户ID列表")
    private List<String> allowedMembers;
    
    @ApiModelProperty(value = "权限设置时间", example = "1640995200000", notes = "权限最后设置的时间戳")
    private Long updateTime;
    
    @ApiModelProperty(value = "权限设置者", example = "admin001", notes = "最后设置权限的用户ID")
    private String updatedBy;
}
