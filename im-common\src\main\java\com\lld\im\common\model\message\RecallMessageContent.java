package com.lld.im.common.model.message;

import com.lld.im.common.model.ClientInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 撤回消息内容模型
 * @author: Chackylee
 * @description: 消息撤回操作的数据模型
 **/
@ApiModel(description = "撤回消息内容模型")
@Data
public class RecallMessageContent extends ClientInfo {

    @ApiModelProperty(value = "消息键", required = true, example = "419455774914383872", notes = "要撤回的消息唯一标识")
    private Long messageKey;

    @ApiModelProperty(value = "发送者用户ID", required = true, example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者用户ID", example = "user456", notes = "消息接收者的用户ID，群组消息时为空")
    private String toId;

    @ApiModelProperty(value = "消息时间", required = true, example = "1665026849851", notes = "原消息的发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "消息序列号", required = true, example = "2", notes = "原消息的序列号")
    private Long messageSequence;

    @ApiModelProperty(value = "会话类型 (0 单聊 1群聊 2机器人 3公众号)", required = true, example = "0", notes = "要撤回消息所属的会话类型")
    private Integer conversationType;

}
