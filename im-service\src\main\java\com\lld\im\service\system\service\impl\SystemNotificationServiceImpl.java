package com.lld.im.service.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.lld.im.common.ResponseVO;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.command.SystemCommand;
import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.SyncResp;
import com.lld.im.service.system.model.entity.ImSystemNotificationEntity;
import com.lld.im.service.system.dao.mapper.ImSystemNotificationMapper;
import com.lld.im.service.system.dao.mapper.ImUserSystemNotificationMapper;
import com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity;
import com.lld.im.service.system.model.req.SystemNotification;
import com.lld.im.service.system.model.resp.NotificationDetailResp;
import com.lld.im.service.system.model.resp.NotificationTypeLatestResp;
import com.lld.im.service.system.model.dto.NotificationTypeLatestDto;
import com.lld.im.service.system.config.NotificationTypeConfig;

import java.util.Objects;
import java.util.Comparator;
import com.lld.im.service.system.service.SystemNotificationService;
import com.lld.im.service.message.model.resp.PageResult;
import com.lld.im.service.utils.SnowflakeIdWorker;
import com.lld.im.codec.pack.system.SystemNotificationPack;
import com.lld.im.service.message.model.BroadcastMessageContent;
import com.lld.im.service.user.dao.mapper.ImUserDataMapper;
import com.lld.im.service.user.dao.ImUserDataEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class SystemNotificationServiceImpl implements SystemNotificationService {

    private static final Logger logger = LoggerFactory.getLogger(SystemNotificationServiceImpl.class);

    @Autowired
    private ImSystemNotificationMapper systemNotificationMapper;

    @Autowired
    private ImUserSystemNotificationMapper userNotificationMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Autowired
    private ImUserDataMapper imUserDataMapper;

    @Autowired
    private NotificationTypeConfig notificationTypeConfig;



    @Override
    @Transactional
    public void sendSystemNotification(Integer appId, SystemNotification notification) {
        logger.info("发送系统通知给所有用户: {}", JSONObject.toJSONString(notification));

        // 参数验证
        if (appId == null || notification == null) {
            throw new IllegalArgumentException("应用ID和通知内容不能为空");
        }

        // 验证排除用户列表
        if (notification.getExcludeUserIds() != null && !notification.getExcludeUserIds().isEmpty()) {
            logger.info("系统通知包含排除用户列表，排除用户数量: {}", notification.getExcludeUserIds().size());
        }

        // 1. 存储系统通知到数据库
        ImSystemNotificationEntity notificationEntity = saveNotification(appId, notification);

        // 2. 创建用户通知关系记录（直接保存到数据库，不使用Redis缓存）
        if (notification.getPersistToDatabase()) {
            createUserNotificationRelations(appId, notificationEntity, null, notification);
            logger.debug("通知用户关系已保存到数据库，类型: {}", notification.getNotificationType());
        } else {
            logger.info("跳过数据库存储，通知类型: {} - 仅进行实时推送", notification.getNotificationType());
        }

        // 3. 构建广播消息包（包含通知ID）
        SystemNotificationPack pack = buildNotificationPack(notification, notificationEntity.getNotificationId());

        // 4. 构建广播消息内容
        BroadcastMessageContent broadcastContent = new BroadcastMessageContent();
        broadcastContent.setAppId(appId);
        broadcastContent.setCommand(SystemCommand.SYSTEM_NOTIFICATION.getCommand());
        broadcastContent.setData(pack);
        broadcastContent.setIncludeGuests(true);

        // 5. 发送到所有用户广播队列
        String messageId = UUID.randomUUID().toString();
        CorrelationData correlationData = new CorrelationData(messageId);

        try {
            rabbitTemplate.convertAndSend(
                Constants.RabbitConstants.BroadcastMessage,
                Constants.RabbitConstants.AllUserRoutingKey,
                JSONObject.toJSONString(broadcastContent),
                getMessagePostProcessor(appId, messageId),
                correlationData
            );

            logger.info("发送系统通知到MQ成功，消息ID: {}", messageId);
        } catch (Exception e) {
            logger.error("发送系统通知到MQ失败", e);
            throw new RuntimeException("发送系统通知失败", e);
        }
    }

    @Override
    @Transactional
    public void sendSystemNotificationToNormalUsers(Integer appId, SystemNotification notification) {
        logger.info("发送系统通知给普通用户: {}", JSONObject.toJSONString(notification));

        // 参数验证
        if (appId == null || notification == null) {
            throw new IllegalArgumentException("应用ID和通知内容不能为空");
        }

        // 1. 存储系统通知
        ImSystemNotificationEntity notificationEntity = saveNotification(appId, notification);

        // 2. 判断是精准推送还是广播推送
        if (notification.getReceiverIds() != null && !notification.getReceiverIds().isEmpty()) {
            // 精准推送：发送给指定的普通用户
            sendToSpecificNormalUsers(appId, notification, notificationEntity);
        } else {
            // 广播推送：发送给所有普通用户
            sendToAllNormalUsers(appId, notification, notificationEntity);
        }
    }

    /**
     * 发送给指定的普通用户（精准推送）
     */
    private void sendToSpecificNormalUsers(Integer appId, SystemNotification notification, ImSystemNotificationEntity notificationEntity) {
        logger.info("精准推送系统通知给指定普通用户，用户数量: {}", notification.getReceiverIds().size());

        // 1. 验证用户是否为普通用户（非游客）
        List<String> validNormalUserIds = validateNormalUsers(appId, notification.getReceiverIds());
        if (validNormalUserIds.isEmpty()) {
            logger.warn("没有有效的普通用户，跳过发送");
            return;
        }

        // 2. 应用排除用户逻辑
        List<String> finalUserIds = applyExcludeUsers(validNormalUserIds, notification.getExcludeUserIds());
        if (finalUserIds.isEmpty()) {
            logger.warn("应用排除用户逻辑后，没有剩余的目标用户，跳过发送");
            return;
        }

        logger.info("验证后的有效普通用户数量: {}，排除用户后的最终用户数量: {}", validNormalUserIds.size(), finalUserIds.size());

        // 3. 为有效用户创建通知关系记录（直接保存到数据库）
        if (notification.getPersistToDatabase()) {
            createUserNotificationRelations(appId, notificationEntity, finalUserIds);
            logger.debug("指定用户通知关系已保存到数据库，类型: {}，用户数量: {}", notification.getNotificationType(), finalUserIds.size());
        } else {
            logger.info("跳过数据库存储，通知类型: {} - 仅进行实时推送给指定用户，用户数量: {}", notification.getNotificationType(), finalUserIds.size());
        }

        // 4. 构建消息包并发送（包含通知ID）
        SystemNotificationPack pack = buildNotificationPack(notification, notificationEntity.getNotificationId());
        pack.setReceiverIds(finalUserIds); // 设置实际的接收者列表

        BroadcastMessageContent broadcastContent = new BroadcastMessageContent();
        broadcastContent.setAppId(appId);
        broadcastContent.setCommand(SystemCommand.SYSTEM_NOTIFICATION.getCommand());
        broadcastContent.setData(pack);
        broadcastContent.setIncludeGuests(false);

        sendNotificationMessage(appId, broadcastContent, Constants.RabbitConstants.NormalUserRoutingKey);
    }

    /**
     * 发送给所有普通用户（广播推送）
     */
    private void sendToAllNormalUsers(Integer appId, SystemNotification notification, ImSystemNotificationEntity notificationEntity) {
        logger.info("广播推送系统通知给所有普通用户");

        // 验证排除用户列表
        if (notification.getExcludeUserIds() != null && !notification.getExcludeUserIds().isEmpty()) {
            logger.info("广播通知包含排除用户列表，排除用户数量: {}", notification.getExcludeUserIds().size());
        }

        // 1. 创建普通用户通知关系记录（直接保存到数据库，排除用户逻辑在方法中处理）
        if (notification.getPersistToDatabase()) {
            createUserNotificationRelations(appId, notificationEntity, false, notification);
            logger.debug("普通用户广播通知关系已保存到数据库，类型: {}", notification.getNotificationType());
        } else {
            logger.info("跳过数据库存储，通知类型: {} - 仅进行实时广播推送给普通用户", notification.getNotificationType());
        }

        // 2. 构建广播消息包（包含通知ID）
        SystemNotificationPack pack = buildNotificationPack(notification, notificationEntity.getNotificationId());

        // 3. 构建广播消息内容
        BroadcastMessageContent broadcastContent = new BroadcastMessageContent();
        broadcastContent.setAppId(appId);
        broadcastContent.setCommand(SystemCommand.SYSTEM_NOTIFICATION.getCommand());
        broadcastContent.setData(pack);
        broadcastContent.setIncludeGuests(false);

        sendNotificationMessage(appId, broadcastContent, Constants.RabbitConstants.NormalUserRoutingKey);
    }

    /**
     * 验证用户是否为普通用户（非游客）
     */
    private List<String> validateNormalUsers(Integer appId, List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<String> validNormalUsers = new ArrayList<>();

        for (String userId : userIds) {
            try {
                // 1. 检查是否为游客（Redis中是否存在游客标识）
                String guestKey = appId + Constants.RedisConstants.GuestUserPrefix + userId;
                Boolean isGuest = redisTemplate.hasKey(guestKey);

                if (isGuest != null && isGuest) {
                    logger.debug("用户 {} 是游客，跳过", userId);
                    continue;
                }

                // 2. 检查是否为数据库中的普通用户
                QueryWrapper<ImUserDataEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("user_id", userId)
                          .eq("app_id", appId)
                          .eq("del_flag", 0);

                Integer count = imUserDataMapper.selectCount(queryWrapper);
                if (count != null && count > 0) {
                    validNormalUsers.add(userId);
                    logger.debug("用户 {} 验证为普通用户", userId);
                } else {
                    logger.debug("用户 {} 不存在或已删除，跳过", userId);
                }
            } catch (Exception e) {
                logger.error("验证用户 {} 类型失败", userId, e);
            }
        }

        return validNormalUsers;
    }

    /**
     * 创建用户通知关系记录（替代Redis缓存）
     * @param appId 应用ID
     * @param notification 通知实体
     * @param isGuest 是否为游客用户，null表示所有用户
     * @param systemNotification 系统通知请求对象
     */
    private void createUserNotificationRelations(Integer appId, ImSystemNotificationEntity notification, Boolean isGuest, SystemNotification systemNotification) {
        try {
            // 获取目标用户列表（应用排除用户逻辑）
            List<String> userIds = getUserIds(appId, isGuest, systemNotification.getExcludeUserIds());

            if (!userIds.isEmpty()) {
                // 批量创建用户通知关系
                batchSaveUserNotificationRelations(appId, notification.getNotificationId(), userIds);
                logger.info("创建用户通知关系成功，通知ID: {}, 用户数量: {}", notification.getNotificationId(), userIds.size());
            }
        } catch (Exception e) {
            logger.error("创建用户通知关系失败，通知ID: {}", notification.getNotificationId(), e);
        }
    }

    /**
     * 为指定用户创建通知关系记录
     * @param appId 应用ID
     * @param notification 通知实体
     * @param userIds 指定的用户ID列表
     */
    private void createUserNotificationRelations(Integer appId, ImSystemNotificationEntity notification, List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }
        try {
            logger.info("为 {} 个指定用户创建通知关系", userIds.size());
            // 批量创建用户通知关系
            batchSaveUserNotificationRelations(appId, notification.getNotificationId(), userIds);
            logger.info("指定用户通知关系创建成功，通知ID: {}, 用户数量: {}", notification.getNotificationId(), userIds.size());
        } catch (Exception e) {
            logger.error("为指定用户创建通知关系失败，通知ID: {}", notification.getNotificationId(), e);
        }
    }

    /**
     * 发送通知消息到MQ
     */
    private void sendNotificationMessage(Integer appId, BroadcastMessageContent broadcastContent, String routingKey) {
        String messageId = UUID.randomUUID().toString();
        CorrelationData correlationData = new CorrelationData(messageId);

        try {
            rabbitTemplate.convertAndSend(
                Constants.RabbitConstants.SystemNotification,
                routingKey,
                JSONObject.toJSONString(broadcastContent),
                getMessagePostProcessor(appId, messageId),
                correlationData
            );

            logger.info("发送系统通知到MQ成功，路由键: {}, 消息ID: {}", routingKey, messageId);
        } catch (Exception e) {
            logger.error("发送系统通知到MQ失败，路由键: {}", routingKey, e);
            throw new RuntimeException("发送系统通知失败", e);
        }
    }

    @Override
    @Transactional
    public void sendSystemNotificationToGuests(Integer appId, SystemNotification notification) {
        logger.info("发送系统通知给游客: {}", JSONObject.toJSONString(notification));

        // 参数验证
        if (appId == null || notification == null) {
            throw new IllegalArgumentException("应用ID和通知内容不能为空");
        }

        // 验证排除用户列表
        if (notification.getExcludeUserIds() != null && !notification.getExcludeUserIds().isEmpty()) {
            logger.info("游客通知包含排除用户列表，排除用户数量: {}", notification.getExcludeUserIds().size());
        }

        // 1. 存储系统通知（游客通知通常不需要持久化，仅实时推送）
        ImSystemNotificationEntity notificationEntity = saveNotification(appId, notification);

        // 2. 构建广播消息包（包含通知ID）
        SystemNotificationPack pack = buildNotificationPack(notification, notificationEntity.getNotificationId());

        // 3. 构建广播消息内容
        BroadcastMessageContent broadcastContent = new BroadcastMessageContent();
        broadcastContent.setAppId(appId);
        broadcastContent.setCommand(SystemCommand.SYSTEM_NOTIFICATION.getCommand());
        broadcastContent.setData(pack);
        broadcastContent.setOnlyForGuests(true);

        // 4. 发送到游客系统通知队列
        String messageId = UUID.randomUUID().toString();
        CorrelationData correlationData = new CorrelationData(messageId);

        try {
            rabbitTemplate.convertAndSend(
                Constants.RabbitConstants.SystemNotification,
                Constants.RabbitConstants.GuestUserRoutingKey,
                JSONObject.toJSONString(broadcastContent),
                getMessagePostProcessor(appId, messageId),
                correlationData
            );

            logger.info("发送系统通知到游客MQ成功，消息ID: {}", messageId);
        } catch (Exception e) {
            logger.error("发送系统通知到游客MQ失败", e);
            throw new RuntimeException("发送系统通知给游客失败", e);
        }
    }

    /**
     * 获取用户未读通知列表
     * 功能：仅获取用户的未读通知，用于未读通知查询场景
     * 与syncSystemNotifications的区别：
     * - 本方法只返回未读通知（onlyUnread = true）
     * - syncSystemNotifications返回所有通知（onlyUnread = false）
     * - 本方法使用简单的ResponseVO<List<T>>格式
     */
    @Override
    public ResponseVO<List<ImSystemNotificationEntity>> getUnreadNotifications(String userId, Integer appId, Long lastSequence, Integer limit) {
        try {
            List<ImSystemNotificationEntity> result = getUnreadNotificationsFromDatabase(userId, appId, lastSequence, limit);
            return ResponseVO.successResponse(result);
        } catch (Exception e) {
            logger.error("获取未读通知失败，用户: {}", userId, e);
            return ResponseVO.errorResponse(500, "获取未读通知失败");
        }
    }

    /**
     * 基于数据库的增量同步查询
     */
    private List<ImSystemNotificationEntity> getUnreadNotificationsFromDatabase(String userId, Integer appId, Long lastSequence, Integer limit) {
        List<ImSystemNotificationEntity> result = new ArrayList<>();

        try {
            // 参数验证和默认值设置
            if (lastSequence == null) {
                lastSequence = 0L;
            }
            if (limit == null || limit <= 0 || limit > 100) {
                limit = 20; // 默认限制20条，最大100条
            }

            // 1. 从数据库查询用户未读通知关系
            List<ImUserSystemNotificationEntity> userNotifications =
                userNotificationMapper.selectNotificationsForSync(appId, userId, lastSequence, limit, true);

            if (userNotifications.isEmpty()) {
                logger.debug("用户{}没有新的未读通知", userId);
                return result;
            }

            // 2. 提取通知ID列表
            List<Long> notificationIds = userNotifications.stream()
                .map(ImUserSystemNotificationEntity::getNotificationId)
                .collect(java.util.stream.Collectors.toList());

            // 3. 批量查询通知详情
            QueryWrapper<ImSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("notification_id", notificationIds)
                       .eq("del_flag", 0)
                       .orderByDesc("create_time");

            List<ImSystemNotificationEntity> notifications = systemNotificationMapper.selectList(queryWrapper);

            // 4. 按照用户通知关系的顺序重新排序
            Map<Long, ImSystemNotificationEntity> notificationMap = notifications.stream()
                .collect(java.util.stream.Collectors.toMap(
                    ImSystemNotificationEntity::getNotificationId,
                    notification -> notification
                ));

            for (ImUserSystemNotificationEntity userNotification : userNotifications) {
                ImSystemNotificationEntity notification = notificationMap.get(userNotification.getNotificationId());
                if (notification != null) {
                    result.add(notification);
                }
            }

            logger.debug("从数据库获取到{}条未读通知，用户: {}", result.size(), userId);

        } catch (Exception e) {
            logger.error("从数据库获取未读通知失败，用户: {}", userId, e);
        }

        return result;
    }



    @Override
    @Transactional
    public ResponseVO<Void> markNotificationRead(String userId, Integer appId, List<Long> notificationIds) {
        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty() || appId == null ||
                notificationIds == null || notificationIds.isEmpty()) {
                return ResponseVO.errorResponse(400, "参数不能为空");
            }

            // 更新数据库已读状态（移除Redis依赖）
            QueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                .eq("user_id", userId)
                .in("notification_id", notificationIds)
                .eq("read_status", 0); // 只更新未读的记录

            ImUserSystemNotificationEntity update = new ImUserSystemNotificationEntity();
            update.setReadStatus(1);
            update.setReadTime(System.currentTimeMillis());

            int affectedRows = userNotificationMapper.update(update, queryWrapper);
            logger.debug("标记通知已读成功，用户: {}, 影响行数: {}", userId, affectedRows);

            return ResponseVO.successResponse();
        } catch (Exception e) {
            logger.error("标记通知已读失败，用户: {}", userId, e);
            return ResponseVO.errorResponse(500, "标记通知已读失败");
        }
    }

    @Override
    public ResponseVO<Integer> getUnreadCount(String userId, Integer appId) {
        try {
            QueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                .eq("user_id", userId)
                .eq("read_status", 0);

            Integer count = userNotificationMapper.selectCount(queryWrapper);
            return ResponseVO.successResponse(count);
        } catch (Exception e) {
            logger.error("获取未读通知数量失败，用户: {}", userId, e);
            return ResponseVO.errorResponse(500, "获取未读通知数量失败");
        }
    }

    @Override
    public ResponseVO<PageResult<NotificationDetailResp>> getAllNotifications(String userId, Integer appId, Integer pageNum, Integer pageSize) {
        logger.info("获取用户所有通知列表，用户: {}, 页码: {}, 每页大小: {}", userId, pageNum, pageSize);

        try {
            // 参数验证
            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 20;
            }
            // 构建分页查询条件
            Page<ImUserSystemNotificationEntity> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ImUserSystemNotificationEntity::getAppId, appId)
                    .eq(ImUserSystemNotificationEntity::getUserId, userId)
                    .orderByDesc(ImUserSystemNotificationEntity::getCreateTime); // 按创建时间倒序

            // 执行分页查询
            IPage<ImUserSystemNotificationEntity> pageResult = userNotificationMapper.selectPage(page, queryWrapper);

            // 获取通知详情列表
            List<NotificationDetailResp> notificationList = buildNotificationDetailList(pageResult.getRecords());

            // 构建分页响应
            PageResult<NotificationDetailResp> result = PageResult.of(
                    notificationList,
                    pageResult.getTotal(),
                    pageNum,
                    pageSize
            );

            logger.info("获取用户所有通知列表成功，用户: {}, 总数: {}, 当前页: {}", userId, pageResult.getTotal(), pageNum);
            return ResponseVO.successResponse(result);

        } catch (Exception e) {
            logger.error("获取用户所有通知列表失败，用户: {}", userId, e);
            return ResponseVO.errorResponse(500, "获取通知列表失败");
        }
    }

    @Override
    public ResponseVO<List<NotificationTypeLatestResp>> getLatestNotificationsByType(String userId, Integer appId) {
        logger.info("获取用户按通知类型分组的最新通知，用户: {}", userId);

        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty() || appId == null) {
                return ResponseVO.errorResponse(400, "用户ID和应用ID不能为空");
            }

            // 第一步：获取用户有通知记录的所有通知类型列表
            List<Integer> notificationTypes = userNotificationMapper.selectUserNotificationTypes(appId, userId);

            if (notificationTypes.isEmpty()) {
                logger.info("用户{}没有任何通知记录", userId);
                return ResponseVO.successResponse(new ArrayList<NotificationTypeLatestResp>());
            }

            logger.debug("用户{}共有{}种通知类型", userId, notificationTypes.size());

            // 第二步和第三步：并行获取每种类型的最新通知和未读数量
            List<NotificationTypeLatestResp> result = notificationTypes.parallelStream()
                    .map(notificationType -> {
                        try {
                            return buildNotificationTypeResponse(appId, userId, notificationType);
                        } catch (Exception e) {
                            logger.error("构建通知类型{}响应失败，用户: {}", notificationType, userId, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(NotificationTypeLatestResp::getNotificationType))
                    .collect(java.util.stream.Collectors.toList());

            logger.info("获取用户按通知类型分组的最新通知成功，用户: {}, 类型数量: {}", userId, result.size());
            return ResponseVO.successResponse(result);

        } catch (Exception e) {
            logger.error("获取用户按通知类型分组的最新通知失败，用户: {}", userId, e);
            return ResponseVO.errorResponse(500, "获取通知类型列表失败");
        }
    }

    /**
     * 构建单个通知类型的响应对象
     */
    private NotificationTypeLatestResp buildNotificationTypeResponse(Integer appId, String userId, Integer notificationType) {
        // 获取该类型的最新通知
        NotificationTypeLatestDto latestNotification = userNotificationMapper.selectLatestNotificationByType(appId, userId, notificationType);

        if (latestNotification == null) {
            logger.warn("通知类型{}没有找到最新通知，用户: {}", notificationType, userId);
            return null;
        }

        // 获取该类型的未读数量
        Integer unreadCount = userNotificationMapper.selectUnreadCountByTypeOptimized(appId, userId, notificationType);
        if (unreadCount == null) {
            unreadCount = 0;
        }

        // 构建响应对象
        NotificationTypeLatestResp resp = new NotificationTypeLatestResp();
        resp.setNotificationType(notificationType);
        resp.setNotificationTypeName(notificationTypeConfig.getTypeName(notificationType));
        resp.setUnreadCount(unreadCount);

        // 构建最新通知详情
        NotificationDetailResp latestNotificationDetail = buildNotificationDetail(latestNotification);
        resp.setLatestNotification(latestNotificationDetail);

        return resp;
    }



    @Override
    public ResponseVO<PageResult<NotificationDetailResp>> getNotificationsByType(String userId, Integer appId, Integer notificationType, Integer pageNum, Integer pageSize) {
        logger.info("按通知类型分页查询用户通知列表，用户: {}, 类型: {}, 页码: {}, 每页大小: {}", userId, notificationType, pageNum, pageSize);

        try {
            // 参数验证
            if (userId == null || userId.trim().isEmpty() || appId == null || notificationType == null) {
                return ResponseVO.errorResponse(400, "用户ID、应用ID和通知类型不能为空");
            }

            if (pageNum == null || pageNum < 1) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize < 1 || pageSize > 100) {
                pageSize = 20;
            }

            // 验证通知类型是否有效
            if (!notificationTypeConfig.isValidType(notificationType)) {
                return ResponseVO.errorResponse(400, "无效的通知类型: " + notificationType);
            }

            // 创建分页对象
            Page<ImUserSystemNotificationEntity> page = new Page<>(pageNum, pageSize);

            // 执行分页查询
            IPage<ImUserSystemNotificationEntity> pageResult = userNotificationMapper.selectNotificationsByType(page, appId, userId, notificationType);

            // 获取通知详情列表
            List<NotificationDetailResp> notificationList = buildNotificationDetailList(pageResult.getRecords());

            // 构建分页响应
            PageResult<NotificationDetailResp> result = PageResult.of(
                    notificationList,
                    pageResult.getTotal(),
                    pageNum,
                    pageSize
            );

            logger.info("按通知类型分页查询用户通知列表成功，用户: {}, 类型: {}, 总数: {}, 当前页: {}",
                    userId, notificationType, pageResult.getTotal(), pageNum);
            return ResponseVO.successResponse(result);

        } catch (Exception e) {
            logger.error("按通知类型分页查询用户通知列表失败，用户: {}, 类型: {}", userId, notificationType, e);
            return ResponseVO.errorResponse(500, "获取通知列表失败");
        }
    }

    /**
     * 构建单个通知详情对象（从DTO转换）
     */
    private NotificationDetailResp buildNotificationDetail(NotificationTypeLatestDto dto) {
        NotificationDetailResp detail = new NotificationDetailResp();
        detail.setNotificationId(dto.getNotificationId());
        detail.setAppId(dto.getAppId());
        detail.setNotificationType(dto.getNotificationType());
        detail.setTitle(dto.getTitle());
        detail.setContent(dto.getContent());
        detail.setCreateTime(dto.getCreateTime());
        detail.setReadStatus(dto.getReadStatus());
        detail.setReadTime(dto.getReadTime());
        detail.setSequence(dto.getSequence());

        // 解析扩展字段
        if (dto.getExtra() != null && !dto.getExtra().trim().isEmpty()) {
            try {
                detail.setExtra(JSONObject.parseObject(dto.getExtra(), Map.class));
            } catch (Exception e) {
                logger.warn("解析通知扩展字段失败，通知ID: {}", dto.getNotificationId(), e);
                detail.setExtra(new HashMap<>());
            }
        } else {
            detail.setExtra(new HashMap<>());
        }

        return detail;
    }

    /**
     * 构建通知详情列表
     */
    private List<NotificationDetailResp> buildNotificationDetailList(List<ImUserSystemNotificationEntity> userNotifications) {
        if (userNotifications == null || userNotifications.isEmpty()) {
            return new ArrayList<>();
        }

        // 获取所有通知ID
        List<Long> notificationIds = userNotifications.stream()
                .map(ImUserSystemNotificationEntity::getNotificationId)
                .collect(java.util.stream.Collectors.toList());

        // 批量查询通知详情
        QueryWrapper<ImSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("notification_id", notificationIds);
        List<ImSystemNotificationEntity> notifications = systemNotificationMapper.selectList(queryWrapper);

        // 构建通知详情Map
        Map<Long, ImSystemNotificationEntity> notificationMap = notifications.stream()
                .collect(java.util.stream.Collectors.toMap(
                        ImSystemNotificationEntity::getNotificationId,
                        notification -> notification
                ));

        // 组装响应数据
        List<NotificationDetailResp> result = new ArrayList<>();
        for (ImUserSystemNotificationEntity userNotification : userNotifications) {
            ImSystemNotificationEntity notification = notificationMap.get(userNotification.getNotificationId());
            if (notification != null) {
                NotificationDetailResp detail = new NotificationDetailResp();
                detail.setNotificationId(notification.getNotificationId());
                detail.setAppId(notification.getAppId());
                detail.setNotificationType(notification.getNotificationType());
                detail.setTitle(notification.getTitle());
                detail.setContent(notification.getContent());
                detail.setCreateTime(notification.getCreateTime());
                detail.setReadStatus(userNotification.getReadStatus());
                detail.setReadTime(userNotification.getReadTime());
                detail.setSequence(userNotification.getSequence());

                // 解析扩展字段
                if (notification.getExtra() != null && !notification.getExtra().trim().isEmpty()) {
                    try {
                        detail.setExtra(JSONObject.parseObject(notification.getExtra(), Map.class));
                    } catch (Exception e) {
                        logger.warn("解析通知扩展字段失败，通知ID: {}", notification.getNotificationId(), e);
                        detail.setExtra(new HashMap<>());
                    }
                } else {
                    detail.setExtra(new HashMap<>());
                }

                result.add(detail);
            }
        }

        return result;
    }

    private ImSystemNotificationEntity saveNotification(Integer appId, SystemNotification notification) {
        // 应用特殊通知类型的规则
        applySpecialNotificationRules(notification);

        ImSystemNotificationEntity notificationEntity = new ImSystemNotificationEntity();
        notificationEntity.setNotificationId(SnowflakeIdWorker.nextId());
        notificationEntity.setAppId(appId);
        notificationEntity.setNotificationType(notification.getNotificationType());
        notificationEntity.setTitle(notification.getTitle());
        notificationEntity.setContent(notification.getContent());
        notificationEntity.setExtra(JSONObject.toJSONString(notification.getExtra()));
        notificationEntity.setCreateTime(System.currentTimeMillis());
        notificationEntity.setDelFlag(0);

        // 根据persistToDatabase参数决定是否持久化到数据库
        if (notification.getPersistToDatabase()) {
            systemNotificationMapper.insert(notificationEntity);
            logger.debug("通知已持久化到数据库，ID: {}", notificationEntity.getNotificationId());
        } else {
            logger.debug("通知跳过数据库持久化，ID: {}", notificationEntity.getNotificationId());
        }

        return notificationEntity;
    }

    /**
     * 应用特殊通知类型的规则
     * 类型5：Redis缓存支持参数传递（默认180分钟），持久化到数据库（业务需求变更）
     * 类型7：完全跳过存储，仅实时推送
     * @param notification 通知对象
     */
    private void applySpecialNotificationRules(SystemNotification notification) {
        Integer notificationType = notification.getNotificationType();

        if (notificationType != null) {
            if (notificationType == 5) {
                // 直播提醒：Redis缓存支持参数传递（默认180分钟），持久化到数据库
                if (notification.getRedisExpireMinutes() == null || notification.getRedisExpireMinutes() == 43200) {
                    notification.setRedisExpireMinutes(180);
                } 
                notification.setPersistToDatabase(true);  // 持久化到数据库
            } else if (notificationType == 7) {
                // 赛事通知：完全跳过存储，仅实时推送
                notification.setPersistToDatabase(false);
            } 
        }
    }



    private SystemNotificationPack buildNotificationPack(SystemNotification notification) {
        SystemNotificationPack pack = new SystemNotificationPack();
        pack.setTitle(notification.getTitle());
        pack.setContent(notification.getContent());
        pack.setNotificationType(notification.getNotificationType());
        pack.setExtra(notification.getExtra());
        pack.setTimestamp(System.currentTimeMillis());
        pack.setExcludeUserIds(notification.getExcludeUserIds());
        return pack;
    }

    /**
     * 构建包含通知ID的系统通知消息包
     * @param notification 系统通知请求对象
     * @param notificationId 通知ID
     * @return 包含通知ID的系统通知消息包
     */
    private SystemNotificationPack buildNotificationPack(SystemNotification notification, Long notificationId) {
        SystemNotificationPack pack = buildNotificationPack(notification);
        pack.setNotificationId(notificationId);
        return pack;
    }

    private MessagePostProcessor getMessagePostProcessor(Integer appId, String messageId) {
        return message -> {
            message.getMessageProperties().setMessageId(messageId);
            message.getMessageProperties().setAppId(appId.toString());
            message.getMessageProperties().setTimestamp(new Date());
            return message;
        };
    }
    

    



    

    




    /**
     * 批量保存用户通知关系到数据库（使用MyBatis-Plus自带批量插入）
     * @param appId 应用ID
     * @param notificationId 通知ID
     * @param userIds 用户ID列表
     */
    private void batchSaveUserNotificationRelations(Integer appId, Long notificationId, List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return;
        }

        try {
            long currentTime = System.currentTimeMillis();
            List<ImUserSystemNotificationEntity> entities = new ArrayList<>(userIds.size());

            // 批量构建实体对象
            for (String userId : userIds) {
                ImUserSystemNotificationEntity entity = new ImUserSystemNotificationEntity();
                entity.setId(SnowflakeIdWorker.nextId());
                entity.setAppId(appId);
                entity.setUserId(userId);
                entity.setNotificationId(notificationId);
                entity.setSequence(notificationId);
                entity.setReadStatus(0);
                entity.setCreateTime(currentTime);
                entities.add(entity);
            }

            // 分批处理避免单次插入数据过多
            int batchSize = 1000; // 批次大小
            for (int i = 0; i < entities.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, entities.size());
                List<ImUserSystemNotificationEntity> batch = entities.subList(i, endIndex);

                userNotificationMapper.insertBatchSomeColumn(batch);

                logger.debug("批量插入用户通知关系，批次: {}/{}, 数量: {}",
                    (i / batchSize + 1), (entities.size() + batchSize - 1) / batchSize, batch.size());
            }
        } catch (Exception e) {
            logger.error("批量保存用户通知关系失败，通知ID: {}, 用户数量: {}", notificationId, userIds.size(), e);
            throw new RuntimeException("批量保存用户通知关系失败", e);
        }
    }



    /**
     * 获取用户ID列表
     * @param appId 应用ID
     * @param isGuest 是否为游客用户，null表示所有用户
     * @return 用户ID列表
     */
    private List<String> getUserIds(Integer appId, Boolean isGuest) {
        if (isGuest != null && isGuest) {
            // 游客通常不需要存储离线通知，直接返回空列表
            return new ArrayList<>();
        } else {
            // 查询普通用户
            QueryWrapper<ImUserDataEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("user_id")
                    .eq("app_id", appId)
                    .eq("del_flag", 0);

            return imUserDataMapper.selectObjs(queryWrapper)
                    .stream()
                    .map(Object::toString)
                    .collect(java.util.stream.Collectors.toList());
        }
    }

    /**
     * 获取用户ID列表（支持排除用户）
     * @param appId 应用ID
     * @param isGuest 是否为游客用户，null表示所有用户
     * @param excludeUserIds 需要排除的用户ID列表
     * @return 用户ID列表
     */
    private List<String> getUserIds(Integer appId, Boolean isGuest, List<String> excludeUserIds) {
        List<String> allUserIds = getUserIds(appId, isGuest);
        return applyExcludeUsers(allUserIds, excludeUserIds);
    }

    /**
     * 应用排除用户逻辑
     * @param targetUserIds 目标用户ID列表
     * @param excludeUserIds 需要排除的用户ID列表
     * @return 排除指定用户后的用户ID列表
     */
    private List<String> applyExcludeUsers(List<String> targetUserIds, List<String> excludeUserIds) {
        if (targetUserIds == null || targetUserIds.isEmpty()) {
            return new ArrayList<>();
        }

        if (excludeUserIds == null || excludeUserIds.isEmpty()) {
            // 没有需要排除的用户，直接返回原列表
            return new ArrayList<>(targetUserIds);
        }

        // 转换为Set以提高查找效率
        Set<String> excludeSet = new HashSet<>(excludeUserIds);

        // 过滤掉需要排除的用户
        List<String> result = targetUserIds.stream()
                .filter(userId -> !excludeSet.contains(userId))
                .collect(java.util.stream.Collectors.toList());

        if (logger.isDebugEnabled()) {
            logger.debug("应用排除用户逻辑：原始用户数量: {}, 排除用户数量: {}, 最终用户数量: {}",
                    targetUserIds.size(), excludeUserIds.size(), result.size());
        }

        return result;
    }

    @Override
    @Transactional
    public ResponseVO<Integer> markAllNotificationsRead(String userId, Integer appId) {
        try {
            logger.info("开始执行一键全部已读操作，用户: {}, 应用: {}", userId, appId);

            // 参数验证
            if (userId == null || userId.trim().isEmpty() || appId == null) {
                ResponseVO<Integer> errorResponse = new ResponseVO<>();
                errorResponse.setCode(400);
                errorResponse.setMsg("用户ID和应用ID不能为空");
                return errorResponse;
            }

            // 1. 查询用户所有未读通知
            QueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                .eq("user_id", userId)
                .eq("read_status", 0);

            List<ImUserSystemNotificationEntity> unreadNotifications = userNotificationMapper.selectList(queryWrapper);

            if (unreadNotifications.isEmpty()) {
                logger.info("用户没有未读通知，用户: {}", userId);
                ResponseVO<Integer> successResponse = new ResponseVO<>();
                successResponse.setCode(200);
                successResponse.setMsg("success");
                successResponse.setData(0);
                return successResponse;
            }

            // 2. 批量更新数据库已读状态
            ImUserSystemNotificationEntity update = new ImUserSystemNotificationEntity();
            update.setReadStatus(1);
            update.setReadTime(System.currentTimeMillis());
            int affectedRows = userNotificationMapper.update(update, queryWrapper);



            logger.info("一键全部已读操作完成，用户: {}, 影响通知数量: {}", userId, affectedRows);
            ResponseVO<Integer> successResponse = new ResponseVO<>();
            successResponse.setCode(200);
            successResponse.setMsg("success");
            successResponse.setData(affectedRows);
            return successResponse;

        } catch (Exception e) {
            logger.error("一键全部已读操作失败，用户: {}", userId, e);
            ResponseVO<Integer> errorResponse = new ResponseVO<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("一键全部已读操作失败");
            return errorResponse;
        }
    }

    @Override
    @Transactional
    public ResponseVO<Integer> markNotificationsByTypeRead(String userId, Integer appId, Integer notificationType) {
        try {
            logger.info("开始执行分类已读操作，用户: {}, 应用: {}, 通知类型: {}", userId, appId, notificationType);

            // 参数验证
            if (userId == null || userId.trim().isEmpty() || appId == null || notificationType == null) {
                ResponseVO<Integer> errorResponse = new ResponseVO<>();
                errorResponse.setCode(400);
                errorResponse.setMsg("用户ID、应用ID和通知类型不能为空");
                return errorResponse;
            }

            // 1. 查询指定类型的未读通知
            List<ImUserSystemNotificationEntity> unreadNotifications =
                userNotificationMapper.selectUnreadNotificationsByType(appId, userId, notificationType);

            if (unreadNotifications.isEmpty()) {
                logger.info("用户没有指定类型的未读通知，用户: {}, 通知类型: {}", userId, notificationType);
                ResponseVO<Integer> successResponse = new ResponseVO<>();
                successResponse.setCode(200);
                successResponse.setMsg("success");
                successResponse.setData(0);
                return successResponse;
            }

            // 2. 批量更新数据库已读状态
            List<Long> notificationIds = unreadNotifications.stream()
                .map(ImUserSystemNotificationEntity::getNotificationId)
                .collect(java.util.stream.Collectors.toList());

            QueryWrapper<ImUserSystemNotificationEntity> updateWrapper = new QueryWrapper<>();
            updateWrapper.eq("app_id", appId)
                .eq("user_id", userId)
                .in("notification_id", notificationIds)
                .eq("read_status", 0);

            ImUserSystemNotificationEntity update = new ImUserSystemNotificationEntity();
            update.setReadStatus(1);
            update.setReadTime(System.currentTimeMillis());
            int affectedRows = userNotificationMapper.update(update, updateWrapper);



            ResponseVO<Integer> successResponse = new ResponseVO<>();
            successResponse.setCode(200);
            successResponse.setMsg("success");
            successResponse.setData(affectedRows);
            return successResponse;

        } catch (Exception e) {
            logger.error("分类已读操作失败，用户: {}, 通知类型: {}", userId, notificationType, e);
            ResponseVO<Integer> errorResponse = new ResponseVO<>();
            errorResponse.setCode(500);
            errorResponse.setMsg("分类已读操作失败");
            return errorResponse;
        }
    }

    /**
     * 标准增量同步系统通知列表
     * 功能：获取用户的所有增量通知（包括已读和未读），与会话同步保持一致的接口设计
     * 与getUnreadNotifications的区别：
     * - 本方法返回所有通知（onlyUnread = false）
     * - getUnreadNotifications只返回未读通知（onlyUnread = true）
     * - 本方法使用标准SyncResp格式，包含maxSequence和isCompleted状态
     * - 响应数据包含read_status和read_time字段
     */
    @Override
    public ResponseVO<SyncResp<NotificationDetailResp>> syncSystemNotifications(SyncReq req) {
        try {
            // 参数验证和默认值设置
            if (req.getMaxLimit() == null || req.getMaxLimit() > 100) {
                req.setMaxLimit(100);
            }
            if (req.getLastSequence() == null) {
                req.setLastSequence(0L);
            }

            // 创建标准的同步响应对象，使用NotificationDetailResp包含读取状态和时间
            SyncResp<NotificationDetailResp> resp = new SyncResp<>();

            // 1. 获取服务端当前最大序列号
            Long serverMaxSeq = getMaxSequenceForUser(req.getAppId(), req.getOperater());

            // 如果客户端的lastSequence已经是最新的，直接返回空结果
            if (req.getLastSequence() >= serverMaxSeq) {
                resp.setMaxSequence(serverMaxSeq);
                resp.setDataList(new ArrayList<>());
                resp.setCompleted(true);
                return ResponseVO.successResponse(resp);
            }

            // 2. 查询增量数据（所有通知，包括已读和未读）
            List<ImUserSystemNotificationEntity> userNotifications =
                userNotificationMapper.selectNotificationsForSync(
                    req.getAppId(), req.getOperater(), req.getLastSequence(), req.getMaxLimit(), false);

            List<NotificationDetailResp> result = new ArrayList<>();
            if (!userNotifications.isEmpty()) {
                // 3. 提取通知ID列表
                List<Long> notificationIds = userNotifications.stream()
                    .map(ImUserSystemNotificationEntity::getNotificationId)
                    .collect(java.util.stream.Collectors.toList());

                // 4. 批量查询通知详情
                QueryWrapper<ImSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
                queryWrapper.in("notification_id", notificationIds)
                           .eq("del_flag", 0)
                           .orderByAsc("create_time");

                List<ImSystemNotificationEntity> notifications = systemNotificationMapper.selectList(queryWrapper);

                // 5. 按照用户通知关系的顺序重新排序，并构建包含读取状态的响应对象
                Map<Long, ImSystemNotificationEntity> notificationMap = notifications.stream()
                    .collect(java.util.stream.Collectors.toMap(
                        ImSystemNotificationEntity::getNotificationId,
                        notification -> notification
                    ));

                for (ImUserSystemNotificationEntity userNotification : userNotifications) {
                    ImSystemNotificationEntity notification = notificationMap.get(userNotification.getNotificationId());
                    if (notification != null) {
                        // 构建包含读取状态和时间的详细响应对象
                        NotificationDetailResp detail = buildNotificationDetailFromEntities(notification, userNotification);
                        result.add(detail);
                    }
                }
            }

            resp.setDataList(result);

            // 6. 设置当前批次的最大序列号作为maxSequence
            Long currentBatchMaxSeq = serverMaxSeq;
            if (!result.isEmpty()) {
                // 获取最后一条记录的序列号作为当前批次最大值
                Long lastSequence = userNotifications.get(userNotifications.size() - 1).getSequence();
                currentBatchMaxSeq = lastSequence;
                // 判断是否同步完成：当前批次最后一条记录的序列号 >= 服务端最大序列号
                resp.setCompleted(lastSequence >= serverMaxSeq);
            } else {
                // 没有查询到数据，说明已经同步完成
                resp.setCompleted(true);
            }

            resp.setMaxSequence(currentBatchMaxSeq);

            logger.debug("系统通知增量同步成功，用户: {}, 返回数据: {}条, 当前批次最大序列号: {}, 服务端最大序列号: {}, 同步完成: {}",
                req.getOperater(), result.size(), currentBatchMaxSeq, serverMaxSeq, resp.isCompleted());

            return ResponseVO.successResponse(resp);

        } catch (Exception e) {
            logger.error("系统通知增量同步失败，用户: {}", req.getOperater(), e);
            return ResponseVO.errorResponse(500, "系统通知增量同步失败");
        }
    }

    /**
     * 获取用户的最大序列号
     */
    private Long getMaxSequenceForUser(Integer appId, String userId) {
        try {
            QueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                       .eq("user_id", userId)
                       .orderByDesc("sequence")
                       .last("LIMIT 1");

            ImUserSystemNotificationEntity entity = userNotificationMapper.selectOne(queryWrapper);
            return entity != null ? entity.getSequence() : 0L;
        } catch (Exception e) {
            logger.error("获取用户最大序列号失败，用户: {}", userId, e);
            return 0L;
        }
    }

    /**
     * 从系统通知实体和用户通知关系实体构建通知详情响应对象
     * @param notification 系统通知实体
     * @param userNotification 用户通知关系实体
     * @return 包含读取状态和时间的通知详情响应对象
     */
    private NotificationDetailResp buildNotificationDetailFromEntities(ImSystemNotificationEntity notification,
                                                                      ImUserSystemNotificationEntity userNotification) {
        NotificationDetailResp detail = new NotificationDetailResp();
        detail.setNotificationId(notification.getNotificationId());
        detail.setAppId(notification.getAppId());
        detail.setNotificationType(notification.getNotificationType());
        detail.setTitle(notification.getTitle());
        detail.setContent(notification.getContent());
        detail.setCreateTime(notification.getCreateTime());

        // 设置读取状态和时间（来自用户通知关系表）
        detail.setReadStatus(userNotification.getReadStatus());
        detail.setReadTime(userNotification.getReadTime());
        detail.setSequence(userNotification.getSequence());

        // 解析扩展字段
        if (notification.getExtra() != null && !notification.getExtra().trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> extraMap = JSONObject.parseObject(notification.getExtra(), Map.class);
                detail.setExtra(extraMap);
            } catch (Exception e) {
                logger.warn("解析通知扩展字段失败，通知ID: {}", notification.getNotificationId(), e);
                detail.setExtra(new HashMap<>());
            }
        } else {
            detail.setExtra(new HashMap<>());
        }

        return detail;
    }
}