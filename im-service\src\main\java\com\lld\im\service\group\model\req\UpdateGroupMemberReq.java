package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 更新群成员信息请求
 */
@ApiModel(description = "更新群成员信息请求模型")
@Data
public class UpdateGroupMemberReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要更新成员的群组唯一标识")
    @NotBlank(message = "群id不能为空")
    private String groupId;

    @ApiModelProperty(value = "成员用户ID", required = true, example = "user456", notes = "要更新信息的群成员用户ID")
    @NotBlank(message = "memberId不能为空")
    private String memberId;

    @ApiModelProperty(value = "群内昵称", example = "小明", notes = "成员在群内的显示昵称")
    private String alias;

    @ApiModelProperty(value = "群内角色", example = "2", notes = "1-群主 2-管理员 3-普通成员")
    private Integer role;

    @ApiModelProperty(value = "扩展字段", example = "{\"level\": \"vip\"}", notes = "成员的扩展信息")
    private String extra;

}
