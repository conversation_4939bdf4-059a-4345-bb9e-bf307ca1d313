package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 移除群成员请求
 */
@ApiModel(description = "移除群成员请求模型")
@Data
public class RemoveGroupMemberReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要移除成员的群组唯一标识")
    @NotBlank(message = "群id不能为空")
    private String groupId;

    @ApiModelProperty(value = "成员用户ID", required = true, example = "user456", notes = "要移除的群成员用户ID")
    private String memberId;

}
