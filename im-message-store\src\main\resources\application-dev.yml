# 开发环境配置 - 单机模式
# 适用于本地开发环境，使用单机 Redis 和 RabbitMQ

spring:
  # === MySQL 数据源配置 ===
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: root
    url: ************************************************************************************************************************************************************************
    username: root

    # HikariCP 连接池配置 - 开发环境
    hikari:
      pool-name: IMMessageStoreDevHikariCP
      minimum-idle: 3                    # 开发环境较小的最小空闲连接数
      maximum-pool-size: 10              # 开发环境较小的最大连接池大小
      connection-timeout: 30000          # 连接超时时间（30秒）
      idle-timeout: 300000               # 空闲超时时间（5分钟）
      max-lifetime: 900000               # 连接最大生命周期（15分钟）
      connection-test-query: SELECT 1    # 连接测试查询
      leak-detection-threshold: 30000    # 连接泄漏检测阈值（30秒）
      register-mbeans: true              # 启用 JMX 监控

  # === Redis 单机配置 (Redisson) ===
  redis:
    # 单机模式配置
    host: localhost
    port: 6379
    password: 123456
    database: 0
    timeout: 3000

    # Redisson 特定配置（单机模式）
    redisson:
      # 连接池配置 - 开发环境小规模
      connection-pool:
        pool-size: 50
        min-idle: 5

      # 超时配置
      connect-timeout: 3000
      retry-attempts: 3
      retry-interval: 1000

  # === RabbitMQ 单机配置 ===
  rabbitmq:
    # 单机模式配置
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    
    # 连接配置
    connection-timeout: 30000
    requested-heartbeat: 30

    # 发布者配置
    publisher-confirm-type: correlated
    publisher-returns: true

    # 消费者配置
    listener:
      simple:
        concurrency: 3
        max-concurrency: 5
        acknowledge-mode: MANUAL
        prefetch: 1
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000
          multiplier: 2.0
          max-interval: 10000
        default-requeue-rejected: false

    # 模板配置
    template:
      mandatory: true
      receive-timeout: 5000
      reply-timeout: 5000
      retry:
        enabled: true
        max-attempts: 3
        initial-interval: 1000

    # 缓存配置
    cache:
      connection:
        mode: channel
        # 注意：当 mode 为 channel 时，不能配置 connection.size
      channel:
        size: 25
        checkout-timeout: 5000

# 开发环境特定配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
