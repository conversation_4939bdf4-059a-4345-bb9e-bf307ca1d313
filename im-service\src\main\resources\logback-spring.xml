<?xml version="1.0" encoding="UTF-8" ?>

<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->

<configuration scan="true" scanPeriod="10 seconds" >

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="springAppEnv" source="spring.profiles.active"/>
    <!--     <springProperty scope="context" name="logFile" source="logging.file"/> -->

    <property name="logFile" value="logs/im-service"/>
    <!--日志在工程中的输出位置-->
    <property name="LOG_FILE" value="${logFile}"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!--控制台的日志输出样式-->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>


    <!--控制台 Appender-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>DEBUG</level>
        </filter>
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>


    <!--隐藏服务发现后输出的日志-->
    <logger name="com.netflix.discovery.shared.resolver.aws.ConfigClusterResolver" level="WARN"/>
    <logger name="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping" level="WARN"/>

    <!-- Redis和RabbitMQ连接监听器日志配置 -->
    <logger name="com.lld.im.service.config.RedisConfig" level="INFO"/>
    <logger name="com.lld.im.service.config.RabbitMQConnectionMonitor" level="INFO"/>

    <!-- Spring Data Redis 和 AMQP 连接日志 - 减少冗余输出 -->
    <logger name="org.springframework.data.redis.connection" level="WARN"/>
    <logger name="org.springframework.data.redis.core" level="WARN"/>
    <logger name="org.springframework.amqp.rabbit.connection" level="INFO"/>
    <logger name="com.rabbitmq.client" level="INFO"/>

    <!-- 禁用 Redis 操作的详细日志 -->
    <logger name="org.springframework.data.redis.connection.DefaultedRedisConnection" level="ERROR"/>
    <logger name="org.redisson" level="WARN"/>
    <logger name="io.lettuce" level="WARN"/>




    <springProfile name="test,dev">
        <!--文件-->
        <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <Prudent>true</Prudent>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <FileNamePattern>
                    ${logFile}.%d{yyyy-MM-dd}.log
                </FileNamePattern>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <!-- 开发环境连接日志级别 -->
        <logger name="com.lld.im.service.config.RedisConfig" level="INFO"/>
        <logger name="com.lld.im.service.config.LazyRedisConfig" level="INFO"/>
        <logger name="com.lld.im.service.config.RabbitMQConnectionMonitor" level="INFO"/>

        <!-- 开发环境 MySQL 连接池和 JDBC 日志配置 -->
        <logger name="com.zaxxer.hikari" level="DEBUG"/>
        <logger name="com.zaxxer.hikari.HikariConfig" level="DEBUG"/>
        <logger name="com.zaxxer.hikari.HikariDataSource" level="DEBUG"/>
        <logger name="com.zaxxer.hikari.pool" level="DEBUG"/>
        <logger name="com.zaxxer.hikari.pool.HikariPool" level="DEBUG"/>
        <logger name="com.zaxxer.hikari.pool.PoolBase" level="DEBUG"/>
        <logger name="com.mysql.cj.jdbc" level="DEBUG"/>
        <logger name="com.mysql.cj.jdbc.Driver" level="DEBUG"/>
        <logger name="com.mysql.cj.jdbc.ConnectionImpl" level="DEBUG"/>
        <logger name="org.springframework.jdbc" level="DEBUG"/>
        <logger name="org.springframework.jdbc.core" level="DEBUG"/>
        <logger name="org.springframework.jdbc.datasource" level="DEBUG"/>
        <logger name="com.baomidou.mybatisplus" level="DEBUG"/>

        <root level="DEBUG">
            <appender-ref ref="console"/>
            <appender-ref ref="fileAppender"/>
        </root>
    </springProfile>
    <springProfile name="prod">
        <!--文件-->
        <appender name="fileAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <Prudent>true</Prudent>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <FileNamePattern>
                    ${logFile}.%d{yyyy-MM-dd}.log
                </FileNamePattern>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </appender>
        <!-- 生产环境连接日志级别 - 保持INFO级别以便监控 -->
        <logger name="com.lld.im.service.config.RedisConfig" level="INFO"/>
        <logger name="com.lld.im.service.config.LazyRedisConfig" level="INFO"/>
        <logger name="com.lld.im.service.config.RabbitMQConnectionMonitor" level="INFO"/>
        <logger name="com.lld.im.service.config.MySQLConnectionMonitor" level="INFO"/>

        <!-- 生产环境 MySQL 连接池和 JDBC 日志配置 - 保守设置 -->
        <logger name="com.zaxxer.hikari" level="WARN"/>
        <logger name="com.zaxxer.hikari.HikariConfig" level="WARN"/>
        <logger name="com.zaxxer.hikari.HikariDataSource" level="INFO"/>
        <logger name="com.zaxxer.hikari.pool" level="WARN"/>
        <logger name="com.zaxxer.hikari.pool.HikariPool" level="WARN"/>
        <logger name="com.zaxxer.hikari.pool.PoolBase" level="WARN"/>
        <logger name="com.mysql.cj.jdbc" level="WARN"/>
        <logger name="com.mysql.cj.jdbc.Driver" level="WARN"/>
        <logger name="com.mysql.cj.jdbc.ConnectionImpl" level="WARN"/>
        <logger name="org.springframework.jdbc" level="WARN"/>
        <logger name="org.springframework.jdbc.core" level="WARN"/>
        <logger name="org.springframework.jdbc.datasource" level="WARN"/>
        <logger name="com.baomidou.mybatisplus" level="WARN"/>

        <root level="WARN">
            <appender-ref ref="console"/>
            <appender-ref ref="fileAppender"/>
        </root>
    </springProfile>

</configuration>