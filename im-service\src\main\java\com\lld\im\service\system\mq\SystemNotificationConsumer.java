package com.lld.im.service.system.mq;

import com.alibaba.fastjson.JSON;
import com.lld.im.common.model.message.ImSystemNotificationMessage;
import com.lld.im.service.system.model.req.SystemNotification;
import com.lld.im.service.system.service.SystemNotificationService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 系统通知消息消费者
 * 基于cursor_mq.md文档中的设计模式实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class SystemNotificationConsumer {

    @Autowired
    private SystemNotificationService systemNotificationService;

    /**
     * 消费系统通知消息
     * 根据targetType调用不同的服务方法处理系统通知
     * 
     * @param message MQ消息体
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息标签
     */
    @RabbitListener(queues = "${im.mq.rabbitmq.notification.queue}")
    public void onMessage(ImSystemNotificationMessage message,
                          Channel channel,
                          @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("Received system notification, messageId: {}, targetType: {}, title: {}", 
                    message.getMessageId(), message.getTargetType(), message.getTitle());

            // 消息验证
            if (!validateMessage(message)) {
                log.error("Invalid system notification message: {}", message);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            // 转换为系统通知对象
            SystemNotification notification = convertToSystemNotification(message);

            // 根据目标类型调用不同的服务方法
            switch (message.getTargetType()) {
                case 1: // 所有用户
                    systemNotificationService.sendSystemNotification(message.getAppId(), notification);
                    break;
                case 2: // 普通用户
                    systemNotificationService.sendSystemNotificationToNormalUsers(message.getAppId(), notification);
                    break;
                case 3: // 游客用户
                    systemNotificationService.sendSystemNotificationToGuests(message.getAppId(), notification);
                    break;
                default:
                    log.warn("Unknown target type: {}, using default (all users)", message.getTargetType());
                    // 默认发送给所有用户
                    systemNotificationService.sendSystemNotification(message.getAppId(), notification);
                    break;
            }

            // 确认消息
            channel.basicAck(deliveryTag, false);

            log.info("Process system notification success, messageId: {}", message.getMessageId());
        } catch (Exception e) {
            log.error("Process system notification failed, messageId: {}", 
                     message != null ? message.getMessageId() : "unknown", e);
            handleError(e, message, channel, deliveryTag);
        }
    }

    /**
     * 将MQ消息转换为系统通知对象
     * 按照cursor_mq.md文档中的转换模式实现
     * 
     * @param message MQ消息
     * @return 系统通知对象
     */
    private SystemNotification convertToSystemNotification(ImSystemNotificationMessage message) {
        SystemNotification notification = new SystemNotification();
        notification.setTitle(message.getTitle());
        notification.setContent(message.getContent());
        notification.setNotificationType(message.getNotificationType());
        notification.setReceiverIds(message.getReceiverIds());

        // 合并扩展字段
        if (message.getExtra() != null) {
            notification.setExtra(message.getExtra());
        }

        return notification;
    }

    /**
     * 验证消息有效性
     * 
     * @param message 消息对象
     * @return 是否有效
     */
    private boolean validateMessage(ImSystemNotificationMessage message) {
        if (message == null) {
            return false;
        }
        
        if (message.getAppId() == null || message.getAppId() <= 0) {
            log.error("Invalid appId: {}", message.getAppId());
            return false;
        }
        
        if (message.getTitle() == null || message.getTitle().trim().isEmpty()) {
            log.error("Title is empty");
            return false;
        }
        
        if (message.getContent() == null || message.getContent().trim().isEmpty()) {
            log.error("Content is empty");
            return false;
        }
        
        if (message.getNotificationType() == null) {
            log.error("NotificationType is null");
            return false;
        }
        
        // 验证targetType范围
        if (message.getTargetType() != null && 
            (message.getTargetType() < 1 || message.getTargetType() > 3)) {
            log.error("Invalid targetType: {}", message.getTargetType());
            return false;
        }
        
        return true;
    }

    /**
     * 错误处理和重试机制
     * 按照cursor_mq.md文档中的错误处理模式实现
     * 
     * @param e 异常
     * @param message 消息
     * @param channel 通道
     * @param deliveryTag 消息标签
     */
    private void handleError(Exception e, ImSystemNotificationMessage message, 
                           Channel channel, long deliveryTag) {
        try {
            // 判断是否为可重试的错误
            if (isRetryableError(e)) {
                // 可重试错误：拒绝消息并重新入队
                log.warn("Retryable error occurred, requeue message. Error: {}", e.getMessage());
                channel.basicNack(deliveryTag, false, true);
            } else {
                // 不可重试错误：拒绝消息但不重新入队（发送到死信队列）
                log.error("Non-retryable error occurred, reject message. Error: {}", e.getMessage());
                channel.basicNack(deliveryTag, false, false);
            }
        } catch (IOException ex) {
            log.error("Failed to handle error for message: {}", 
                     message != null ? message.getMessageId() : "unknown", ex);
        }
    }

    /**
     * 判断是否为可重试的错误
     * 
     * @param e 异常
     * @return 是否可重试
     */
    private boolean isRetryableError(Exception e) {
        // 网络相关异常可重试
        if (e instanceof java.net.SocketException || 
            e instanceof java.net.ConnectException ||
            e instanceof java.util.concurrent.TimeoutException) {
            return true;
        }
        
        // 数据库连接异常可重试
        if (e.getMessage() != null && 
            (e.getMessage().contains("connection") || 
             e.getMessage().contains("timeout") ||
             e.getMessage().contains("Connection refused"))) {
            return true;
        }
        
        // 参数验证错误不可重试
        if (e instanceof IllegalArgumentException || 
            e instanceof javax.validation.ValidationException) {
            return false;
        }
        
        // 默认可重试
        return true;
    }
}
