package com.lld.message.config;

import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

/**
 * MySQL 连接监控器 - im-message-store
 * 用于检查 MySQL 数据源配置和连接状态
 */
@Component
public class MySQLConnectionMonitor implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(MySQLConnectionMonitor.class);

    // 构造函数，用于验证 Bean 是否被创建
    public MySQLConnectionMonitor() {
        logger.info("🔧 MySQLConnectionMonitor Bean 正在创建...");
    }

    @Autowired
    private DataSource dataSource;

    @Autowired
    private Environment environment;

    @Override
    public void run(String... args) throws Exception {
        logger.info("=== IM-MESSAGE-STORE MySQL 连接信息检查开始 ===");

        try {
            // 获取当前环境
            String activeProfile = String.join(",", environment.getActiveProfiles());
            logger.info("当前环境: {}", activeProfile);

            // 检查数据源类型
            logger.info("数据源类型: {}", dataSource.getClass().getSimpleName());

            // 如果是 HikariCP，显示连接池信息
            if (dataSource instanceof HikariDataSource) {
                HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
                logger.info("=== HikariCP 连接池配置信息 ===");
                logger.info("连接池名称: {}", hikariDataSource.getPoolName());
                logger.info("JDBC URL: {}", hikariDataSource.getJdbcUrl());
                logger.info("用户名: {}", hikariDataSource.getUsername());
                logger.info("最大连接池大小: {}", hikariDataSource.getMaximumPoolSize());
                logger.info("最小空闲连接数: {}", hikariDataSource.getMinimumIdle());
                logger.info("连接超时时间: {}ms", hikariDataSource.getConnectionTimeout());
                logger.info("空闲超时时间: {}ms", hikariDataSource.getIdleTimeout());
                logger.info("连接最大生命周期: {}ms", hikariDataSource.getMaxLifetime());
                logger.info("连接泄漏检测阈值: {}ms", hikariDataSource.getLeakDetectionThreshold());
                logger.info("JMX 监控: {}", hikariDataSource.isRegisterMbeans());

                // 显示当前连接池状态
                logger.info("=== HikariCP 连接池状态 ===");
                try {
                    if (hikariDataSource.getHikariPoolMXBean() != null) {
                        logger.info("活跃连接数: {}", hikariDataSource.getHikariPoolMXBean().getActiveConnections());
                        logger.info("空闲连接数: {}", hikariDataSource.getHikariPoolMXBean().getIdleConnections());
                        logger.info("总连接数: {}", hikariDataSource.getHikariPoolMXBean().getTotalConnections());
                        logger.info("等待连接的线程数: {}", hikariDataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
                    } else {
                        logger.warn("HikariCP MXBean 尚未初始化，连接池可能正在启动中");
                    }
                } catch (Exception e) {
                    logger.warn("获取连接池状态失败: {}", e.getMessage());
                }
            }

            // 测试数据库连接
            logger.info("开始测试MySQL连接...");
            long startTime = System.currentTimeMillis();

            try (Connection connection = dataSource.getConnection()) {
                logger.info("连接状态: 已连接");
                logger.info("数据库产品名称: {}", connection.getMetaData().getDatabaseProductName());
                logger.info("数据库版本: {}", connection.getMetaData().getDatabaseProductVersion());
                logger.info("驱动名称: {}", connection.getMetaData().getDriverName());
                logger.info("驱动版本: {}", connection.getMetaData().getDriverVersion());

                // 执行简单查询测试
                try (PreparedStatement stmt = connection.prepareStatement("SELECT 1")) {
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            logger.info("连接测试查询结果: {}", rs.getInt(1));
                        }
                    }
                }

                long endTime = System.currentTimeMillis();
                logger.info("✅ MySQL连接测试成功 - 总耗时: {}ms", (endTime - startTime));

            } catch (Exception e) {
                logger.error("❌ MySQL连接测试失败: {}", e.getMessage(), e);
            }

        } catch (Exception e) {
            logger.error("❌ MySQL连接信息检查失败: {}", e.getMessage(), e);
        }

        logger.info("=== IM-MESSAGE-STORE MySQL 连接信息检查完成 ===");
    }
}