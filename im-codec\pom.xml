<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>im-system</artifactId>
        <groupId>com.lld</groupId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>im-codec</artifactId>

    <properties>
        <java.version>1.8</java.version>
    </properties>
    <dependencies>

        <!-- netty -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.35.Final</version>
        </dependency>

        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.51</version>
        </dependency>

        <!-- common -->
        <dependency>
            <groupId>com.lld</groupId>
            <artifactId>im-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
        </plugins>
    </build>


</project>