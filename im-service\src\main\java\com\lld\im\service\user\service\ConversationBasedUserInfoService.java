package com.lld.im.service.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.DelFlagEnum;
import com.lld.im.common.enums.FriendShipStatusEnum;
import com.lld.im.service.conversation.dao.ImConversationSetEntity;
import com.lld.im.service.conversation.dao.mapper.ImConversationSetMapper;
import com.lld.im.service.friendship.dao.ImFriendShipEntity;
import com.lld.im.service.friendship.dao.mapper.ImFriendShipMapper;
import com.lld.im.service.user.dao.ImUserDataEntity;
import com.lld.im.service.user.dao.mapper.ImUserDataMapper;
import com.lld.im.service.user.model.req.GetUserInfoByConversationReq;
import com.lld.im.service.user.model.resp.GetUserInfoByConversationResp;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 基于会话关系的用户信息服务
 * 
 * <AUTHOR>
 */
@Service
public class ConversationBasedUserInfoService {

    private static final Logger logger = LoggerFactory.getLogger(ConversationBasedUserInfoService.class);

    @Autowired
    private ImUserDataMapper imUserDataMapper;

    @Autowired
    private ImConversationSetMapper imConversationSetMapper;

    @Autowired
    private ImFriendShipMapper imFriendShipMapper;

    /**
     * 关系类型常量
     */
    private static final int RELATION_TYPE_FRIEND = 1;      // 好友关系
    private static final int RELATION_TYPE_CONVERSATION = 2; // 会话关系
    private static final int RELATION_TYPE_BOTH = 3;        // 好友+会话关系

    /**
     * 基于会话关系获取用户信息
     * 
     * @param req 请求参数
     * @return 用户信息响应
     */
    public ResponseVO<GetUserInfoByConversationResp> getUserInfoByConversation(GetUserInfoByConversationReq req) {
        try {
            logger.info("开始基于会话关系获取用户信息，请求用户：{}，目标用户数量：{}", 
                    req.getRequestUserId(), req.getTargetUserIds().size());

            GetUserInfoByConversationResp response = new GetUserInfoByConversationResp();
            response.setUserDataList(new ArrayList<>());
            response.setRelationTypeMap(new HashMap<>());
            response.setNoPermissionUsers(new ArrayList<>());
            response.setNotExistUsers(new ArrayList<>());

            // 1. 检查目标用户是否存在
            Map<String, ImUserDataEntity> existingUsersMap = checkUsersExistence(req.getTargetUserIds(), req.getAppId());
            
            // 记录不存在的用户
            Set<String> existingUserIds = existingUsersMap.keySet();
            for (String targetUserId : req.getTargetUserIds()) {
                if (!existingUserIds.contains(targetUserId)) {
                    response.getNotExistUsers().add(targetUserId);
                }
            }

            // 2. 检查好友关系
            Map<String, Boolean> friendRelationMap = new HashMap<>();
            if (req.getIncludeFriends()) {
                friendRelationMap = checkFriendRelations(req.getRequestUserId(), existingUserIds, req.getAppId());
            }

            // 3. 检查会话关系
            Map<String, Boolean> conversationRelationMap = new HashMap<>();
            if (req.getIncludeConversations()) {
                conversationRelationMap = checkConversationRelations(req.getRequestUserId(), existingUserIds, req.getAppId());
            }

            // 4. 确定每个用户的关系类型和权限
            for (String targetUserId : existingUserIds) {
                boolean isFriend = friendRelationMap.getOrDefault(targetUserId, false);
                boolean hasConversation = conversationRelationMap.getOrDefault(targetUserId, false);

                // 确定关系类型
                int relationType = 0;
                if (isFriend && hasConversation) {
                    relationType = RELATION_TYPE_BOTH;
                } else if (isFriend) {
                    relationType = RELATION_TYPE_FRIEND;
                } else if (hasConversation) {
                    relationType = RELATION_TYPE_CONVERSATION;
                }

                // 检查是否有权限获取用户信息
                boolean hasPermission = false;
                if (req.getIncludeFriends() && isFriend) {
                    hasPermission = true;
                }
                if (req.getIncludeConversations() && hasConversation) {
                    hasPermission = true;
                }

                if (hasPermission) {
                    // 有权限，添加用户信息
                    ImUserDataEntity userInfo = existingUsersMap.get(targetUserId);
                    response.getUserDataList().add(userInfo);
                    response.getRelationTypeMap().put(targetUserId, relationType);
                } else {
                    // 无权限
                    response.getNoPermissionUsers().add(targetUserId);
                }
            }

            logger.info("基于会话关系获取用户信息完成，成功获取：{}个，无权限：{}个，不存在：{}个", 
                    response.getUserDataList().size(), 
                    response.getNoPermissionUsers().size(), 
                    response.getNotExistUsers().size());

            return ResponseVO.successResponse(response);

        } catch (Exception e) {
            logger.error("基于会话关系获取用户信息失败，请求用户：{}", req.getRequestUserId(), e);
            return ResponseVO.errorResponse(500, "获取用户信息失败");
        }
    }

    /**
     * 检查用户是否存在
     */
    private Map<String, ImUserDataEntity> checkUsersExistence(List<String> userIds, Integer appId) {
        QueryWrapper<ImUserDataEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("app_id", appId)
                   .in("user_id", userIds)
                   .eq("del_flag", DelFlagEnum.NORMAL.getCode());

        List<ImUserDataEntity> userList = imUserDataMapper.selectList(queryWrapper);
        return userList.stream().collect(Collectors.toMap(ImUserDataEntity::getUserId, user -> user));
    }

    /**
     * 检查好友关系
     */
    private Map<String, Boolean> checkFriendRelations(String requestUserId, Set<String> targetUserIds, Integer appId) {
        Map<String, Boolean> relationMap = new HashMap<>();
        
        // 初始化所有用户为无好友关系
        for (String userId : targetUserIds) {
            relationMap.put(userId, false);
        }

        try {
            // 查询好友关系
            QueryWrapper<ImFriendShipEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                       .eq("from_id", requestUserId)
                       .in("to_id", targetUserIds)
                       .eq("status", FriendShipStatusEnum.FRIEND_STATUS_NORMAL.getCode());

            List<ImFriendShipEntity> friendships = imFriendShipMapper.selectList(queryWrapper);
            
            // 更新好友关系状态
            for (ImFriendShipEntity friendship : friendships) {
                relationMap.put(friendship.getToId(), true);
            }

        } catch (Exception e) {
            logger.error("检查好友关系失败，请求用户：{}", requestUserId, e);
        }

        return relationMap;
    }

    /**
     * 检查会话关系
     */
    private Map<String, Boolean> checkConversationRelations(String requestUserId, Set<String> targetUserIds, Integer appId) {
        Map<String, Boolean> relationMap = new HashMap<>();
        
        // 初始化所有用户为无会话关系
        for (String userId : targetUserIds) {
            relationMap.put(userId, false);
        }

        try {
            // 构建会话ID列表（单聊会话）
            List<String> conversationIds = new ArrayList<>();
            for (String targetUserId : targetUserIds) {
                // 单聊会话ID格式：0_fromId_toId
                String conversationId = ConversationTypeEnum.P2P.getCode() + "_" + requestUserId + "_" + targetUserId;
                conversationIds.add(conversationId);
            }

            // 查询会话关系
            QueryWrapper<ImConversationSetEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("app_id", appId)
                       .eq("from_id", requestUserId)
                       .in("conversation_id", conversationIds)
                       .eq("conversation_type", ConversationTypeEnum.P2P.getCode());

            List<ImConversationSetEntity> conversations = imConversationSetMapper.selectList(queryWrapper);
            
            // 更新会话关系状态
            for (ImConversationSetEntity conversation : conversations) {
                // 从会话ID中解析出目标用户ID
                String[] parts = conversation.getConversationId().split("_");
                if (parts.length == 3) {
                    String targetUserId = parts[2]; // 0_requestUserId_targetUserId
                    relationMap.put(targetUserId, true);
                }
            }

        } catch (Exception e) {
            logger.error("检查会话关系失败，请求用户：{}", requestUserId, e);
        }

        return relationMap;
    }
}
