package com.lld.im.service.friendship.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 好友信息DTO
 */
@ApiModel(description = "好友信息数据传输对象")
@Data
public class FriendDto {

    @ApiModelProperty(value = "好友用户ID", required = true, example = "user456", notes = "要添加的好友用户唯一标识")
    private String toId;

    @ApiModelProperty(value = "好友备注", example = "小明", notes = "对好友的备注名称")
    private String remark;

    @ApiModelProperty(value = "添加来源", example = "search", notes = "添加好友的来源：search-搜索添加 qr-扫码添加 phone-手机号添加")
    private String addSource;

    @ApiModelProperty(value = "扩展字段", example = "{\"tag\": \"colleague\"}", notes = "好友的扩展信息")
    private String extra;

    @ApiModelProperty(value = "添加好友申请理由", example = "我是张三，想加您为好友", notes = "发送好友申请时的验证消息")
    private String addWording;

}
