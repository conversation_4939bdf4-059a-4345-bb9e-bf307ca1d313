package com.lld.im.service.config;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * IM MQ消费者配置类
 * 基于cursor_mq.md文档中的设计模式，为消费者创建队列和绑定关系
 * 
 * <AUTHOR>
 */
@Configuration
public class ImMqConsumerConfig {

    @Value("${im.mq.rabbitmq.notification.exchange}")
    private String notificationExchange;

    @Value("${im.mq.rabbitmq.notification.queue}")
    private String notificationQueue;

    @Value("${im.mq.rabbitmq.notification.routing-key}")
    private String notificationRoutingKey;

    @Value("${im.mq.rabbitmq.liveroom.exchange}")
    private String liveroomExchange;

    @Value("${im.mq.rabbitmq.liveroom.queue}")
    private String liveroomQueue;

    @Value("${im.mq.rabbitmq.liveroom.routing-key}")
    private String liveroomRoutingKey;

    /**
     * 系统通知交换机
     * 按照cursor_mq.md文档中的交换机设计
     */
    @Bean
    public TopicExchange imNotificationExchange() {
        return new TopicExchange(notificationExchange, true, false);
    }

    /**
     * 直播间消息交换机
     * 按照cursor_mq.md文档中的交换机设计
     */
    @Bean
    public TopicExchange imLiveroomExchange() {
        return new TopicExchange(liveroomExchange, true, false);
    }

    /**
     * 系统通知队列
     * 消费者监听此队列接收系统通知消息
     */
    @Bean
    public Queue imNotificationQueue() {
        return QueueBuilder.durable(notificationQueue)
                .build();
    }

    /**
     * 直播间消息队列
     * 消费者监听此队列接收直播间消息
     */
    @Bean
    public Queue imLiveroomQueue() {
        return QueueBuilder.durable(liveroomQueue)
                .build();
    }

    /**
     * 系统通知队列绑定
     * 将系统通知队列绑定到系统通知交换机
     */
    @Bean
    public Binding imNotificationBinding() {
        return BindingBuilder.bind(imNotificationQueue())
                .to(imNotificationExchange())
                .with(notificationRoutingKey);
    }

    /**
     * 直播间消息队列绑定
     * 将直播间消息队列绑定到直播间消息交换机
     */
    @Bean
    public Binding imLiveroomBinding() {
        return BindingBuilder.bind(imLiveroomQueue())
                .to(imLiveroomExchange())
                .with(liveroomRoutingKey);
    }
}
