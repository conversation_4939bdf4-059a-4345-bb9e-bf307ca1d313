package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 退出群组请求
 * @description: 用户主动退出群组的请求模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "退出群组请求模型")
@Data
public class ExitGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要退出的群组唯一标识")
    @NotBlank(message = "群组ID不能为空")
    private String groupId;

    @ApiModelProperty(value = "退出原因", example = "个人原因", notes = "用户退出群组的原因说明，可选字段，最多200字符")
    @Size(max = 200, message = "退出原因不能超过200个字符")
    private String exitReason;

    @ApiModelProperty(value = "扩展字段", example = "{\"source\": \"user_action\"}", notes = "扩展信息，JSON格式")
    private String extra;
}
