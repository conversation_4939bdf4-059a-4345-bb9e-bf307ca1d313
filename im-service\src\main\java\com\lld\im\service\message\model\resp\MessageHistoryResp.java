package com.lld.im.service.message.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 消息历史记录响应
 * @description: 历史消息查询的响应模型
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "消息历史记录响应模型")
@Data
public class MessageHistoryResp {

    @ApiModelProperty(value = "消息ID", example = "123456789", notes = "消息的唯一标识")
    private Long messageKey;

    @ApiModelProperty(value = "发送者ID", example = "user123", notes = "消息发送者的用户ID")
    private String fromId;

    @ApiModelProperty(value = "接收者ID", example = "user456", notes = "消息接收者的用户ID")
    private String toId;

    @ApiModelProperty(value = "消息内容", example = "Hello, this is a test message", notes = "消息的具体内容")
    private String messageBody;

    @ApiModelProperty(value = "消息时间", example = "1640995200000", notes = "消息发送时间戳，单位毫秒")
    private Long messageTime;

    @ApiModelProperty(value = "创建时间", example = "1640995200000", notes = "消息创建时间戳，单位毫秒")
    private Long createTime;

    @ApiModelProperty(value = "消息序列号", example = "1001", notes = "消息的序列号，用于排序")
    private Long sequence;

    @ApiModelProperty(value = "消息随机数", example = "12345", notes = "消息的随机数，用于去重")
    private Integer messageRandom;

    @ApiModelProperty(value = "会话ID", example = "user1|user2", notes = "消息所属的会话ID")
    private String conversationId;

    @ApiModelProperty(value = "会话类型", example = "1", notes = "会话类型：0-单聊，1-群聊")
    private Integer conversationType;

    @ApiModelProperty(value = "消息类型", example = "1", notes = "消息类型：1-文本，2-图片，3-语音，4-视频等")
    private String messageType;

    @ApiModelProperty(value = "扩展字段", example = "{\"type\": \"text\"}", notes = "消息的扩展信息")
    private String extra;

}