package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 审批群申请请求
 */
@ApiModel(description = "审批群申请请求模型")
@Data
public class ApproveGroupApplyReq extends RequestBase {

    @ApiModelProperty(value = "申请ID", required = true, example = "123456", notes = "要审批的申请记录唯一标识")
    @NotNull(message = "申请ID不能为空")
    private Long applyId;

    @ApiModelProperty(value = "审批结果", required = true, example = "1", notes = "审批结果：1-同意 2-拒绝")
    @NotNull(message = "审批结果不能为空")
    private Integer approveResult;

    @ApiModelProperty(value = "拒绝理由", example = "群已满员，暂时无法加入", notes = "拒绝申请时的理由说明，最多500字符")
    @Size(max = 500, message = "拒绝理由不能超过500个字符")
    private String rejectReason;

    @ApiModelProperty(value = "扩展字段", example = "{\"notifyType\": \"push\"}", notes = "扩展信息，JSON格式")
    private String extra;
}
