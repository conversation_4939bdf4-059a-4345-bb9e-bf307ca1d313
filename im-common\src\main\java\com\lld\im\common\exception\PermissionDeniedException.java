package com.lld.im.common.exception;

public class PermissionDeniedException extends RuntimeException {
    private int code;

    private String error;
    public int getCode() {
        return code;
    }

    public String getError() {
        return error;
    }

    public PermissionDeniedException(int code, String message) {
        super(message);
        this.code = code;
        this.error = message;
    }
    @Override
    public Throwable fillInStackTrace() {
        return this;
    }
}
