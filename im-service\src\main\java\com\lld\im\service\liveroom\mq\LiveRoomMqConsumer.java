package com.lld.im.service.liveroom.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lld.im.common.constant.Constants;
import com.lld.im.service.liveroom.model.req.*;
import com.lld.im.service.liveroom.service.LiveRoomService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 直播间MQ消费者，监听im2LiveRoomService队列
 */
@Slf4j
@Component
public class LiveRoomMqConsumer {
    @Autowired
    private LiveRoomService liveRoomService;

    @RabbitListener(
        bindings = @QueueBinding(
            value = @Queue(value = Constants.RabbitConstants.Im2LiveRoomService, durable = "true"),
            exchange = @Exchange(value = Constants.RabbitConstants.Im2LiveRoomService, durable = "true")
        ),
        concurrency = "1"
    )
    public void onLiveRoomMessage(@Payload Message message,
                                  @Headers Map<String, Object> headers,
                                  Channel channel) throws Exception {
        String msg = new String(message.getBody(), "utf-8");
        Long deliveryTag = (Long) headers.get(AmqpHeaders.DELIVERY_TAG);
        try {

            JSONObject data = JSON.parseObject(msg);
            int command = data.getIntValue("command");
            //JSONObject data = json.getJSONObject("data");
            log.info("[LiveRoomMqConsumer] 收到业务指令: command={}, data={}", command, data);
            switch (command) {
                case 5010: // LIVE_ROOM_MSG
                    SendLiveRoomMsgReq msgReq = data.toJavaObject(SendLiveRoomMsgReq.class);
                    liveRoomService.sendMessage(msgReq);
                    break;
                case 5012: // LIVE_ROOM_JOIN
                    JoinLiveRoomReq joinReq = data.toJavaObject(JoinLiveRoomReq.class);
                    liveRoomService.joinLiveRoom(joinReq);
                    break;
                case 5013: // LIVE_ROOM_LEAVE
                    LeaveLiveRoomReq leaveReq = data.toJavaObject(LeaveLiveRoomReq.class);
                    liveRoomService.leaveLiveRoom(leaveReq);
                    break;
                case 5014: // LIVE_ROOM_MUTE
                    MuteUserReq muteUserReq = data.toJavaObject(MuteUserReq.class);
                    liveRoomService.muteUser(muteUserReq);
                    break;
                case 5015: // LIVE_ROOM_KICK
                    KickUserReq kickUserReq = data.toJavaObject(KickUserReq.class);
                    liveRoomService.kickUser(kickUserReq);
                    break;
                case 5016: // LIVE_ROOM_ANNOUNCEMENT
                    UpdateAnnouncementReq annReq = data.toJavaObject(UpdateAnnouncementReq.class);
                    liveRoomService.updateAnnouncement(annReq);
                    break;
                case 5017: // LIVE_ROOM_MUTE_ALL
                    MuteAllReq muteAllReq = data.toJavaObject(MuteAllReq.class);
                    liveRoomService.muteAll(muteAllReq);
                    break;
                case 5020: // LIVE_ROOM_CREATE
                    CreateLiveRoomReq createReq = data.toJavaObject(CreateLiveRoomReq.class);
                    liveRoomService.createLiveRoom(createReq);
                    break;
                case 5021: // LIVE_ROOM_CLOSE
                    CloseLiveRoomReq closeReq = data.toJavaObject(CloseLiveRoomReq.class);
                    liveRoomService.closeLiveRoom(closeReq);
                    break;
                default:
                    log.warn("[LiveRoomMqConsumer] 未知指令: {}", command);
            }
            channel.basicAck(deliveryTag, false);
        } catch (Exception e) {
            log.error("[LiveRoomMqConsumer] 处理业务指令异常", e);
            channel.basicNack(deliveryTag, false, false);
        }
    }
} 