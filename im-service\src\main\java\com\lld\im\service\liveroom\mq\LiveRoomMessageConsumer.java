package com.lld.im.service.liveroom.mq;


import com.lld.im.common.model.message.ImLiveRoomMessage;
import com.lld.im.service.liveroom.model.req.SendLiveRoomMsgReq;
import com.lld.im.service.liveroom.service.LiveRoomService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 直播间消息消费者
 * 基于cursor_mq.md文档中的设计模式实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class LiveRoomMessageConsumer {

    @Autowired
    private LiveRoomService liveRoomService;

    /**
     * 消费直播间消息
     * 将MQ消息转换为SendLiveRoomMsgReq并调用现有服务处理
     * 
     * @param message MQ消息体
     * @param channel RabbitMQ通道
     * @param deliveryTag 消息标签
     */
    @RabbitListener(queues = "${im.mq.rabbitmq.liveroom.queue}")
    public void onMessage(ImLiveRoomMessage message,
                          Channel channel,
                          @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        try {
            log.info("Received live room message, messageId: {}, roomId: {}, type: {}", 
                    message.getMessageId(), message.getRoomId(), message.getMessageType());

            // 消息验证
            if (!validateMessage(message)) {
                log.error("Invalid live room message: {}", message);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            // 转换为发送消息请求对象
            SendLiveRoomMsgReq req = convertToSendMsgReq(message);

            // 调用现有服务发送消息
            liveRoomService.sendMessage(req);

            // 确认消息
            channel.basicAck(deliveryTag, false);

            log.info("Process live room message success, messageId: {}", message.getMessageId());
        } catch (Exception e) {
            log.error("Process live room message failed, messageId: {}", 
                     message != null ? message.getMessageId() : "unknown", e);
            handleError(e, message, channel, deliveryTag);
        }
    }

    /**
     * 将MQ消息转换为发送消息请求对象
     * 按照cursor_mq.md文档中的转换模式实现
     * 
     * @param message MQ消息
     * @return 发送消息请求对象
     */
    private SendLiveRoomMsgReq convertToSendMsgReq(ImLiveRoomMessage message) {
        SendLiveRoomMsgReq req = new SendLiveRoomMsgReq();
        req.setAppId(message.getAppId());
        req.setRoomId(message.getRoomId());
        req.setFromId(message.getFromId());
        req.setFromNickname(message.getFromNickname());
        req.setFromAvatar(message.getFromAvatar());
        req.setMessageType(message.getMessageType());
        req.setContent(message.getContent());
        req.setClientType(message.getClientType());
        req.setImei(message.getImei());

        // 合并扩展字段
        if (message.getExtra() != null) {
            req.setExtra(message.getExtra());
        }

        return req;
    }

    /**
     * 验证消息有效性
     * 
     * @param message 消息对象
     * @return 是否有效
     */
    private boolean validateMessage(ImLiveRoomMessage message) {
        if (message == null) {
            return false;
        }
        
        if (message.getAppId() == null || message.getAppId() <= 0) {
            log.error("Invalid appId: {}", message.getAppId());
            return false;
        }
        
        if (message.getRoomId() == null || message.getRoomId().trim().isEmpty()) {
            log.error("RoomId is empty");
            return false;
        }
        
        if (message.getFromId() == null || message.getFromId().trim().isEmpty()) {
            log.error("FromId is empty");
            return false;
        }
        
        if (message.getMessageType() == null) {
            log.error("MessageType is null");
            return false;
        }
        
        if (message.getContent() == null || message.getContent().trim().isEmpty()) {
            log.error("Content is empty");
            return false;
        }
        
        // 验证消息类型范围 (1-10)
        if (message.getMessageType() < 1 || message.getMessageType() > 10) {
            log.error("Invalid messageType: {}", message.getMessageType());
            return false;
        }
        
        return true;
    }

    /**
     * 错误处理和重试机制
     * 按照cursor_mq.md文档中的错误处理模式实现
     * 
     * @param e 异常
     * @param message 消息
     * @param channel 通道
     * @param deliveryTag 消息标签
     */
    private void handleError(Exception e, ImLiveRoomMessage message, 
                           Channel channel, long deliveryTag) {
        try {
            // 判断是否为可重试的错误
            if (isRetryableError(e)) {
                // 可重试错误：拒绝消息并重新入队
                log.warn("Retryable error occurred, requeue message. Error: {}", e.getMessage());
                channel.basicNack(deliveryTag, false, true);
            } else {
                // 不可重试错误：拒绝消息但不重新入队（发送到死信队列）
                log.error("Non-retryable error occurred, reject message. Error: {}", e.getMessage());
                channel.basicNack(deliveryTag, false, false);
            }
        } catch (IOException ex) {
            log.error("Failed to handle error for message: {}", 
                     message != null ? message.getMessageId() : "unknown", ex);
        }
    }

    /**
     * 判断是否为可重试的错误
     * 
     * @param e 异常
     * @return 是否可重试
     */
    private boolean isRetryableError(Exception e) {
        // 网络相关异常可重试
        if (e instanceof java.net.SocketException || 
            e instanceof java.net.ConnectException ||
            e instanceof java.util.concurrent.TimeoutException) {
            return true;
        }
        
        // 数据库连接异常可重试
        if (e.getMessage() != null && 
            (e.getMessage().contains("connection") || 
             e.getMessage().contains("timeout") ||
             e.getMessage().contains("Connection refused"))) {
            return true;
        }
        
        // 直播间不存在等业务异常不可重试
        if (e.getMessage() != null && 
            (e.getMessage().contains("直播间不存在") || 
             e.getMessage().contains("用户被禁言") ||
             e.getMessage().contains("直播间已关闭"))) {
            return false;
        }
        
        // 参数验证错误不可重试
        if (e instanceof IllegalArgumentException || 
            e instanceof javax.validation.ValidationException) {
            return false;
        }
        
        // 默认可重试
        return true;
    }
}
