package com.lld.im.service.liveroom.model.resp;

import lombok.Data;

import java.util.Date;

/**
 * 直播间信息响应
 */
@Data
public class LiveRoomResp {

    /**
     * 直播间ID
     */
    private String roomId;

    /**
     * 直播间名称
     */
    private String roomName;

    /**
     * 直播间封面
     */
    private String roomCover;

    /**
     * 直播间状态 0-未开播 1-直播中 2-已结束
     */
    private Integer status;

    /**
     * 主播ID
     */
    private String anchorId;

    /**
     * 主播昵称
     */
    private String anchorName;

    /**
     * 主播头像
     */
    private String anchorAvatar;

    /**
     * 直播间公告
     */
    private String announcement;

    /**
     * 是否开启禁言 0-否 1-是
     */
    private Integer muteAll;

    /**
     * 在线人数
     */
    private Integer onlineCount;

    /**
     * 最大在线人数
     */
    private Integer maxOnlineCount;

    /**
     * 创建时间
     */
    private Date createTime;
} 