package com.lld.im.service.group.service;

import com.lld.im.codec.pack.message.ChatMessageAck;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.command.GroupEventCommand;
import com.lld.im.common.exception.ApplicationException;
import com.lld.im.common.enums.command.MessageCommand;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.message.GroupChatMessageContent;
import com.lld.im.common.model.message.MessageContent;
import com.lld.im.common.model.message.OfflineMessageContent;
import com.lld.im.service.conversation.service.ConversationService;
import com.lld.im.service.group.model.req.SendGroupMessageReq;
import com.lld.im.service.group.service.impl.ImGroupServiceImpl;
import com.lld.im.service.message.model.resp.SendMessageResp;
import com.lld.im.service.message.service.CheckSendMessageService;
import com.lld.im.service.message.service.MessageStoreService;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.utils.MessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
public class GroupMessageService {

    private static final Logger logger = LoggerFactory.getLogger(GroupMessageService.class);


    @Autowired
    CheckSendMessageService checkSendMessageService;

    @Autowired
    MessageProducer messageProducer;

    @Autowired
    ImGroupMemberService imGroupMemberService;

    @Autowired
    MessageStoreService messageStoreService;

    @Autowired
    RedisSeq redisSeq;

    @Autowired
    ConversationService conversationService;

    private final ThreadPoolExecutor threadPoolExecutor;

    {
        AtomicInteger num = new AtomicInteger(0);
        threadPoolExecutor = new ThreadPoolExecutor(8, 8, 60, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000), new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setDaemon(true);
                thread.setName("message-group-thread-" + num.getAndIncrement());
                return thread;
            }
        });
    }

    public void process(GroupChatMessageContent messageContent){
        String fromId = messageContent.getFromId();
        String groupId = messageContent.getGroupId();
        Integer appId = messageContent.getAppId();

        // 前置校验：权限验证
        ResponseVO permissionCheck = checkSendMessageService.checkGroupMessage(fromId, groupId, appId);
        if (!permissionCheck.isOk()) {
            // 权限验证失败，回复错误ACK
            ack(messageContent, permissionCheck);
            return;
        }

        GroupChatMessageContent messageFromMessageIdCache = messageStoreService.getMessageFromMessageIdCache(messageContent.getAppId(),
                messageContent.getMessageId(), GroupChatMessageContent.class);
        if(messageFromMessageIdCache != null){
            threadPoolExecutor.execute(() ->{
                //1.回ack成功给自己
                ack(messageContent,ResponseVO.successResponse());
                //2.发消息给同步在线端
                syncToSender(messageContent,messageContent);
                //3.发消息给对方在线端
                dispatchMessage(messageContent);
            });
            return;
        }
        long seq = redisSeq.doGetSeq(messageContent.getAppId() + ":" + Constants.SeqConstants.GroupMessage
                + messageContent.getGroupId());
        messageContent.setMessageSequence(seq);

        // 确保messageTime字段正确设置
        if (messageContent.getMessageTime() == null || messageContent.getMessageTime() == 0) {
            messageContent.setMessageTime(System.currentTimeMillis());
        }

            threadPoolExecutor.execute(() ->{
                messageStoreService.storeGroupMessage(messageContent);

                List<String> groupMemberId = imGroupMemberService.getGroupMemberId(messageContent.getGroupId(),
                        messageContent.getAppId());
                messageContent.setMemberId(groupMemberId);

                OfflineMessageContent offlineMessageContent = new OfflineMessageContent();
                BeanUtils.copyProperties(messageContent,offlineMessageContent);
                offlineMessageContent.setToId(messageContent.getGroupId());
                messageStoreService.storeGroupOfflineMessage(offlineMessageContent,groupMemberId);

                // 更新群聊会话记录 - 为所有群成员创建或更新会话记录
                conversationService.updateConversationSetForGroup(messageContent, groupMemberId);

                //1.回ack成功给自己
                ack(messageContent,ResponseVO.successResponse());
                //2.发消息给同步在线端
                syncToSender(messageContent,messageContent);
                //3.发消息给对方在线端
                dispatchMessage(messageContent);

                messageStoreService.setMessageFromMessageIdCache(messageContent.getAppId(),
                        messageContent.getMessageId(),messageContent);
            });
    }

    private void dispatchMessage(GroupChatMessageContent messageContent){
        for (String memberId : messageContent.getMemberId()) {
            if(!memberId.equals(messageContent.getFromId())){
                messageProducer.sendToUser(memberId,
                        GroupEventCommand.MSG_GROUP,
                        messageContent,messageContent.getAppId());
            }
        }
    }

    private void ack(MessageContent messageContent,ResponseVO responseVO){


        ChatMessageAck chatMessageAck = new ChatMessageAck();
        // 设置基本属性
        chatMessageAck.setMessageId(messageContent.getMessageId());
        chatMessageAck.setMessageSequence(messageContent.getMessageSequence());
        chatMessageAck.setMessageKey(messageContent.getMessageKey());

        // 设置其他属性
        chatMessageAck.setFromId(messageContent.getFromId());
        chatMessageAck.setToId(messageContent.getToId());
        chatMessageAck.setMessageBody(messageContent.getMessageBody());
        chatMessageAck.setMessageTime(messageContent.getMessageTime());
        chatMessageAck.setExtra(messageContent.getExtra());

        // 设置会话类型为群聊
        chatMessageAck.setConversationType(ConversationTypeEnum.GROUP.getCode());

        // 设置群聊会话ID：格式为 1_fromId_groupId
        if (messageContent instanceof GroupChatMessageContent) {
            GroupChatMessageContent groupMessage = (GroupChatMessageContent) messageContent;
            String conversationId = ConversationTypeEnum.GROUP.getCode() + "_" + messageContent.getFromId() + "_" + groupMessage.getGroupId();
            chatMessageAck.setConversationId(conversationId);
            chatMessageAck.setToId(groupMessage.getGroupId());
        }

        responseVO.setData(chatMessageAck);
        //發消息
        messageProducer.sendToUser(messageContent.getFromId(),
                GroupEventCommand.GROUP_MSG_ACK,
                responseVO,messageContent
        );
    }

    private void syncToSender(GroupChatMessageContent messageContent, ClientInfo clientInfo){
        messageProducer.sendToUserExceptClient(messageContent.getFromId(),
                GroupEventCommand.MSG_GROUP,messageContent,messageContent);
    }

    private ResponseVO imServerPermissionCheck(String fromId, String toId,Integer appId){
        ResponseVO responseVO = checkSendMessageService
                .checkGroupMessage(fromId, toId,appId);
        return responseVO;
    }

    public SendMessageResp send(SendGroupMessageReq req) {

        // 权限验证
        ResponseVO permissionCheck = checkSendMessageService.checkGroupMessage(
            req.getFromId(), req.getGroupId(), req.getAppId());
        if (!permissionCheck.isOk()) {
            throw new ApplicationException(permissionCheck.getCode(), permissionCheck.getMsg());
        }

        SendMessageResp sendMessageResp = new SendMessageResp();
        GroupChatMessageContent message = new GroupChatMessageContent();
        BeanUtils.copyProperties(req,message);

        // 确保messageTime字段正确设置
        if (message.getMessageTime() == null || message.getMessageTime() == 0) {
            message.setMessageTime(System.currentTimeMillis());
        }

        // 确保sequence字段正确设置
        if (message.getMessageSequence() == 0) {
            long seq = redisSeq.doGetSeq(message.getAppId() + ":" + Constants.SeqConstants.GroupMessage
                    + message.getGroupId());
            message.setMessageSequence(seq);
        }

        messageStoreService.storeGroupMessage(message);

        sendMessageResp.setMessageKey(message.getMessageKey());
        sendMessageResp.setMessageTime(message.getMessageTime());

        List<String> groupMemberId = imGroupMemberService.getGroupMemberId(message.getGroupId(),
                message.getAppId());
        message.setMemberId(groupMemberId);

        // 更新群聊会话记录 - 为所有群成员创建或更新会话记录
        conversationService.updateConversationSetForGroup(message, groupMemberId);

        //2.发消息给同步在线端
        syncToSender(message,message);
        //3.发消息给对方在线端
        dispatchMessage(message);

        return sendMessageResp;

    }

    /**
     * 处理系统消息（绕过权限验证）
     * 专门用于系统级别的群聊消息，如群名称修改通知等
     * @param messageContent 系统消息内容
     */
    public void processSystemMessage(GroupChatMessageContent messageContent) {
        logger.info("开始处理系统消息: messageId={}, groupId={}, fromId={}",
                messageContent.getMessageId(), messageContent.getGroupId(), messageContent.getFromId());

        try {
            // 检查消息缓存
            GroupChatMessageContent messageFromMessageIdCache = messageStoreService.getMessageFromMessageIdCache(
                    messageContent.getAppId(), messageContent.getMessageId(), GroupChatMessageContent.class);
            if(messageFromMessageIdCache != null){
                logger.info("系统消息命中缓存，直接分发: messageId={}", messageContent.getMessageId());
                threadPoolExecutor.execute(() ->{
                    //1.回ack成功给自己（系统消息不需要ACK，但保持一致性）
                    ack(messageContent, ResponseVO.successResponse());
                    //2.发消息给同步在线端
                    syncToSender(messageContent, messageContent);
                    //3.发消息给对方在线端
                    dispatchMessage(messageContent);
                });
                return;
            }

            // 生成消息序列号
            long seq = redisSeq.doGetSeq(messageContent.getAppId() + ":" + Constants.SeqConstants.GroupMessage
                    + messageContent.getGroupId());
            messageContent.setMessageSequence(seq);

            // 确保messageTime字段正确设置
            if (messageContent.getMessageTime() == null || messageContent.getMessageTime() == 0) {
                messageContent.setMessageTime(System.currentTimeMillis());
            }

            // 异步处理系统消息
            threadPoolExecutor.execute(() -> {
                try {
                    logger.info("开始异步处理系统消息: messageId={}", messageContent.getMessageId());

                    // 1. 存储消息到数据库
                    messageStoreService.storeGroupMessage(messageContent);

                    // 2. 获取群成员列表
                    List<String> groupMemberId = imGroupMemberService.getGroupMemberId(
                            messageContent.getGroupId(), messageContent.getAppId());
                    messageContent.setMemberId(groupMemberId);

                    logger.info("系统消息群成员数量: {}, messageId={}", groupMemberId.size(), messageContent.getMessageId());

                    // 3. 存储离线消息
                    OfflineMessageContent offlineMessageContent = new OfflineMessageContent();
                    BeanUtils.copyProperties(messageContent, offlineMessageContent);
                    offlineMessageContent.setToId(messageContent.getGroupId());
                    messageStoreService.storeGroupOfflineMessage(offlineMessageContent, groupMemberId);

                    // 4. 更新群聊会话记录
                    conversationService.updateConversationSetForGroup(messageContent, groupMemberId);

                    // 5. 发送消息给在线用户
                    //   系统消息不需要ACK回复，直接分发
                    syncToSender(messageContent, messageContent);
                    dispatchMessage(messageContent);

                    // 6. 设置消息缓存
                    messageStoreService.setMessageFromMessageIdCache(messageContent.getAppId(),
                            messageContent.getMessageId(), messageContent);

                    logger.info("系统消息处理完成: messageId={}, 分发给{}个用户",
                            messageContent.getMessageId(), groupMemberId.size());

                } catch (Exception e) {
                    logger.error("系统消息处理异常: messageId={}, error={}",
                            messageContent.getMessageId(), e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            logger.error("系统消息处理失败: messageId={}, error={}",
                    messageContent.getMessageId(), e.getMessage(), e);
        }
    }
}
