package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 申请加群请求
 */
@ApiModel(description = "申请加群请求模型")
@Data
public class ApplyJoinGroupReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要申请加入的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "申请理由", example = "希望加入技术交流群，学习新技术", notes = "申请加群的理由说明，最多500字符")
    @Size(max = 500, message = "{validation.apply.reason.size}")
    private String applyReason;

    @ApiModelProperty(value = "扩展字段", example = "{\"source\": \"search\", \"referrer\": \"user456\"}", notes = "扩展信息，JSON格式")
    private String extra;
}
