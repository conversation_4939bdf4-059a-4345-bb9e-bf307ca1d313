package com.lld.im.service.liveroom.model.dto;

import lombok.Data;

/**
 * 用户加入直播间数据包
 */
@Data
public class LiveRoomJoinedPack {
    
    /**
     * 直播间ID
     */
    private String roomId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 是否为游客
     */
    private Boolean isGuest;
    
    /**
     * 加入时间戳
     */
    private Long timestamp = System.currentTimeMillis();
} 