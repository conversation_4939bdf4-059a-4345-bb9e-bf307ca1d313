package com.lld.im.codec;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.proto.Message;
import com.lld.im.codec.proto.MessageHeader;
import com.lld.im.codec.utils.ByteBufToMessageUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.ByteToMessageDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * @description: 消息解码类
 * @author: lld
 * @version: 1.0
 */
public class MessageDecoder extends ByteToMessageDecoder {

    private static final Logger logger = LoggerFactory.getLogger(MessageDecoder.class);

    @Override
    protected void decode(ChannelHandlerContext ctx,
                          ByteBuf in, List<Object> out) throws Exception {
        //请求头（指令
        // 版本
        // clientType
        // 消息解析类型
        // appId
        // imei长度
        // bodylen）+ imei号 + 请求体

        try {
            // 确保至少有一个消息头的长度
            if(in.readableBytes() < 28){
                return;
            }

            // 标记当前读取位置
            in.markReaderIndex();
            
            Message message = ByteBufToMessageUtils.transition(in);
            if(message == null){
                // 如果解析失败但没有异常，只是静默返回
                return;
            }

            out.add(message);
        } catch (Exception e) {
            logger.error("消息解码异常", e);
            // 发生异常时，尝试清空缓冲区以恢复正常通信
            in.skipBytes(in.readableBytes());
        }
    }
}
