package com.lld.im.codec.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: Chackylee
 * @description:
 **/
@Data
public class BootstrapConfig {

    private TcpConfig lim;

    private LiveroomConfig liveroom;

    @Data
    public static class TcpConfig {
        private Integer tcpPort;// tcp 绑定的端口号

        private Integer webSocketPort; // webSocket 绑定的端口号

        private boolean enableWebSocket; //是否启用webSocket

        private Integer bossThreadSize; // boss线程 默认=1

        private Integer workThreadSize; //work线程

        private Long heartBeatTime; //心跳超时时间 单位毫秒

        private Integer loginModel;

        /**
         * redis配置
         */
        private RedisConfig redis;

        /**
         * rabbitmq配置
         */
        private Rabbitmq rabbitmq;

        /**
         * Nacos配置
         */
        private NacosConfig nacosConfig;

        /**
         * brokerId
         */
        private Integer brokerId;

        private String logicUrl;

    }

    @Data
    public static class NacosConfig {
        /**
         * Nacos服务器地址
         */
        private String serverAddr = "127.0.0.1:8848";

        /**
         * Nacos命名空间
         */
        private String namespace = "im-system";

        /**
         * Nacos分组
         */
        private String group = "DEFAULT_GROUP";

        /**
         * Nacos用户名
         */
        private String username;

        /**
         * Nacos密码
         */
        private String password;

        /**
         * 连接超时时间
         */
        private Integer connectTimeout = 3000;

        /**
         * 读取超时时间
         */
        private Integer readTimeout = 5000;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RedisConfig {

        /**
         * 单机模式：single 哨兵模式：sentinel 集群模式：cluster
         */
        private String mode;
        /**
         * 数据库
         */
        private Integer database;
        /**
         * 密码
         */
        private String password;
        /**
         * 超时时间
         */
        private Integer timeout;
        /**
         * 最小空闲数
         */
        private Integer poolMinIdle;
        /**
         * 连接超时时间(毫秒)
         */
        private Integer poolConnTimeout;
        /**
         * 连接池大小
         */
        private Integer poolSize;

        /**
         * redis单机配置
         */
        private RedisSingle single;

        /**
         * redis集群配置
         */
        private RedisCluster cluster;

        /**
         * redis哨兵配置
         */
        private RedisSentinel sentinel;

    }

    /**
     * redis单机配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RedisSingle {
        /**
         * 地址
         */
        private String address;
    }

    /**
     * redis集群配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RedisCluster {
        /**
         * 集群节点地址列表
         */
        private List<String> nodes;
        /**
         * 最大重定向次数（保留字段，当前Redisson版本使用默认值）
         */
        @Builder.Default
        private Integer maxRedirects = 3;
        /**
         * 扫描间隔时间（毫秒）
         */
        @Builder.Default
        private Integer scanInterval = 1000;
    }

    /**
     * redis哨兵配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RedisSentinel {
        /**
         * 主节点名称
         */
        private String masterName;
        /**
         * 哨兵节点地址列表
         */
        private List<String> sentinels;
        /**
         * 故障转移超时时间（毫秒）（保留字段，当前Redisson版本使用默认值）
         */
        @Builder.Default
        private Integer failoverTimeout = 2000;
    }

    /**
     * rabbitmq配置 - 支持单机和集群模式
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Rabbitmq {
        // 单机模式配置（向后兼容）
        private String host;
        private Integer port;

        // 集群模式配置
        private List<Address> addresses;

        // 通用配置
        private String virtualHost;
        private String userName;
        private String password;

        @Builder.Default
        private Integer connectionTimeout = 5000;
        @Builder.Default
        private Integer requestedHeartbeat = 30;
        @Builder.Default
        private Integer networkRecoveryInterval = 5000;
        @Builder.Default
        private Boolean automaticRecoveryEnabled = true;

        /**
         * RabbitMQ集群节点地址
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Address {
            private String host;
            private Integer port;
        }
    }

    /*    # 直播间分片配置
    liveroom:
    sharding:
    enabled: true
    shardCount: 4
    strategy: roomId*/

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LiveroomConfig {
        private ShardingConfig sharding;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ShardingConfig {
        private Boolean enabled;
        private Integer shardCount;
        private String strategy;
    }
}
