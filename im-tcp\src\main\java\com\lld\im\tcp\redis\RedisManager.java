package com.lld.im.tcp.redis;

import com.lld.im.codec.config.BootstrapConfig;
import com.lld.im.tcp.reciver.UserLoginMessageListener;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Redis连接管理器 - 支持单机、集群、哨兵模式
 *
 * @author: lld
 * @version: 1.0
 */
public class RedisManager {

    private static final Logger logger = LoggerFactory.getLogger(RedisManager.class);

    private static RedissonClient redissonClient;
    private static Integer loginModel;

    // Redis客户端策略映射
    private static final Map<String, RedisClientStrategy> strategies = new HashMap<>();

    static {
        strategies.put("single", new SingleClientStrategy());
        strategies.put("cluster", new ClusterClientStrategy());
        strategies.put("sentinel", new SentinelClientStrategy());
    }

    /**
     * 初始化Redis连接 - 根据配置自动选择连接策略
     */
    public static void init(BootstrapConfig config){
        logger.info("初始化Redis连接管理器...");

        loginModel = config.getLim().getLoginModel();

        // 获取Redis模式
        String mode = config.getLim().getRedis().getMode();
        if (mode == null) {
            mode = "single"; // 默认单机模式
        }

        logger.info("Redis连接模式: {}", mode);

        // 获取对应的策略
        RedisClientStrategy strategy = strategies.get(mode.toLowerCase());
        if (strategy == null) {
            logger.warn("不支持的Redis模式: {}，使用默认单机模式", mode);
            strategy = strategies.get("single");
        }

        // 创建Redis连接
        try {
            redissonClient = strategy.getRedissonClient(config.getLim().getRedis());
            logger.info("Redis连接创建成功，策略: {}", strategy.getStrategyName());
        } catch (Exception e) {
            logger.error("Redis连接创建失败", e);
            throw new RuntimeException("Redis连接初始化失败", e);
        }

        // 初始化用户登录消息监听器
        UserLoginMessageListener userLoginMessageListener = new UserLoginMessageListener(loginModel);
        userLoginMessageListener.listenerUserLogin();

        logger.info("Redis连接管理器初始化完成");
    }

    /**
     * 获取RedissonClient实例
     */
    public static RedissonClient getRedissonClient(){
        if (redissonClient == null) {
            throw new IllegalStateException("RedisManager未初始化，请先调用init方法");
        }
        return redissonClient;
    }

    /**
     * 检查Redis连接状态
     */
    public static boolean isConnected() {
        try {
            return redissonClient != null && !redissonClient.isShutdown();
        } catch (Exception e) {
            logger.error("检查Redis连接状态失败", e);
            return false;
        }
    }

    /**
     * 关闭Redis连接
     */
    public static void shutdown() {
        if (redissonClient != null && !redissonClient.isShutdown()) {
            logger.info("关闭Redis连接...");
            redissonClient.shutdown();
            logger.info("Redis连接已关闭");
        }
    }

}
