package com.lld.im.tcp.task;

import com.lld.im.common.constant.Constants;
import com.lld.im.tcp.config.AppConfig;
import com.lld.im.tcp.redis.RedisManager;
import com.lld.im.tcp.utils.SessionSocketHolder;
import io.netty.channel.socket.nio.NioSocketChannel;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * WebSocket连接清理任务
 * 负责定期清理无效的WebSocket连接，防止内存泄漏和重复处理失效用户
 * 
 * <AUTHOR>
 */
@Slf4j
public class ConnectionCleanupTask {
    
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(
        AppConfig.Cleanup.CLEANUP_THREAD_POOL_SIZE, r -> {
            Thread t = new Thread(r, "connection-cleanup-thread");
            t.setDaemon(true); // 设置为守护线程
            return t;
        });
    
    private static volatile boolean started = false;
    
    /**
     * 启动定时清理任务
     */
    public static synchronized void start() {
        if (started) {
            log.warn("连接清理任务已经启动，跳过重复启动");
            return;
        }
        
        log.info("🚀 启动连接清理任务");
        
        // 定时清理无效连接 - 使用配置的间隔时间
        scheduler.scheduleAtFixedRate(() -> {
            try {
                SessionSocketHolder.cleanupInactiveConnections();
            } catch (Exception e) {
                log.error("❌ 定时连接清理任务执行失败", e);
            }
        }, AppConfig.Cleanup.REGULAR_CLEANUP_INTERVAL, AppConfig.Cleanup.REGULAR_CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
        
        // 深度清理任务 - 使用配置的间隔时间
        scheduler.scheduleAtFixedRate(() -> {
            try {
                log.info("🔍 开始执行深度连接清理任务");
                long startTime = System.currentTimeMillis();

                performDeepCleanup();

                long endTime = System.currentTimeMillis();
                log.info("✅ 深度连接清理任务完成，耗时: {}ms", (endTime - startTime));

            } catch (Exception e) {
                log.error("❌ 深度连接清理任务执行失败", e);
            }
        }, AppConfig.Cleanup.DEEP_CLEANUP_INTERVAL, AppConfig.Cleanup.DEEP_CLEANUP_INTERVAL, TimeUnit.MILLISECONDS);
        
        started = true;
        log.info("✅ 连接清理任务启动成功");
    }
    
    /**
     * 停止定时清理任务
     */
    public static synchronized void stop() {
        if (!started) {
            log.warn("连接清理任务未启动，跳过停止操作");
            return;
        }
        
        log.info("🛑 停止连接清理任务");
        
        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(AppConfig.Cleanup.SHUTDOWN_TIMEOUT_SECONDS, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
                log.warn("强制停止连接清理任务");
            }
            started = false;
            log.info("✅ 连接清理任务停止成功");
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
            log.error("❌ 停止连接清理任务时被中断", e);
        }
    }
    
    /**
     * 执行深度清理
     * 包括连接状态检查、直播间用户列表同步等
     */
    private static void performDeepCleanup() {
        try {
            // 1. 清理无效连接
            SessionSocketHolder.cleanupInactiveConnections();
            
            // 2. 同步直播间用户列表（移除无连接的用户）
            syncLiveRoomUserLists();
            
            // 3. 输出连接统计信息
            logConnectionStats();
            
            log.info("🧹 深度清理完成");
            
        } catch (Exception e) {
            log.error("❌ 执行深度清理时异常", e);
        }
    }
    
    /**
     * 同步直播间用户列表
     * 检查直播间中的用户是否还有有效连接，移除无连接的用户
     */
    private static void syncLiveRoomUserLists() {
        try {
            log.debug("🔄 开始同步直播间用户列表");

            int totalUsersRemoved = 0;

            // 1. 同步正式用户直播间列表
            int regularUsersRemoved = syncRegularUserRooms();
            totalUsersRemoved += regularUsersRemoved;

            // 2. 同步游客用户直播间列表
            int guestUsersRemoved = syncGuestUserRooms();
            totalUsersRemoved += guestUsersRemoved;

            // 3. 清理Redis中的无效用户
            int redisUsersRemoved = cleanupRedisRoomUsers();
            totalUsersRemoved += redisUsersRemoved;

            log.info("✅ 直播间用户列表同步完成: 移除无效用户{}个", totalUsersRemoved);

        } catch (Exception e) {
            log.error("❌ 同步直播间用户列表时异常", e);
        }
    }

    /**
     * 同步正式用户直播间列表
     * @return 移除的用户数量
     */
    private static int syncRegularUserRooms() {
        int removedCount = 0;

        try {
            // 获取所有正式用户直播间
            for (String roomId : SessionSocketHolder.getAllRegularUserRooms()) {
                removedCount += syncRoomUsers(roomId, false);
            }

            log.debug("正式用户直播间同步完成，移除{}个无效用户", removedCount);

        } catch (Exception e) {
            log.error("❌ 同步正式用户直播间时异常", e);
        }

        return removedCount;
    }

    /**
     * 同步游客用户直播间列表
     * @return 移除的用户数量
     */
    private static int syncGuestUserRooms() {
        int removedCount = 0;

        try {
            // 获取所有游客直播间
            for (String roomId : SessionSocketHolder.getAllGuestUserRooms()) {
                removedCount += syncRoomUsers(roomId, true);
            }

            log.debug("游客直播间同步完成，移除{}个无效用户", removedCount);

        } catch (Exception e) {
            log.error("❌ 同步游客直播间时异常", e);
        }

        return removedCount;
    }

    /**
     * 同步指定直播间的用户列表
     * @param roomId 直播间ID
     * @param isGuest 是否为游客直播间
     * @return 移除的用户数量
     */
    private static int syncRoomUsers(String roomId, boolean isGuest) {
        int removedCount = 0;

        try {
            // 获取直播间用户列表
            java.util.Set<String> roomUsers = SessionSocketHolder.getRoomUsers(roomId);
            if (roomUsers == null || roomUsers.isEmpty()) {
                return 0;
            }

            // 检查每个用户的连接状态
            java.util.Set<String> usersToRemove = new java.util.HashSet<>();

            for (String userId : roomUsers) {
                if (!hasValidConnection(userId, isGuest)) {
                    usersToRemove.add(userId);
                }
            }

            // 移除无效用户
            if (!usersToRemove.isEmpty()) {
                // 批量判断用户身份，提高性能
                Map<String, Boolean> userGuestStatus = SessionSocketHolder.batchCheckGuestUsers(
                    AppConfig.getCurrentAppId(), new ArrayList<>(usersToRemove));

                for (String userId : usersToRemove) {
                    boolean userIsGuest = userGuestStatus.getOrDefault(userId, isGuest);
                    SessionSocketHolder.removeUserFromRoom(roomId, userId, userIsGuest);
                    removedCount++;
                }
            }

        } catch (Exception e) {
            log.error("❌ 同步直播间{}用户列表时异常", roomId, e);
        }

        return removedCount;
    }

    /**
     * 检查用户是否有有效连接
     * @param userId 用户ID
     * @param isGuest 是否为游客
     * @return 是否有有效连接
     */
    private static boolean hasValidConnection(String userId, boolean isGuest) {
        try {
            if (isGuest) {
                // 检查游客连接 - 直接通过SessionSocketHolder的方法检查
                // 使用配置的appId
                NioSocketChannel guestChannel =
                    SessionSocketHolder.getGuestChannel(AppConfig.getCurrentAppId(), userId);
                if (guestChannel != null && guestChannel.isActive()) {
                    return true;
                }
                return false;
            } else {
                // 检查正式用户连接 - 使用SessionSocketHolder的get方法
                // 使用配置的appId
                List<NioSocketChannel> channels =
                    SessionSocketHolder.get(AppConfig.getCurrentAppId(), userId);
                if (channels != null && !channels.isEmpty()) {
                    for (NioSocketChannel channel : channels) {
                        if (channel != null && channel.isActive()) {
                            return true;
                        }
                    }
                }
                return false;
            }
        } catch (Exception e) {
            log.warn("检查用户{}连接状态时异常", userId, e);
            return false;
        }
    }

    /**
     * 清理Redis中的无效直播间用户
     * @return 清理的用户数量
     */
    private static int cleanupRedisRoomUsers() {
        int cleanedCount = 0;

        try {
            // 清理Redis中的正式用户直播间数据
            cleanedCount += cleanupRedisRegularUserRooms();

            // 清理Redis中的游客直播间数据
            cleanedCount += cleanupRedisGuestUserRooms();

            log.debug("Redis直播间用户清理完成，清理{}个无效用户", cleanedCount);

        } catch (Exception e) {
            log.error("❌ 清理Redis直播间用户时异常", e);
        }

        return cleanedCount;
    }

    /**
     * 清理Redis中的正式用户直播间数据
     * @return 清理的用户数量
     */
    private static int cleanupRedisRegularUserRooms() {
        int cleanedCount = 0;

        try {
            // 获取Redis连接
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient == null) {
                log.warn("Redis连接不可用，跳过Redis清理");
                return 0;
            }

            // 遍历所有正式用户直播间
            for (String roomId : SessionSocketHolder.getAllRegularUserRooms()) {
                String redisKey = Constants.RedisConstants.LiveRoomGuestPrefix +
                                roomId + Constants.RedisConstants.LiveRoomUserSuffix;

                RSet<String> roomUsers = redissonClient.getSet(redisKey);
                if (roomUsers != null && !roomUsers.isEmpty()) {
                    java.util.Set<String> usersToRemove = new java.util.HashSet<>();

                    // 检查每个用户的连接状态
                    for (String userId : roomUsers) {
                        if (!hasValidConnection(userId, false)) {
                            usersToRemove.add(userId);
                        }
                    }

                    // 从Redis中移除无效用户
                    for (String userId : usersToRemove) {
                        roomUsers.remove(userId);
                        cleanedCount++;
                    }
                }
            }

        } catch (Exception e) {
            log.error("❌ 清理Redis正式用户直播间数据时异常", e);
        }

        return cleanedCount;
    }

    /**
     * 清理Redis中的游客直播间数据
     * @return 清理的用户数量
     */
    private static int cleanupRedisGuestUserRooms() {
        int cleanedCount = 0;

        try {
            // 获取Redis连接
            RedissonClient redissonClient = RedisManager.getRedissonClient();
            if (redissonClient == null) {
                log.warn("Redis连接不可用，跳过游客Redis清理");
                return 0;
            }

            // 遍历所有游客直播间
            for (String roomId : SessionSocketHolder.getAllGuestUserRooms()) {
                String redisKey = Constants.RedisConstants.LiveRoomGuestPrefix +
                                roomId + Constants.RedisConstants.LiveRoomGuestSuffix;

                RSet<String> roomUsers = redissonClient.getSet(redisKey);
                if (roomUsers != null && !roomUsers.isEmpty()) {
                    java.util.Set<String> usersToRemove = new java.util.HashSet<>();

                    // 检查每个用户的连接状态
                    for (String userId : roomUsers) {
                        if (!hasValidConnection(userId, true)) {
                            usersToRemove.add(userId);
                        }
                    }

                    // 从Redis中移除无效用户
                    for (String userId : usersToRemove) {
                        roomUsers.remove(userId);
                        cleanedCount++;
                    }
                }
            }

        } catch (Exception e) {
            log.error("❌ 清理Redis游客直播间数据时异常", e);
        }

        return cleanedCount;
    }
    
    /**
     * 输出连接统计信息
     */
    private static void logConnectionStats() {
        try {
            int regularConnections = SessionSocketHolder.getAllChannels().size();
            int guestConnections = SessionSocketHolder.getAllGuestChannels().size();
            
            log.info("📊 连接统计 - 正式用户: {}, 游客: {}, 总计: {}", 
                    regularConnections, guestConnections, regularConnections + guestConnections);
        } catch (Exception e) {
            log.error("❌ 获取连接统计信息失败", e);
        }
    }
    
    /**
     * 手动触发连接清理
     * 提供给其他服务调用的接口
     */
    public static void manualCleanup() {
        log.info("🔧 手动触发连接清理");
        
        try {
            SessionSocketHolder.cleanupInactiveConnections();
            log.info("✅ 手动连接清理完成");
        } catch (Exception e) {
            log.error("❌ 手动连接清理失败", e);
        }
    }
    
    /**
     * 获取任务状态
     * @return 任务是否已启动
     */
    public static boolean isStarted() {
        return started;
    }
    
    /**
     * 获取连接统计信息
     * @return 连接统计信息
     */
    public static String getConnectionStats() {
        try {
            int regularConnections = SessionSocketHolder.getAllChannels().size();
            int guestConnections = SessionSocketHolder.getAllGuestChannels().size();
            
            return String.format("连接统计 - 正式用户: %d, 游客: %d, 总计: %d", 
                               regularConnections, guestConnections, regularConnections + guestConnections);
        } catch (Exception e) {
            log.error("❌ 获取连接统计信息失败", e);
            return "获取连接统计信息失败";
        }
    }
}
