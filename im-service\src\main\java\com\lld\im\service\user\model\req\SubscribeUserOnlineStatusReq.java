package com.lld.im.service.user.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 订阅用户在线状态请求
 * @description: 订阅指定用户的在线状态变化通知
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "订阅用户在线状态请求模型")
@Data
public class SubscribeUserOnlineStatusReq extends RequestBase {

    @ApiModelProperty(value = "订阅用户ID列表", required = true, example = "[\"user123\", \"user456\"]", notes = "要订阅在线状态的用户ID列表")
    private List<String> subUserId;

    @ApiModelProperty(value = "订阅时间", example = "1640995200000", notes = "订阅的时间戳，单位毫秒")
    private Long subTime;

}
