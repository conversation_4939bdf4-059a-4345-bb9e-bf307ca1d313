package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 群成员禁言请求
 */
@ApiModel(description = "群成员禁言请求模型")
@Data
public class SpeaMemberReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要设置成员禁言的群组唯一标识")
    @NotBlank(message = "{validation.group.id.not.blank}")
    private String groupId;

    @ApiModelProperty(value = "成员用户ID", required = true, example = "user456", notes = "要禁言的群成员用户ID")
    @NotBlank(message = "{validation.member.id.not.blank}")
    private String memberId;

    @ApiModelProperty(value = "禁言时间", required = true, example = "1640995200000", notes = "禁言截止时间戳，单位毫秒，0表示取消禁言")
    @NotNull(message = "{validation.speak.date.not.null}")
    private Long speakDate;
}
