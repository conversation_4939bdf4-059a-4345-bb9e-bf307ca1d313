package com.lld.im.common.enums;

import com.lld.im.common.exception.ApplicationExceptionEnum;

public enum UserErrorCode implements ApplicationExceptionEnum {


    IMPORT_SIZE_BEYOND(20000,"user.import.size.beyond"),
    USER_IS_NOT_EXIST(20001,"user.not.exist"),
    SERVER_GET_USER_ERROR(20002,"user.server.get.failed"),
    MODIFY_USER_ERROR(20003,"user.modify.failed"),
    SERVER_NOT_AVAILABLE(71000, "user.server.not.available"),
    ;

    private int code;
    private String messageKey;

    UserErrorCode(int code, String messageKey){
        this.code = code;
        this.messageKey = messageKey;
    }

    public int getCode() {
        return this.code;
    }

    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case IMPORT_SIZE_BEYOND:
                return "导入數量超出上限";
            case USER_IS_NOT_EXIST:
                return "用户不存在";
            case SERVER_GET_USER_ERROR:
                return "服务获取用户失败";
            case MODIFY_USER_ERROR:
                return "更新用户失败";
            case SERVER_NOT_AVAILABLE:
                return "没有可用的服务";
            default:
                return this.messageKey;
        }
    }

}
