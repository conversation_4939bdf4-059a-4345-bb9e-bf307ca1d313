package com.lld.im.tcp.config;

/**
 * 应用配置常量
 * 统一管理系统配置参数，避免硬编码
 * 
 * <AUTHOR>
 */
public class AppConfig {
    
    /**
     * 当前系统使用的应用ID
     */
    public static final Integer DEFAULT_APP_ID = 10000;
    
    /**
     * 连接清理相关配置
     */
    public static class Cleanup {

        /**
         * 常规清理间隔（毫秒）- 5分钟
         */
        public static final long REGULAR_CLEANUP_INTERVAL = 5 * 60 * 1000L;

        /**
         * 深度清理间隔（毫秒）- 30分钟
         */
        public static final long DEEP_CLEANUP_INTERVAL = 30 * 60 * 1000L;

        /**
         * 清理线程池大小
         */
        public static final int CLEANUP_THREAD_POOL_SIZE = 2;

        /**
         * 清理任务关闭超时时间（秒）
         */
        public static final int SHUTDOWN_TIMEOUT_SECONDS = 10;

        /**
         * 实时清理模式：专注于连接断开时的即时清理
         * 消息发送不再进行失效用户统计和处理
         */
    }
    

    

    

    
    /**
     * 获取当前应用ID
     * 可以从配置文件或环境变量中读取，这里先使用默认值
     * 
     * @return 应用ID
     */
    public static Integer getCurrentAppId() {
        // TODO: 可以从配置文件或环境变量中读取
        // String appIdStr = System.getProperty("app.id");
        // if (appIdStr != null) {
        //     return Integer.valueOf(appIdStr);
        // }
        return DEFAULT_APP_ID;
    }
    

    

}
