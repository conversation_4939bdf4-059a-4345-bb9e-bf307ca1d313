package com.lld.im.service.message.service;

import com.alibaba.fastjson.JSONObject;
import com.lld.im.codec.pack.message.ChatMessageAck;
import com.lld.im.codec.pack.message.MessageReciveServerAckPack;
import com.lld.im.codec.proto.Message;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.config.AppConfig;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.ConversationTypeEnum;
import com.lld.im.common.enums.FriendShipErrorCode;
import com.lld.im.common.enums.command.MessageCommand;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.common.model.message.MessageContent;
import com.lld.im.common.model.message.OfflineMessageContent;
import com.lld.im.service.conversation.service.ConversationService;
import com.lld.im.service.message.model.req.SendMessageReq;
import com.lld.im.service.message.model.resp.SendMessageResp;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.utils.CallbackService;
import com.lld.im.service.utils.ConversationIdGenerate;
import com.lld.im.service.utils.MessageProducer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
public class P2PMessageService {

    private static Logger logger = LoggerFactory.getLogger(P2PMessageService.class);

    @Autowired
    CheckSendMessageService checkSendMessageService;

    @Autowired
    MessageProducer messageProducer;

    @Autowired
    MessageStoreService messageStoreService;

    @Autowired
    RedisSeq redisSeq;

    @Autowired
    NonFriendMsgLimitService nonFriendMsgLimitService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    CallbackService callbackService;

    @Autowired
    ConversationService conversationService;

    private final ThreadPoolExecutor threadPoolExecutor;

    {
        final AtomicInteger num = new AtomicInteger(0);
        threadPoolExecutor = new ThreadPoolExecutor(8, 8, 60, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(1000), new ThreadFactory() {
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setDaemon(true);
                thread.setName("message-process-thread-" + num.getAndIncrement());
                return thread;
            }
        });
    }

    //离线
    //存储介质
    //1.mysql
    //2.redis
    //怎么存？
    //list


    //历史消息

    //发送方客户端时间
    //messageKey
    //redis 1 2 3
    public void process(MessageContent messageContent){

        logger.info("消息开始处理：{}",messageContent.getMessageId());
        String fromId = messageContent.getFromId();
        String toId = messageContent.getToId();
        Integer appId = messageContent.getAppId();

        MessageContent messageFromMessageIdCache = messageStoreService.getMessageFromMessageIdCache
                (messageContent.getAppId(), messageContent.getMessageId(),MessageContent.class);
        if (messageFromMessageIdCache != null){
            threadPoolExecutor.execute(() ->{
                ack(messageContent,ResponseVO.successResponse());
                //2.发消息给同步在线端
                syncToSender(messageFromMessageIdCache,messageFromMessageIdCache);
                //3.发消息给对方在线端
                List<ClientInfo> clientInfos = dispatchMessage(messageFromMessageIdCache);
                if(clientInfos.isEmpty()){
                    //发送接收确认给发送方，要带上是服务端发送的标识
                    reciverAck(messageFromMessageIdCache);
                }
            });
            return;
        }

        //回调
        ResponseVO responseVO = ResponseVO.successResponse();
        if(appConfig.isSendMessageAfterCallback()){
            responseVO = callbackService.beforeCallback(messageContent.getAppId(), Constants.CallbackCommand.SendMessageBefore
                    , JSONObject.toJSONString(messageContent));
        }

        if(!responseVO.isOk()){
            ack(messageContent,responseVO);
            return;
        }

        long seq = redisSeq.doGetSeq(messageContent.getAppId() + ":"
                + Constants.SeqConstants.Message+ ":" + ConversationIdGenerate.generateP2PId(
                messageContent.getFromId(),messageContent.getToId()
        ));
        messageContent.setMessageSequence(seq);
        messageContent.setMessageTime(System.currentTimeMillis());


        //前置校验
        //这个用户是否被禁言 是否被禁用
        //发送方和接收方是否是好友
//        ResponseVO responseVO = imServerPermissionCheck(fromId, toId, appId);
//        if(responseVO.isOk()){

        // 先检查是否为回复消息，回复消息优先处理
        boolean isReplyMessage = nonFriendMsgLimitService.isReplyMessage(
                messageContent.getFromId(), messageContent.getToId(), messageContent.getAppId());

        if (!isReplyMessage) {
            // 不是回复消息，进行正常的限制检查
            ResponseVO nonFriendLimitCheck = nonFriendMsgLimitService.checkNonFriendMsgLimit(
                    messageContent.getFromId(), messageContent.getToId(), messageContent.getAppId());

            // 检查是否被限制：需要检查data中的isLimited字段
            if (nonFriendLimitCheck.isOk() && nonFriendLimitCheck.getData() != null) {
                @SuppressWarnings("unchecked")
                Map<String, Object> limitData = (Map<String, Object>) nonFriendLimitCheck.getData();
                Boolean isLimited = (Boolean) limitData.get("isLimited");
                if (Boolean.TRUE.equals(isLimited)) {
                    // 被限制时，构造错误响应返回给客户端
                    ResponseVO<Object> errorResponse = ResponseVO.errorResponse(FriendShipErrorCode.NON_FRIEND_MSG_LIMIT_EXCEEDED);
                    errorResponse.setData(limitData);
                    ack(messageContent, errorResponse);
                    logger.info("用户消息被非好友私信限制拦截: fromId={}, toId={}, limitData={}",
                            messageContent.getFromId(), messageContent.getToId(), limitData);
                    return;
                }
            }
        } else {
            logger.info("检测到回复消息，跳过限制检查: fromId={}, toId={}",
                    messageContent.getFromId(), messageContent.getToId());
        }

        threadPoolExecutor.execute(() ->{
            // 记录非好友私信发送状态
            nonFriendMsgLimitService.recordNonFriendMsg(
                    messageContent.getFromId(), messageContent.getToId(), messageContent.getAppId());

            // 如果是回复消息，重置对方的发送限制
            if (isReplyMessage) {
                nonFriendMsgLimitService.recordNonFriendReply(
                        messageContent.getFromId(), messageContent.getToId(), messageContent.getAppId());
                logger.info("处理回复消息，重置对方发送限制: fromId={}, toId={}",
                        messageContent.getFromId(), messageContent.getToId());
            }
                //appId + Seq + (from + to) groupId
                messageStoreService.storeP2PMessage(messageContent);

                OfflineMessageContent offlineMessageContent = new OfflineMessageContent();
                BeanUtils.copyProperties(messageContent,offlineMessageContent);
                offlineMessageContent.setConversationType(ConversationTypeEnum.P2P.getCode());
                messageStoreService.storeOfflineMessage(offlineMessageContent);

                // 更新会话记录 - 为发送方和接收方创建或更新会话记录（支持实时推送）
                conversationService.updateConversationSetForP2P(messageContent, messageContent.getClientType(), messageContent.getImei());

                //插入数据
                //1.回ack成功给自己
                ack(messageContent,ResponseVO.successResponse());
                //2.发消息给同步在线端
                syncToSender(messageContent,messageContent);
                //3.发消息给对方在线端
                List<ClientInfo> clientInfos = dispatchMessage(messageContent);

                messageStoreService.setMessageFromMessageIdCache(messageContent.getAppId(),
                        messageContent.getMessageId(),messageContent);
                if(clientInfos.isEmpty()){
                    //发送接收确认给发送方，要带上是服务端发送的标识
                    reciverAck(messageContent);
                }

                if(appConfig.isSendMessageAfterCallback()){
                    callbackService.callback(messageContent.getAppId(),Constants.CallbackCommand.SendMessageAfter,
                            JSONObject.toJSONString(messageContent));
                }

                logger.info("消息处理完成：{}",messageContent.getMessageId());
            });
//        }else{
//            //告诉客户端失败了
//            //ack
//            ack(messageContent,responseVO);
//        }
    }

    private List<ClientInfo> dispatchMessage(MessageContent messageContent){
        List<ClientInfo> clientInfos = messageProducer.sendToUser(messageContent.getToId(), MessageCommand.MSG_P2P,
                messageContent, messageContent.getAppId());
        return clientInfos;
    }

    private void ack(MessageContent messageContent,ResponseVO responseVO){
        logger.info("msg ack,msgId={},checkResut{}",messageContent.getMessageId(),responseVO.getCode());

        ChatMessageAck chatMessageAck = new ChatMessageAck();
        // 设置基本属性
        chatMessageAck.setMessageId(messageContent.getMessageId());
        chatMessageAck.setMessageSequence(messageContent.getMessageSequence());
        chatMessageAck.setMessageKey(messageContent.getMessageKey());

        // 设置其他属性
        chatMessageAck.setFromId(messageContent.getFromId());
        chatMessageAck.setToId(messageContent.getToId());
        chatMessageAck.setMessageBody(messageContent.getMessageBody());
        chatMessageAck.setMessageTime(messageContent.getMessageTime());
        chatMessageAck.setExtra(messageContent.getExtra());

        // 设置会话类型为单聊
        chatMessageAck.setConversationType(ConversationTypeEnum.P2P.getCode());

        // 设置单聊会话ID：格式为 0_fromId_toId（按用户ID升序排列）
        String conversationId = ConversationTypeEnum.P2P.getCode() + "_" + messageContent.getFromId() + "_" + messageContent.getToId();
        chatMessageAck.setConversationId(conversationId);

        responseVO.setData(chatMessageAck);
        //發消息
        messageProducer.sendToUser(messageContent.getFromId(), MessageCommand.MSG_ACK,
                responseVO,messageContent
                );
    }

    public void reciverAck(MessageContent messageContent){
        MessageReciveServerAckPack pack = new MessageReciveServerAckPack();
        pack.setFromId(messageContent.getToId());
        pack.setToId(messageContent.getFromId());
        pack.setMessageKey(messageContent.getMessageKey());
        pack.setMessageSequence(messageContent.getMessageSequence());
        pack.setServerSend(true);
        messageProducer.sendToUser(messageContent.getFromId(),MessageCommand.MSG_RECIVE_ACK,
                pack,new ClientInfo(messageContent.getAppId(),messageContent.getClientType()
                ,messageContent.getImei()));
    }

    private void syncToSender(MessageContent messageContent, ClientInfo clientInfo){
            messageProducer.sendToUserExceptClient(messageContent.getFromId(),
                    MessageCommand.MSG_P2P,messageContent,messageContent);
    }

    public ResponseVO imServerPermissionCheck(String fromId,String toId,
                                               Integer appId){
        ResponseVO responseVO = checkSendMessageService.checkSenderForvidAndMute(fromId, appId);
        if(!responseVO.isOk()){
            return responseVO;
        }
        responseVO = checkSendMessageService.checkFriendShip(fromId, toId, appId);
        return responseVO;
    }

    public SendMessageResp send(SendMessageReq req) {

        SendMessageResp sendMessageResp = new SendMessageResp();
        MessageContent message = new MessageContent();
        BeanUtils.copyProperties(req,message);

        // 确保messageTime字段正确设置
        if (message.getMessageTime() == null || message.getMessageTime() == 0) {
            message.setMessageTime(System.currentTimeMillis());
        }

        // 确保sequence字段正确设置
        if (message.getMessageSequence() == 0) {
            long seq = redisSeq.doGetSeq(message.getAppId() + ":"
                    + Constants.SeqConstants.Message+ ":" + ConversationIdGenerate.generateP2PId(
                    message.getFromId(),message.getToId()
            ));
            message.setMessageSequence(seq);
        }

        //插入数据
        messageStoreService.storeP2PMessage(message);

        // 更新会话记录 - 为发送方和接收方创建或更新会话记录（支持实时推送）
        conversationService.updateConversationSetForP2P(message, message.getClientType(), message.getImei());

        sendMessageResp.setMessageKey(message.getMessageKey());
        sendMessageResp.setMessageTime(message.getMessageTime());

        //2.发消息给同步在线端
        syncToSender(message,message);
        //3.发消息给对方在线端
        dispatchMessage(message);
        return sendMessageResp;
    }
}
