package com.lld.im.service.user.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.interceptor.UserPermissionCheck;
import com.lld.im.service.user.model.req.GetUserInfoByConversationReq;
import com.lld.im.service.user.model.req.GetUserInfoReq;
import com.lld.im.service.user.model.req.ModifyUserInfoReq;
import com.lld.im.service.user.model.req.UserId;
import com.lld.im.service.user.model.resp.GetUserInfoByConversationResp;
import com.lld.im.service.user.service.ConversationBasedUserInfoService;
import com.lld.im.service.user.service.ImUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 用户数据管理控制器
 * @author: lld
 * @version: 1.0
 */
@Api(tags = "用户数据管理", description = "用户信息的查询和修改相关接口")
@RestController
@RequestMapping("v1/user/data")
public class ImUserDataController extends BaseController {

    private static Logger logger = LoggerFactory.getLogger(ImUserDataController.class);

    @Autowired
    ImUserService imUserService;

    @Autowired
    ConversationBasedUserInfoService conversationBasedUserInfoService;

    @ApiOperation(value = "批量获取用户信息", notes = "根据用户ID列表批量获取用户详细信息")
    @PostMapping("/getUserInfo")
    public ResponseVO getUserInfo(
            @ApiParam(value = "获取用户信息请求参数", required = true) @RequestBody @Validated GetUserInfoReq req){
        // 自动填充公共参数
        fillCommonParams(req);
        return imUserService.getUserInfo(req);
    }

    @ApiOperation(value = "获取单个用户信息", notes = "根据用户ID获取单个用户的详细信息")
    @PostMapping("/getSingleUserInfo")
    @UserPermissionCheck("userId")
    public ResponseVO getSingleUserInfo(
            @ApiParam(value = "用户ID请求参数", required = true) @RequestBody @Validated UserId req){
        // 自动填充公共参数
        fillCommonParams(req);
        return imUserService.getSingleUserInfo(req.getUserId(),req.getAppId());
    }

    @ApiOperation(value = "修改用户信息", notes = "更新用户的个人资料信息")
    @PutMapping("/modifyUserInfo")
    @UserPermissionCheck("userId")
    public ResponseVO modifyUserInfo(
            @ApiParam(value = "修改用户信息请求参数", required = true) @RequestBody @Validated ModifyUserInfoReq req,
            @ApiParam(value = "应用ID", required = true, example = "10000") Integer appId){
        req.setAppId(appId);
        return imUserService.modifyUserInfo(req);
    }

    @ApiOperation(value = "基于会话关系批量获取用户信息", notes = "只能获取存在好友关系或会话关系的用户信息，保护用户隐私")
    @PostMapping("/getUserInfoByConversation")
    @UserPermissionCheck("requestUserId")
    public ResponseVO<GetUserInfoByConversationResp> getUserInfoByConversation(
            @ApiParam(value = "基于会话关系获取用户信息请求参数", required = true) @RequestBody @Validated GetUserInfoByConversationReq req) {
        // 自动填充公共参数
        fillCommonParams(req);
        return conversationBasedUserInfoService.getUserInfoByConversation(req);
    }
}
