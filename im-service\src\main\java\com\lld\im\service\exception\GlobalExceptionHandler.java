package com.lld.im.service.exception;

import com.lld.im.common.BaseErrorCode;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.exception.ApplicationException;
import com.lld.im.common.exception.PermissionDeniedException;
import com.lld.im.service.utils.MessageUtils;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;

@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @Autowired
    private MessageUtils messageUtils;

    @ExceptionHandler(value=Exception.class)
    @ResponseBody
    public ResponseVO<?> unknowException(Exception e){
        logger.error("系统发生未知异常", e);

        ResponseVO<?> resultBean = new ResponseVO<>();
        resultBean.setCode(BaseErrorCode.SYSTEM_ERROR.getCode());
        resultBean.setMsg(BaseErrorCode.SYSTEM_ERROR.getError());
        /**
         * 未知异常的话，这里写逻辑，发邮件，发短信都可以、、
         */
        return resultBean;
    }


    @ExceptionHandler(PermissionDeniedException.class)
    @ResponseBody
    public ResponseVO<?> handlePermissionDeniedException(PermissionDeniedException ex) {
        // 记录权限异常日志
        logger.warn("权限拒绝异常: code={}, message={}", ex.getCode(), ex.getError());

        ResponseVO<?> resultBean = new ResponseVO<>();
        resultBean.setCode(ex.getCode());
        resultBean.setMsg(ex.getError());
        return resultBean;
    }

    /**
     * HTTP请求方法不支持异常处理
     *
     * @param ex HttpRequestMethodNotSupportedException
     * @return ResponseVO
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    public ResponseVO<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        // 记录HTTP方法不支持异常日志
        logger.warn("HTTP请求方法不支持异常: method={}, supportedMethods={}",
                   ex.getMethod(), ex.getSupportedMethods());

        ResponseVO<?> resultBean = new ResponseVO<>();
        resultBean.setCode(BaseErrorCode.METHOD_NOT_SUPPORTED.getCode());
        resultBean.setMsg(BaseErrorCode.METHOD_NOT_SUPPORTED.getError());
        return resultBean;
    }

    /**
     * 缺少必需请求参数异常处理
     *
     * @param ex MissingServletRequestParameterException
     * @return ResponseVO
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    public ResponseVO handleMissingServletRequestParameterException(MissingServletRequestParameterException ex) {
        // 记录缺少参数异常日志
        logger.warn("缺少必需请求参数异常: parameterName={}, parameterType={}",
                   ex.getParameterName(), ex.getParameterType());

        String message = "Required " + ex.getParameterType() + " parameter '" + ex.getParameterName() + "' is not present";

        ResponseVO resultBean = new ResponseVO();
        resultBean.setCode(BaseErrorCode.PARAMETER_ERROR.getCode());
        resultBean.setMsg(message);
        return resultBean;
    }

    /**
     * Validator 参数校验异常处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseBody
    public Object handleMethodArgumentNotValidException(ConstraintViolationException ex) {
        // 记录参数校验异常日志
        logger.warn("参数校验异常: {}", ex.getMessage());

        Set<ConstraintViolation<?>> constraintViolations = ex.getConstraintViolations();
        ResponseVO resultBean =new ResponseVO();
        resultBean.setCode(BaseErrorCode.PARAMETER_ERROR.getCode());
        for (ConstraintViolation<?> constraintViolation : constraintViolations) {
            PathImpl pathImpl = (PathImpl) constraintViolation.getPropertyPath();
            // 读取参数字段，constraintViolation.getMessage() 读取验证注解中的message值
            String paramName = pathImpl.getLeafNode().getName();
            String message = "参数{".concat(paramName).concat("}").concat(constraintViolation.getMessage());
            resultBean.setMsg(message);

            return resultBean;
        }
        resultBean.setMsg(BaseErrorCode.PARAMETER_ERROR.getError() + ex.getMessage());
        return resultBean;
    }

    @ExceptionHandler(ApplicationException.class)
    @ResponseBody
    public ResponseVO<?> applicationExceptionHandler(ApplicationException e) {
        // 记录业务异常日志
        logger.warn("业务异常: code={}, message={}", e.getCode(), e.getError());

        // 使用公共的结果类封装返回结果
        ResponseVO<?> resultBean = new ResponseVO<>();
        resultBean.setCode(e.getCode());
        // 错误信息已通过枚举类的getError()方法国际化
        resultBean.setMsg(e.getError());
        return resultBean;
    }

    /**
     * Validator 参数校验异常处理
     *
     * @param ex
     * @return
     */
    @ExceptionHandler(value = BindException.class)
    @ResponseBody
    public Object  handleException2(BindException ex) {
        FieldError err = ex.getFieldError();
        if (err != null) {
            String message = "参数{".concat(err.getField()).concat("}").concat(err.getDefaultMessage());
            // 记录参数绑定异常日志
            logger.warn("参数绑定异常: field={}, message={}", err.getField(), err.getDefaultMessage());

            ResponseVO resultBean =new ResponseVO();
            resultBean.setCode(BaseErrorCode.PARAMETER_ERROR.getCode());
            resultBean.setMsg(message);
            return resultBean;
        } else {
            // 记录参数绑定异常日志
            logger.warn("参数绑定异常: {}", ex.getMessage());

            ResponseVO resultBean =new ResponseVO();
            resultBean.setCode(BaseErrorCode.PARAMETER_ERROR.getCode());
            resultBean.setMsg(BaseErrorCode.PARAMETER_ERROR.getError());
            return resultBean;
        }
    }

    /**
     * JSON参数校验异常处理
     * 处理@RequestBody参数校验失败的情况
     */
    @ExceptionHandler(value = MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseVO<?> handleMethodArgumentNotValidException(MethodArgumentNotValidException ex) {
        StringBuilder errorMsg = new StringBuilder();
        BindingResult re = ex.getBindingResult();
        for (ObjectError error : re.getAllErrors()) {
            // 直接使用已国际化的错误消息（通过{validation.xxx}键获取）
            errorMsg.append(error.getDefaultMessage()).append(",");
        }
        errorMsg.delete(errorMsg.length() - 1, errorMsg.length());

        // 记录JSON参数校验异常日志
        logger.warn("JSON参数校验异常: {}", errorMsg.toString());

        ResponseVO<?> resultBean = new ResponseVO<>();
        resultBean.setCode(BaseErrorCode.PARAMETER_ERROR.getCode());

        // 使用国际化的参数错误消息
        String paramErrorMsg = messageUtils.getMessage("error.parameter.error");
        resultBean.setMsg(paramErrorMsg + " : " + errorMsg.toString());
        return resultBean;
    }

}
