package com.lld.im.service.group.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lld.im.codec.pack.group.AddGroupMemberPack;
import com.lld.im.codec.pack.group.ApplyJoinGroupPack;
import com.lld.im.codec.pack.group.ApproveGroupApplyPack;
import com.lld.im.codec.pack.group.ExitGroupPack;
import com.lld.im.codec.pack.group.GroupMemberSpeakPack;
import com.lld.im.codec.pack.group.RemoveGroupMemberPack;
import com.lld.im.codec.pack.group.UpdateGroupMemberPack;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lld.im.common.BaseErrorCode;
import com.lld.im.common.ResponseVO;
import com.lld.im.common.config.AppConfig;
import com.lld.im.common.constant.Constants;
import com.lld.im.common.enums.DelFlagEnum;
import com.lld.im.common.enums.GroupErrorCode;
import com.lld.im.common.enums.GroupMemberRoleEnum;
import com.lld.im.common.enums.GroupStatusEnum;
import com.lld.im.common.enums.GroupTypeEnum;
import com.lld.im.common.enums.command.GroupEventCommand;
import com.lld.im.common.exception.ApplicationException;
import com.lld.im.common.model.ClientInfo;
import com.lld.im.service.group.dao.ImGroupEntity;
import com.lld.im.service.group.dao.ImGroupMemberEntity;
import com.lld.im.service.group.dao.ImGroupApplyEntity;
import com.lld.im.service.group.dao.mapper.ImGroupMemberMapper;
import com.lld.im.service.group.dao.mapper.ImGroupApplyMapper;
import com.lld.im.service.group.dao.mapper.ImGroupMapper;
import com.lld.im.service.group.model.callback.AddMemberAfterCallback;
import com.lld.im.service.group.model.callback.DestroyGroupCallbackDto;
import com.lld.im.service.group.model.req.*;
import com.lld.im.service.group.model.resp.BatchSpeakMemberResp;
import com.lld.im.service.group.model.resp.BatchRemoveGroupMemberResp;
import com.lld.im.service.group.model.resp.GroupApplyListResp;
import com.lld.im.service.group.model.resp.AddMemberResp;
import com.lld.im.service.group.model.resp.GetRoleInGroupResp;
import com.lld.im.service.group.model.resp.UserApplyListResp;
import com.lld.im.service.group.service.ImGroupMemberService;
import com.lld.im.service.group.service.ImGroupService;
import com.lld.im.service.seq.RedisSeq;
import com.lld.im.service.user.dao.ImUserDataEntity;
import com.lld.im.service.user.dao.mapper.ImUserDataMapper;
import com.lld.im.service.user.service.ImUserService;
import com.lld.im.service.utils.CallbackService;
import com.lld.im.service.utils.GroupMessageProducer;
import com.lld.im.service.utils.WriteUserSeq;
import com.lld.im.service.conversation.service.ConversationPreCreateService;
import com.lld.im.service.group.service.FocusDayCheckService;
import com.lld.im.service.group.utils.GroupJoinThresholdChecker;
import com.lld.im.service.group.model.GroupJoinThresholdConfig;
import com.lld.im.common.enums.GroupJoinThresholdEnum;
import com.lld.im.service.group.service.GroupMessageService;
import com.lld.im.common.model.message.GroupChatMessageContent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.UUID;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Service
@Slf4j
public class ImGroupMemberServiceImpl implements ImGroupMemberService {

    @Autowired
    ImGroupMemberMapper imGroupMemberMapper;

    @Autowired
    ImGroupApplyMapper imGroupApplyMapper;

    @Autowired
    ImGroupMapper imGroupDataMapper;

    @Autowired
    ImGroupService groupService;

    @Autowired
    ImGroupMemberService groupMemberService;

    @Autowired
    AppConfig appConfig;

    @Autowired
    CallbackService callbackService;

    @Autowired
    ImUserService imUserService;

    @Autowired
    GroupMessageProducer groupMessageProducer;

    @Autowired
    RedisSeq redisSeq;

    @Autowired
    ConversationPreCreateService conversationPreCreateService;

    @Autowired
    FocusDayCheckService focusDayCheckService;

    @Autowired
    GroupJoinThresholdChecker groupJoinThresholdChecker;

    @Autowired
    WriteUserSeq writeUserSeq;

    @Autowired
    GroupMessageService groupMessageService;

    @Override
    public ResponseVO importGroupMember(ImportGroupMemberReq req) {

        List<AddMemberResp> resp = new ArrayList<>();

        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }

        for (GroupMemberDto memberId :
                req.getMembers()) {
            ResponseVO responseVO = null;
            try {
                responseVO = groupMemberService.addGroupMember(req.getGroupId(), req.getAppId(), memberId);
            } catch (Exception e) {
                e.printStackTrace();
                responseVO = ResponseVO.errorResponse();
            }
            AddMemberResp addMemberResp = new AddMemberResp();
            addMemberResp.setMemberId(memberId.getMemberId());
            if (responseVO.isOk()) {
                addMemberResp.setResult(0);
            } else if (responseVO.getCode() == GroupErrorCode.USER_IS_JOINED_GROUP.getCode()) {
                addMemberResp.setResult(2);
            } else {
                addMemberResp.setResult(1);
            }
            resp.add(addMemberResp);
        }

        return ResponseVO.successResponse(resp);
    }

    /**
     * @param
     * @return com.lld.im.common.ResponseVO
     * @description: 添加群成员，内部调用
     * <AUTHOR>
     */
    @Override
    @Transactional
    public ResponseVO addGroupMember(String groupId, Integer appId, GroupMemberDto dto) {

        ResponseVO<ImUserDataEntity> singleUserInfo = imUserService.getSingleUserInfo(dto.getMemberId(), appId);
        if(!singleUserInfo.isOk()){
            return singleUserInfo;
        }

        if (dto.getRole() != null && GroupMemberRoleEnum.OWNER.getCode() == dto.getRole()) {
            QueryWrapper<ImGroupMemberEntity> queryOwner = new QueryWrapper<>();
            queryOwner.eq("group_id", groupId);
            queryOwner.eq("app_id", appId);
            queryOwner.eq("role", GroupMemberRoleEnum.OWNER.getCode());
            Integer ownerNum = imGroupMemberMapper.selectCount(queryOwner);
            if (ownerNum > 0) {
                return ResponseVO.errorResponse(GroupErrorCode.GROUP_IS_HAVE_OWNER);
            }
        }

        QueryWrapper<ImGroupMemberEntity> query = new QueryWrapper<>();
        query.eq("group_id", groupId);
        query.eq("app_id", appId);
        query.eq("member_id", dto.getMemberId());
        ImGroupMemberEntity memberDto = imGroupMemberMapper.selectOne(query);

        long now = System.currentTimeMillis();
        if (memberDto == null) {
            //初次加群
            memberDto = new ImGroupMemberEntity();
            BeanUtils.copyProperties(dto, memberDto);
            memberDto.setGroupId(groupId);
            memberDto.setAppId(appId);
            memberDto.setJoinTime(now);
            int insert = imGroupMemberMapper.insert(memberDto);
            if (insert == 1) {
                // 预创建群聊会话记录
                try {
                    conversationPreCreateService.preCreateGroupConversation(appId, dto.getMemberId(), groupId);
                    log.info("群成员加入成功后预创建会话记录完成，userId: {}, groupId: {}", dto.getMemberId(), groupId);
                } catch (Exception e) {
                    log.error("群成员加入成功后预创建会话记录失败，userId: {}, groupId: {}, 错误: {}",
                        dto.getMemberId(), groupId, e.getMessage(), e);
                    // 会话预创建失败不影响群成员加入的主流程，只记录日志
                }

                // 发送群成员加入系统消息
                sendMemberJoinSystemMessage(groupId, dto.getMemberId(), appId);

                return ResponseVO.successResponse();
            }
            return ResponseVO.errorResponse(GroupErrorCode.USER_JOIN_GROUP_ERROR);
        } else if (GroupMemberRoleEnum.LEAVE.getCode() == memberDto.getRole()) {
            //重新进群
            memberDto = new ImGroupMemberEntity();
            BeanUtils.copyProperties(dto, memberDto);
            memberDto.setJoinTime(now);
            int update = imGroupMemberMapper.update(memberDto, query);
            if (update == 1) {
                // 预创建群聊会话记录
                try {
                    conversationPreCreateService.preCreateGroupConversation(appId, dto.getMemberId(), groupId);
                    log.info("群成员重新加入成功后预创建会话记录完成，userId: {}, groupId: {}", dto.getMemberId(), groupId);
                } catch (Exception e) {
                    log.error("群成员重新加入成功后预创建会话记录失败，userId: {}, groupId: {}, 错误: {}",
                        dto.getMemberId(), groupId, e.getMessage(), e);
                    // 会话预创建失败不影响群成员加入的主流程，只记录日志
                }

                // 发送群成员加入系统消息
                sendMemberJoinSystemMessage(groupId, dto.getMemberId(), appId);

                return ResponseVO.successResponse();
            }
            return ResponseVO.errorResponse(GroupErrorCode.USER_JOIN_GROUP_ERROR);
        }

        return ResponseVO.errorResponse(GroupErrorCode.USER_IS_JOINED_GROUP);

    }

    /**
     * @param
     * @return com.lld.im.common.ResponseVO
     * @description: 删除群成员，内部调用
     * <AUTHOR>
     */
    @Override
    public ResponseVO removeGroupMember(String groupId, Integer appId, String memberId) {

        ResponseVO<ImUserDataEntity> singleUserInfo = imUserService.getSingleUserInfo(memberId, appId);
        if(!singleUserInfo.isOk()){
            return singleUserInfo;
        }

        ResponseVO<GetRoleInGroupResp> roleInGroupOne = getRoleInGroupOne(groupId, memberId, appId);
        if (!roleInGroupOne.isOk()) {
            return roleInGroupOne;
        }

        GetRoleInGroupResp data = roleInGroupOne.getData();
        ImGroupMemberEntity imGroupMemberEntity = new ImGroupMemberEntity();
        imGroupMemberEntity.setRole(GroupMemberRoleEnum.LEAVE.getCode());
        imGroupMemberEntity.setLeaveTime(System.currentTimeMillis());
        imGroupMemberEntity.setGroupMemberId(data.getGroupMemberId());
        int updateResult = imGroupMemberMapper.updateById(imGroupMemberEntity);

        if (updateResult == 1) {
            // 发送群成员移除系统消息给当事人
            sendMemberRemoveSystemMessage(groupId, memberId, appId);
        }

        return ResponseVO.successResponse();
    }

    /**
     * @param [groupId, memberId, appId]
     * @return com.lld.im.common.ResponseVO<com.lld.im.service.group.model.resp.GetRoleInGroupResp>
     * @description 查询用户在群内的角色
     * <AUTHOR>
     */
    @Override
    public ResponseVO<GetRoleInGroupResp> getRoleInGroupOne(String groupId, String memberId, Integer appId) {

        GetRoleInGroupResp resp = new GetRoleInGroupResp();

        QueryWrapper<ImGroupMemberEntity> queryOwner = new QueryWrapper<>();
        queryOwner.eq("group_id", groupId);
        queryOwner.eq("app_id", appId);
        queryOwner.eq("member_id", memberId);

        ImGroupMemberEntity imGroupMemberEntity = imGroupMemberMapper.selectOne(queryOwner);
        if (imGroupMemberEntity == null || imGroupMemberEntity.getRole() == GroupMemberRoleEnum.LEAVE.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.MEMBER_IS_NOT_JOINED_GROUP);
        }

        resp.setSpeakDate(imGroupMemberEntity.getSpeakDate());
        resp.setGroupMemberId(imGroupMemberEntity.getGroupMemberId());
        resp.setMemberId(imGroupMemberEntity.getMemberId());
        resp.setRole(imGroupMemberEntity.getRole());
        return ResponseVO.successResponse(resp);
    }

    @Override
    public ResponseVO<Collection<String>> getMemberJoinedGroup(GetJoinedGroupReq req) {

        if (req.getLimit() != null) {
            Page<ImGroupMemberEntity> objectPage = new Page<>(req.getOffset(), req.getLimit());
            QueryWrapper<ImGroupMemberEntity> query = new QueryWrapper<>();
            query.eq("app_id", req.getAppId());
            query.eq("member_id", req.getMemberId());
            IPage<ImGroupMemberEntity> imGroupMemberEntityPage = imGroupMemberMapper.selectPage(objectPage, query);

            Set<String> groupId = new HashSet<>();
            List<ImGroupMemberEntity> records = imGroupMemberEntityPage.getRecords();
            records.forEach(e -> {
                groupId.add(e.getGroupId());
            });

            return ResponseVO.successResponse(groupId);
        } else {
            return ResponseVO.successResponse(imGroupMemberMapper.getJoinedGroupId(req.getAppId(), req.getMemberId()));
        }
    }

    /**
     * @param
     * @return com.lld.im.common.ResponseVO
     * @description: 添加群成员，拉人入群的逻辑，直接进入群聊。如果是后台管理员，则直接拉入群，
     * 否则只有私有群可以调用本接口，并且群成员也可以拉人入群.只有私有群可以调用本接口
     * <AUTHOR>
     */
    @Override
    public ResponseVO addMember(AddGroupMemberReq req) {

        List<AddMemberResp> resp = new ArrayList<>();

        boolean isAdmin = false;
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }

        List<GroupMemberDto> memberDtos = req.getMembers();
        if(appConfig.isAddGroupMemberBeforeCallback()){

            ResponseVO responseVO = callbackService.beforeCallback(req.getAppId(), Constants.CallbackCommand.GroupMemberAddBefore
                    , JSONObject.toJSONString(req));
            if(!responseVO.isOk()){
                return responseVO;
            }

            try {
                memberDtos
                        = JSONArray.parseArray(JSONObject.toJSONString(responseVO.getData()), GroupMemberDto.class);
            }catch (Exception e){
                e.printStackTrace();
                log.error("GroupMemberAddBefore 回调失败：{}",req.getAppId());
            }
        }

        ImGroupEntity group = groupResp.getData();


        /**
         * 私有群（private）	类似普通微信群，创建后仅支持已在群内的好友邀请加群，且无需被邀请方同意或群主审批
         * 公开群（Public）	类似 QQ 群，创建后群主可以指定群管理员，需要群主或管理员审批通过才能入群
         * 群类型 1私有群（类似微信） 2公开群(类似qq）
         *
         */

        if (!isAdmin && GroupTypeEnum.PUBLIC.getCode() == group.getGroupType()) {
            throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_APPMANAGER_ROLE);
        }

        List<String> successId = new ArrayList<>();
        for (GroupMemberDto memberId :
                memberDtos) {
            ResponseVO responseVO = null;
            try {
                responseVO = groupMemberService.addGroupMember(req.getGroupId(), req.getAppId(), memberId);
            } catch (Exception e) {
                e.printStackTrace();
                responseVO = ResponseVO.errorResponse();
            }
            AddMemberResp addMemberResp = new AddMemberResp();
            addMemberResp.setMemberId(memberId.getMemberId());
            if (responseVO.isOk()) {
                successId.add(memberId.getMemberId());
                addMemberResp.setResult(0);
            } else if (responseVO.getCode() == GroupErrorCode.USER_IS_JOINED_GROUP.getCode()) {
                addMemberResp.setResult(2);
                addMemberResp.setResultMessage(responseVO.getMsg());
            } else {
                addMemberResp.setResult(1);
                addMemberResp.setResultMessage(responseVO.getMsg());
            }
            resp.add(addMemberResp);
        }

        AddGroupMemberPack addGroupMemberPack = new AddGroupMemberPack();
        addGroupMemberPack.setGroupId(req.getGroupId());
        addGroupMemberPack.setMembers(successId);

        // 生成序列号
        Long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);
        addGroupMemberPack.setSequence(seq);

        // 为所有新加入的群成员写入群组序列号，用于增量同步
        // 因为这些用户的群组列表发生了变化（新增了一个群组）
        for (String memberId : successId) {
            writeUserSeq.writeUserSeq(req.getAppId(), memberId, Constants.SeqConstants.Group, seq);
        }

        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.ADDED_MEMBER, addGroupMemberPack
                , new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));

        // 批量预创建群聊会话记录
        if (!successId.isEmpty()) {
            try {
                conversationPreCreateService.batchPreCreateGroupConversation(req.getAppId(), successId, req.getGroupId());
                log.info("批量群成员加入成功后预创建会话记录完成，userIds: {}, groupId: {}", successId, req.getGroupId());
            } catch (Exception e) {
                log.error("批量群成员加入成功后预创建会话记录失败，userIds: {}, groupId: {}, 错误: {}",
                    successId, req.getGroupId(), e.getMessage(), e);
                // 会话预创建失败不影响群成员加入的主流程，只记录日志
            }
        }

        if(appConfig.isAddGroupMemberAfterCallback()){
            AddMemberAfterCallback dto = new AddMemberAfterCallback();
            dto.setGroupId(req.getGroupId());
            dto.setGroupType(group.getGroupType());
            dto.setMemberId(resp);
            dto.setOperater(req.getOperater());
            callbackService.callback(req.getAppId()
                    ,Constants.CallbackCommand.GroupMemberAddAfter,
                    JSONObject.toJSONString(dto));
        }

        return ResponseVO.successResponse(resp);
    }

    @Override
    public ResponseVO removeMember(RemoveGroupMemberReq req) {

        List<AddMemberResp> resp = new ArrayList<>();
        boolean isAdmin = false;
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }

        ImGroupEntity group = groupResp.getData();

        if (!isAdmin) {
            if (GroupTypeEnum.PUBLIC.getCode() == group.getGroupType()) {

                //获取操作人的权限 是管理员or群主or群成员
                ResponseVO<GetRoleInGroupResp> role = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
                if (!role.isOk()) {
                    return role;
                }

                GetRoleInGroupResp data = role.getData();
                Integer roleInfo = data.getRole();

                boolean isOwner = roleInfo == GroupMemberRoleEnum.OWNER.getCode();
                boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode();

                if (!isOwner && !isManager) {
                    throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
                }

                //私有群必须是群主才能踢人
                if (!isOwner && GroupTypeEnum.PRIVATE.getCode() == group.getGroupType()) {
                    throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
                }

                //公开群管理员和群主可踢人，但管理员只能踢普通群成员
                if (GroupTypeEnum.PUBLIC.getCode() == group.getGroupType()) {
//                    throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
                    //获取被踢人的权限
                    ResponseVO<GetRoleInGroupResp> roleInGroupOne = this.getRoleInGroupOne(req.getGroupId(), req.getMemberId(), req.getAppId());
                    if (!roleInGroupOne.isOk()) {
                        return roleInGroupOne;
                    }
                    GetRoleInGroupResp memberRole = roleInGroupOne.getData();
                    if (memberRole.getRole() == GroupMemberRoleEnum.OWNER.getCode()) {
                        throw new ApplicationException(GroupErrorCode.GROUP_OWNER_IS_NOT_REMOVE);
                    }
                    //是管理员并且被踢人不是群成员，无法操作
                    if (isManager && memberRole.getRole() != GroupMemberRoleEnum.ORDINARY.getCode()) {
                        throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
                    }
                }
            }
        }
        ResponseVO responseVO = groupMemberService.removeGroupMember(req.getGroupId(), req.getAppId(), req.getMemberId());
        if(responseVO.isOk()){
            // 生成序列号，用于增量同步
            Long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);

            // 更新群组序列号，确保群组信息变更能被增量同步
            ImGroupEntity groupUpdate = new ImGroupEntity();
            groupUpdate.setGroupId(req.getGroupId());
            groupUpdate.setSequence(seq);
            groupUpdate.setUpdateTime(System.currentTimeMillis());

            QueryWrapper<ImGroupEntity> groupQuery = new QueryWrapper<>();
            groupQuery.eq("app_id", req.getAppId());
            groupQuery.eq("group_id", req.getGroupId());
            imGroupDataMapper.update(groupUpdate, groupQuery);

            // 更新被踢出用户的群组序列号，确保用户的群组列表变更能被增量同步
            writeUserSeq.writeUserSeq(req.getAppId(), req.getMemberId(), Constants.SeqConstants.Group, seq);

            RemoveGroupMemberPack removeGroupMemberPack = new RemoveGroupMemberPack();
            removeGroupMemberPack.setGroupId(req.getGroupId());
            removeGroupMemberPack.setMember(req.getMemberId());
            removeGroupMemberPack.setSequence(seq);

            groupMessageProducer.producer(req.getOperater(), GroupEventCommand.DELETED_MEMBER, removeGroupMemberPack
                    , new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));
            if(appConfig.isDeleteGroupMemberAfterCallback()){
                callbackService.callback(req.getAppId(),
                        Constants.CallbackCommand.GroupMemberDeleteAfter,
                        JSONObject.toJSONString(req));
            }
        }

        return responseVO;
    }

    @Override
    public ResponseVO<BatchRemoveGroupMemberResp> batchRemoveMember(BatchRemoveGroupMemberReq req) {
        log.info("开始批量移除群成员操作，群组ID: {}, 操作人: {}, 成员数量: {}",
                req.getGroupId(), req.getOperater(), req.getMemberIds().size());

        // 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }

        ImGroupEntity group = groupResp.getData();

        // 权限验证 - 只有群主或管理员可以移除成员
        boolean isAdmin = false;
        if (!isAdmin) {
            if (GroupTypeEnum.PUBLIC.getCode() == group.getGroupType()) {
                // 获取操作人的权限 是管理员or群主or群成员
                ResponseVO<GetRoleInGroupResp> role = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
                if (!role.isOk()) {
                    return ResponseVO.errorResponse(role.getCode(), role.getMsg());
                }

                GetRoleInGroupResp data = role.getData();
                Integer roleInfo = data.getRole();

                boolean isOwner = roleInfo == GroupMemberRoleEnum.OWNER.getCode();
                boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode();

                if (!isOwner && !isManager) {
                    return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
                }

                // 私有群必须是群主才能踢人
                if (!isOwner && GroupTypeEnum.PRIVATE.getCode() == group.getGroupType()) {
                    return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
                }
            }
        }

        // 初始化响应对象
        BatchRemoveGroupMemberResp resp = new BatchRemoveGroupMemberResp();
        resp.setSuccessMemberIds(new ArrayList<>());
        resp.setFailedMembers(new ArrayList<>());
        resp.setTotalCount(req.getMemberIds().size());

        // 批量处理每个成员
        for (String memberId : req.getMemberIds()) {
            try {
                // 创建单个移除请求
                RemoveGroupMemberReq singleReq = new RemoveGroupMemberReq();
                singleReq.setGroupId(req.getGroupId());
                singleReq.setMemberId(memberId);
                singleReq.setAppId(req.getAppId());
                singleReq.setOperater(req.getOperater());
                singleReq.setClientType(req.getClientType());
                singleReq.setImei(req.getImei());

                // 执行单个移除操作
                ResponseVO singleResp = removeMember(singleReq);
                if (singleResp.isOk()) {
                    resp.getSuccessMemberIds().add(memberId);
                    log.info("成功移除用户: {}, 群组: {}", memberId, req.getGroupId());
                } else {
                    resp.getFailedMembers().add(new BatchRemoveGroupMemberResp.FailedMemberInfo(memberId, singleResp.getMsg()));
                    log.warn("移除用户失败: {}, 群组: {}, 原因: {}", memberId, req.getGroupId(), singleResp.getMsg());
                }
            } catch (Exception e) {
                resp.getFailedMembers().add(new BatchRemoveGroupMemberResp.FailedMemberInfo(memberId, "系统异常: " + e.getMessage()));
                log.error("移除用户异常: {}, 群组: {}", memberId, req.getGroupId(), e);
            }
        }

        // 设置统计信息
        resp.setSuccessCount(resp.getSuccessMemberIds().size());
        resp.setFailedCount(resp.getFailedMembers().size());

        log.info("批量移除群成员操作完成，群组ID: {}, 总数: {}, 成功: {}, 失败: {}",
                req.getGroupId(), resp.getTotalCount(), resp.getSuccessCount(), resp.getFailedCount());

        return ResponseVO.successResponse(resp);
    }

    @Override
    public ResponseVO<List<GroupMemberDto>> getGroupMember(String groupId, Integer appId) {
        List<GroupMemberDto> groupMember = imGroupMemberMapper.getGroupMember(appId, groupId);
        return ResponseVO.successResponse(groupMember);
    }

    @Override
    public List<String> getGroupMemberId(String groupId, Integer appId) {
        return imGroupMemberMapper.getGroupMemberId(appId, groupId);
    }

    @Override
    public List<GroupMemberDto> getGroupManager(String groupId, Integer appId) {
        return imGroupMemberMapper.getGroupManager(groupId, appId);
    }

    @Override
    public ResponseVO updateGroupMember(UpdateGroupMemberReq req) {

        boolean isadmin = false;

        ResponseVO<ImGroupEntity> group = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!group.isOk()) {
            return group;
        }

        ImGroupEntity groupData = group.getData();
        if (groupData.getStatus() == GroupStatusEnum.DESTROY.getCode()) {
            throw new ApplicationException(GroupErrorCode.GROUP_IS_DESTROY);
        }

        //是否是自己修改自己的资料
        boolean isMeOperate = req.getOperater().equals(req.getMemberId());

        if (!isadmin) {
            //昵称只能自己修改 权限只能群主或管理员修改
            if (StringUtils.isNotBlank(req.getAlias()) && !isMeOperate) {
                return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_ONESELF);
            }
            //私有群不能设置管理员
            if (groupData.getGroupType() == GroupTypeEnum.PRIVATE.getCode() &&
                    req.getRole() != null && (req.getRole() == GroupMemberRoleEnum.MAMAGER.getCode() ||
                    req.getRole() == GroupMemberRoleEnum.OWNER.getCode())) {
                return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
            }

            //如果要修改权限相关的则走下面的逻辑
            if(req.getRole() != null){
                //获取被操作人的是否在群内
                ResponseVO<GetRoleInGroupResp> roleInGroupOne = this.getRoleInGroupOne(req.getGroupId(), req.getMemberId(), req.getAppId());
                if(!roleInGroupOne.isOk()){
                    return roleInGroupOne;
                }

                //获取操作人权限
                ResponseVO<GetRoleInGroupResp> operateRoleInGroupOne = this.getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
                if(!operateRoleInGroupOne.isOk()){
                    return operateRoleInGroupOne;
                }

                GetRoleInGroupResp data = operateRoleInGroupOne.getData();
                Integer roleInfo = data.getRole();
                boolean isOwner = roleInfo == GroupMemberRoleEnum.OWNER.getCode();
                boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode();

                //不是管理员不能修改权限
                if(req.getRole() != null && !isOwner && !isManager){
                    return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
                }

                //管理员只有群主能够设置
                if(req.getRole() != null && req.getRole() == GroupMemberRoleEnum.MAMAGER.getCode() && !isOwner){
                    return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
                }

            }
        }

        ImGroupMemberEntity update = new ImGroupMemberEntity();

        if (StringUtils.isNotBlank(req.getAlias())) {
            update.setAlias(req.getAlias());
        }

        //不能直接修改为群主
        if(req.getRole() != null && req.getRole() != GroupMemberRoleEnum.OWNER.getCode()){
            update.setRole(req.getRole());
        }

        UpdateWrapper<ImGroupMemberEntity> objectUpdateWrapper = new UpdateWrapper<>();
        objectUpdateWrapper.eq("app_id", req.getAppId());
        objectUpdateWrapper.eq("member_id", req.getMemberId());
        objectUpdateWrapper.eq("group_id", req.getGroupId());
        imGroupMemberMapper.update(update, objectUpdateWrapper);

        UpdateGroupMemberPack pack = new UpdateGroupMemberPack();
        BeanUtils.copyProperties(req, pack);
        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.UPDATED_MEMBER, pack, new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));


        return ResponseVO.successResponse();
    }

    @Override
    @Transactional
    public ResponseVO transferGroupMember(String owner, String groupId, Integer appId) {

        //更新旧群主
        ImGroupMemberEntity imGroupMemberEntity = new ImGroupMemberEntity();
        imGroupMemberEntity.setRole(GroupMemberRoleEnum.ORDINARY.getCode());
        UpdateWrapper<ImGroupMemberEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("app_id", appId);
        updateWrapper.eq("group_id", groupId);
        updateWrapper.eq("role", GroupMemberRoleEnum.OWNER.getCode());
        imGroupMemberMapper.update(imGroupMemberEntity, updateWrapper);

        //更新新群主
        ImGroupMemberEntity newOwner = new ImGroupMemberEntity();
        newOwner.setRole(GroupMemberRoleEnum.OWNER.getCode());
        UpdateWrapper<ImGroupMemberEntity> ownerWrapper = new UpdateWrapper<>();
        ownerWrapper.eq("app_id", appId);
        ownerWrapper.eq("group_id", groupId);
        ownerWrapper.eq("member_id", owner);
        imGroupMemberMapper.update(newOwner, ownerWrapper);

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO speak(SpeaMemberReq req) {
        log.info("开始禁言操作，群组ID: {}, 操作人: {}, 被操作人: {}, 禁言时间: {}",
                req.getGroupId(), req.getOperater(), req.getMemberId(), req.getSpeakDate());

        // 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            log.warn("群组不存在，群组ID: {}", req.getGroupId());
            return groupResp;
        }

        boolean isadmin = false;
        boolean isOwner = false;
        boolean isManager = false;
        GetRoleInGroupResp memberRole = null;

        if (!isadmin) {
            // 获取操作人的权限 是管理员or群主or群成员
            ResponseVO<GetRoleInGroupResp> role = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
            if (!role.isOk()) {
                log.warn("获取操作人权限失败，操作人: {}, 群组ID: {}", req.getOperater(), req.getGroupId());
                return role;
            }

            GetRoleInGroupResp data = role.getData();
            Integer roleInfo = data.getRole();

            isOwner = roleInfo == GroupMemberRoleEnum.OWNER.getCode();
            isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode();

            if (!isOwner && !isManager) {
                log.warn("操作人权限不足，操作人: {}, 群组ID: {}, 角色: {}", req.getOperater(), req.getGroupId(), roleInfo);
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
            }

            // 获取被操作人的权限
            ResponseVO<GetRoleInGroupResp> roleInGroupOne = this.getRoleInGroupOne(req.getGroupId(), req.getMemberId(), req.getAppId());
            if (!roleInGroupOne.isOk()) {
                log.warn("获取被操作人权限失败，被操作人: {}, 群组ID: {}", req.getMemberId(), req.getGroupId());
                return roleInGroupOne;
            }
            memberRole = roleInGroupOne.getData();

            // 被操作人是群主只能app管理员操作
            if (memberRole.getRole() == GroupMemberRoleEnum.OWNER.getCode()) {
                log.warn("尝试禁言群主，操作人: {}, 群主: {}, 群组ID: {}", req.getOperater(), req.getMemberId(), req.getGroupId());
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_APPMANAGER_ROLE);
            }

            // 是管理员并且被操作人不是群成员，无法操作
            if (isManager && memberRole.getRole() != GroupMemberRoleEnum.ORDINARY.getCode()) {
                log.warn("管理员尝试禁言非普通成员，操作人: {}, 被操作人: {}, 被操作人角色: {}, 群组ID: {}",
                        req.getOperater(), req.getMemberId(), memberRole.getRole(), req.getGroupId());
                throw new ApplicationException(GroupErrorCode.THIS_OPERATE_NEED_OWNER_ROLE);
            }
        }

        // 确保获取到被操作人信息
        if(memberRole == null){
            ResponseVO<GetRoleInGroupResp> roleInGroupOne = this.getRoleInGroupOne(req.getGroupId(), req.getMemberId(), req.getAppId());
            if (!roleInGroupOne.isOk()) {
                log.warn("重新获取被操作人权限失败，被操作人: {}, 群组ID: {}", req.getMemberId(), req.getGroupId());
                return roleInGroupOne;
            }
            memberRole = roleInGroupOne.getData();
        }

        // 更新禁言状态
        ImGroupMemberEntity imGroupMemberEntity = new ImGroupMemberEntity();
        imGroupMemberEntity.setGroupMemberId(memberRole.getGroupMemberId());

        // 设置禁言时间
        if(req.getSpeakDate() > 0){
            // 如果传入的是相对时间（毫秒数），则加上当前时间戳
            long speakEndTime = System.currentTimeMillis() + req.getSpeakDate();
            imGroupMemberEntity.setSpeakDate(speakEndTime);
            log.info("设置用户禁言，用户: {}, 群组: {}, 禁言到: {}", req.getMemberId(), req.getGroupId(), speakEndTime);
        }else{
            // 如果传入0，表示解除禁言
            imGroupMemberEntity.setSpeakDate(req.getSpeakDate());
            log.info("解除用户禁言，用户: {}, 群组: {}", req.getMemberId(), req.getGroupId());
        }

        // 执行数据库更新
        int updateResult = imGroupMemberMapper.updateById(imGroupMemberEntity);
        if(updateResult == 1){
            // 发送WebSocket通知
            GroupMemberSpeakPack pack = new GroupMemberSpeakPack();
            BeanUtils.copyProperties(req,pack);
            groupMessageProducer.producer(req.getOperater(),GroupEventCommand.SPEAK_GOUP_MEMBER,pack,
                    new ClientInfo(req.getAppId(),req.getClientType(),req.getImei()));

            log.info("禁言操作成功，操作人: {}, 被操作人: {}, 群组: {}", req.getOperater(), req.getMemberId(), req.getGroupId());
        } else {
            log.error("禁言操作失败，数据库更新失败，操作人: {}, 被操作人: {}, 群组: {}", req.getOperater(), req.getMemberId(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.UPDATE_GROUP_BASE_INFO_ERROR);
        }

        return ResponseVO.successResponse();
    }

    @Override
    public ResponseVO<BatchSpeakMemberResp> batchSpeak(BatchSpeakMemberReq req) {
        log.info("开始批量禁言操作，群组ID: {}, 操作人: {}, 成员数量: {}",
                req.getGroupId(), req.getOperater(), req.getMemberIds().size());

        // 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }

        // 权限验证 - 只有群主或管理员可以禁言
        boolean isAdmin = false;
        if (!isAdmin) {
            ResponseVO<GetRoleInGroupResp> role = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
            if (!role.isOk()) {
                return ResponseVO.errorResponse(role.getCode(), role.getMsg());
            }

            GetRoleInGroupResp data = role.getData();
            Integer roleInfo = data.getRole();
            boolean isOwner = roleInfo == GroupMemberRoleEnum.OWNER.getCode();
            boolean isManager = roleInfo == GroupMemberRoleEnum.MAMAGER.getCode();

            if (!isOwner && !isManager) {
                return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
            }
        }

        // 初始化响应对象
        BatchSpeakMemberResp resp = new BatchSpeakMemberResp();
        resp.setSuccessMemberIds(new ArrayList<>());
        resp.setFailedMembers(new ArrayList<>());
        resp.setTotalCount(req.getMemberIds().size());

        // 批量处理每个成员
        for (String memberId : req.getMemberIds()) {
            try {
                // 创建单个禁言请求
                SpeaMemberReq singleReq = new SpeaMemberReq();
                singleReq.setGroupId(req.getGroupId());
                singleReq.setMemberId(memberId);
                singleReq.setSpeakDate(req.getSpeakDate());
                singleReq.setAppId(req.getAppId());
                singleReq.setOperater(req.getOperater());
                singleReq.setClientType(req.getClientType());
                singleReq.setImei(req.getImei());

                // 执行单个禁言操作
                ResponseVO singleResp = speak(singleReq);
                if (singleResp.isOk()) {
                    resp.getSuccessMemberIds().add(memberId);
                    log.info("成功禁言用户: {}, 群组: {}", memberId, req.getGroupId());
                } else {
                    resp.getFailedMembers().add(new BatchSpeakMemberResp.FailedMemberInfo(memberId, singleResp.getMsg()));
                    log.warn("禁言用户失败: {}, 群组: {}, 原因: {}", memberId, req.getGroupId(), singleResp.getMsg());
                }
            } catch (Exception e) {
                resp.getFailedMembers().add(new BatchSpeakMemberResp.FailedMemberInfo(memberId, "系统异常: " + e.getMessage()));
                log.error("禁言用户异常: {}, 群组: {}", memberId, req.getGroupId(), e);
            }
        }

        // 设置统计信息
        resp.setSuccessCount(resp.getSuccessMemberIds().size());
        resp.setFailedCount(resp.getFailedMembers().size());

        log.info("批量禁言操作完成，群组ID: {}, 总数: {}, 成功: {}, 失败: {}",
                req.getGroupId(), resp.getTotalCount(), resp.getSuccessCount(), resp.getFailedCount());

        return ResponseVO.successResponse(resp);
    }

    @Override
    public ResponseVO<Collection<String>> syncMemberJoinedGroup(String operater, Integer appId) {
        return ResponseVO.successResponse(imGroupMemberMapper.syncJoinedGroupId(appId,operater,GroupMemberRoleEnum.LEAVE.getCode()));
    }

    /**
     * 申请加群
     */
    @Override
    public ResponseVO applyJoinGroup(ApplyJoinGroupReq req) {
        log.info("用户申请加群，申请人: {}, 群组: {}, 申请理由: {}", req.getOperater(), req.getGroupId(), req.getApplyReason());

        // 1. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 检查群组状态
        if (group.getStatus() != GroupStatusEnum.NORMAL.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_IS_DESTROY);
        }

        // 3. 检查申请加群权限
        if (group.getApplyJoinType() == 0) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_TYPE_NOT_ALLOW);
        }

        // 4. 检查群成员数量是否已达上限
        List<String> currentMembers = getGroupMemberId(req.getGroupId(), req.getAppId());
        if (group.getMaxMemberCount() != null && currentMembers.size() >= group.getMaxMemberCount()) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_MEMBER_IS_BEYOND);
        }

        // 5. 检查用户是否已经是群成员
        ResponseVO<GetRoleInGroupResp> roleResp = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
        if (roleResp.isOk()) {
            return ResponseVO.errorResponse(GroupErrorCode.USER_IS_JOINED_GROUP);
        }

        // 6. 检查是否有待审批的申请
        ImGroupApplyEntity existingApply = imGroupApplyMapper.getPendingApply(req.getAppId(), req.getGroupId(), req.getOperater());
        if (existingApply != null) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_ALREADY_EXISTS);
        }

        // 7. 检查申请频率限制（24小时内最多申请3次）
        long oneDayAgo = System.currentTimeMillis() - 24 * 60 * 60 * 1000L;
        List<ImGroupApplyEntity> recentApplies = imGroupApplyMapper.getUserApplyHistory(req.getAppId(), req.getOperater(), 10);
        int recentApplyCount = 0;
        for (ImGroupApplyEntity apply : recentApplies) {
            if (apply.getApplyTime() > oneDayAgo && apply.getGroupId().equals(req.getGroupId())) {
                recentApplyCount++;
            }
        }
        if (recentApplyCount >= 3) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_FAILED.getCode(), "24小时内申请次数过多，请稍后再试");
        }

        // 8. 验证入群门槛
        ResponseVO thresholdCheckResult = validateJoinThreshold(group, req.getOperater());
        if (!thresholdCheckResult.isOk()) {
            return thresholdCheckResult;
        }

        // 9. 创建申请记录
        ImGroupApplyEntity applyEntity = new ImGroupApplyEntity();
        applyEntity.setAppId(req.getAppId());
        applyEntity.setGroupId(req.getGroupId());
        applyEntity.setApplicantId(req.getOperater());
        applyEntity.setApplyStatus(0); // 待审批
        applyEntity.setApplyReason(req.getApplyReason());
        applyEntity.setApplyTime(System.currentTimeMillis());
        applyEntity.setCreateTime(System.currentTimeMillis());
        applyEntity.setExtra(req.getExtra());

        // 7. 生成序列号
        long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.GroupApply);
        applyEntity.setSequence(seq);

        int result = imGroupApplyMapper.insert(applyEntity);
        if (result != 1) {
            log.error("申请加群失败，数据库插入失败，申请人: {}, 群组: {}", req.getOperater(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_FAILED);
        }

        // 8. 发送WebSocket通知给群主和管理员
        ApplyJoinGroupPack applyPack = new ApplyJoinGroupPack();
        applyPack.setApplyId(applyEntity.getApplyId());
        applyPack.setGroupId(req.getGroupId());
        applyPack.setApplicantId(req.getOperater());
        applyPack.setApplyReason(req.getApplyReason());
        applyPack.setApplyTime(applyEntity.getApplyTime());
        applyPack.setGroupName(group.getGroupName());
        applyPack.setAppId(req.getAppId());
        applyPack.setSequence(seq);

        // 查询申请人昵称和头像信息
        try {
            ResponseVO<ImUserDataEntity> applicantResp = imUserService.getSingleUserInfo(req.getOperater(), req.getAppId());
            if (applicantResp.isOk() && applicantResp.getData() != null) {
                ImUserDataEntity applicantInfo = applicantResp.getData();
                applyPack.setApplicantNickname(applicantInfo.getNickName());
                applyPack.setApplicantAvatar(applicantInfo.getPhoto());
                log.debug("获取申请人信息成功，用户: {}, 昵称: {}", req.getOperater(), applicantInfo.getNickName());
            } else {
                log.warn("获取申请人信息失败，用户: {}, 错误: {}", req.getOperater(), applicantResp.getMsg());
                applyPack.setApplicantNickname("未知用户");
                applyPack.setApplicantAvatar("");
            }
        } catch (Exception e) {
            log.error("查询申请人信息异常，用户: {}, 异常: {}", req.getOperater(), e.getMessage(), e);
            applyPack.setApplicantNickname("未知用户");
            applyPack.setApplicantAvatar("");
        }

        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.APPLY_JOIN_GROUP, applyPack,
                new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));

        log.info("申请加群成功，申请人: {}, 群组: {}, 申请ID: {}", req.getOperater(), req.getGroupId(), applyEntity.getApplyId());
        return ResponseVO.successResponse();
    }

    /**
     * 审批群申请
     */
    @Override
    @Transactional
    public ResponseVO approveGroupApply(ApproveGroupApplyReq req) {
        log.info("审批群申请，审批人: {}, 申请ID: {}, 审批结果: {}", req.getOperater(), req.getApplyId(), req.getApproveResult());

        // 1. 查询申请记录
        ImGroupApplyEntity applyEntity = imGroupApplyMapper.selectById(req.getApplyId());
        if (applyEntity == null) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_NOT_EXIST);
        }

        // 2. 检查申请状态
        if (applyEntity.getApplyStatus() != 0) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_STATUS_ERROR);
        }

        // 3. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(applyEntity.getGroupId(), applyEntity.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }
        ImGroupEntity group = groupResp.getData();

        // 4. 检查审批人权限（必须是群主或管理员）
        ResponseVO<GetRoleInGroupResp> roleResp = getRoleInGroupOne(applyEntity.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleResp.isOk()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }
        GetRoleInGroupResp roleData = roleResp.getData();
        if (roleData.getRole() != GroupMemberRoleEnum.OWNER.getCode() &&
            roleData.getRole() != GroupMemberRoleEnum.MAMAGER.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 5. 更新申请状态
        ImGroupApplyEntity updateEntity = new ImGroupApplyEntity();
        updateEntity.setApplyId(req.getApplyId());
        updateEntity.setApproverId(req.getOperater());
        updateEntity.setApproveTime(System.currentTimeMillis());
        updateEntity.setUpdateTime(System.currentTimeMillis());

        if (req.getApproveResult() == 1) {
            // 同意申请
            updateEntity.setApplyStatus(1);

            // 6. 检查用户是否已经是群成员
            ResponseVO<GetRoleInGroupResp> memberRoleResp = getRoleInGroupOne(applyEntity.getGroupId(), applyEntity.getApplicantId(), applyEntity.getAppId());
            if (memberRoleResp.isOk()) {
                // 用户已经是群成员，直接更新申请状态
                int updateResult = imGroupApplyMapper.updateById(updateEntity);
                if (updateResult != 1) {
                    log.error("审批申请失败，数据库更新失败，申请ID: {}", req.getApplyId());
                    return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_APPROVE_FAILED);
                }
                return ResponseVO.successResponse();
            }

            // 7. 检查群成员数量是否已达上限
            List<String> currentMembers = getGroupMemberId(applyEntity.getGroupId(), applyEntity.getAppId());
            if (group.getMaxMemberCount() != null && currentMembers.size() >= group.getMaxMemberCount()) {
                // 群已满，更新申请状态为拒绝
                updateEntity.setApplyStatus(2);
                updateEntity.setRejectReason("群成员已达上限");
                int updateResult = imGroupApplyMapper.updateById(updateEntity);
                if (updateResult == 1) {
                    log.info("群已满，自动拒绝申请，申请ID: {}", req.getApplyId());
                }
                return ResponseVO.errorResponse(GroupErrorCode.GROUP_MEMBER_IS_BEYOND);
            }

            // 8. 添加用户到群组
            GroupMemberDto memberDto = new GroupMemberDto();
            memberDto.setMemberId(applyEntity.getApplicantId());
            memberDto.setRole(GroupMemberRoleEnum.ORDINARY.getCode());
            memberDto.setJoinTime(System.currentTimeMillis());
            memberDto.setJoinType("apply");

            ResponseVO addMemberResp = addGroupMember(applyEntity.getGroupId(), applyEntity.getAppId(), memberDto);
            if (!addMemberResp.isOk()) {
                log.error("审批申请失败，添加群成员失败，申请ID: {}, 错误: {}", req.getApplyId(), addMemberResp.getMsg());
                return addMemberResp;
            }
        } else if (req.getApproveResult() == 2) {
            // 拒绝申请
            updateEntity.setApplyStatus(2);
            updateEntity.setRejectReason(req.getRejectReason());
        } else {
            return ResponseVO.errorResponse(BaseErrorCode.PARAMETER_ERROR);
        }

        // 8. 更新申请记录
        int updateResult = imGroupApplyMapper.updateById(updateEntity);
        if (updateResult != 1) {
            log.error("审批申请失败，数据库更新失败，申请ID: {}", req.getApplyId());
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_APPLY_APPROVE_FAILED);
        }

        // 9. 发送WebSocket通知给申请人
        ApproveGroupApplyPack approvePack = new ApproveGroupApplyPack();
        approvePack.setApplyId(req.getApplyId());
        approvePack.setGroupId(applyEntity.getGroupId());
        approvePack.setGroupName(group.getGroupName());
        approvePack.setApplicantId(applyEntity.getApplicantId());
        approvePack.setApproverId(req.getOperater());
        approvePack.setApproveResult(req.getApproveResult());
        approvePack.setRejectReason(req.getRejectReason());
        approvePack.setApproveTime(updateEntity.getApproveTime());
        approvePack.setAppId(req.getAppId());

        // 查询审批人昵称信息
        try {
            ResponseVO<ImUserDataEntity> approverResp = imUserService.getSingleUserInfo(req.getOperater(), req.getAppId());
            if (approverResp.isOk() && approverResp.getData() != null) {
                ImUserDataEntity approverInfo = approverResp.getData();
                approvePack.setApproverNickname(approverInfo.getNickName());
                log.debug("获取审批人信息成功，用户: {}, 昵称: {}", req.getOperater(), approverInfo.getNickName());
            } else {
                log.warn("获取审批人信息失败，用户: {}, 错误: {}", req.getOperater(), approverResp.getMsg());
                approvePack.setApproverNickname("未知用户");
            }
        } catch (Exception e) {
            log.error("查询审批人信息异常，用户: {}, 异常: {}", req.getOperater(), e.getMessage(), e);
            approvePack.setApproverNickname("未知用户");
        }

        groupMessageProducer.producer(req.getOperater(), GroupEventCommand.APPROVE_GROUP_APPLY, approvePack,
                new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));

        log.info("审批申请成功，审批人: {}, 申请ID: {}, 审批结果: {}", req.getOperater(), req.getApplyId(), req.getApproveResult());
        return ResponseVO.successResponse();
    }

    /**
     * 自由加入群组
     */
    @Override
    public ResponseVO freeJoinGroup(FreeJoinGroupReq req) {
        log.info("用户自由加入群组，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());

        // 1. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return groupResp;
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 检查群组状态
        if (group.getStatus() != GroupStatusEnum.NORMAL.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_IS_DESTROY);
        }

        // 3. 检查是否允许自由加入
        if (group.getApplyJoinType() != 2) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_TYPE_NOT_ALLOW);
        }

        // 4. 检查群成员数量是否已达上限
        List<String> currentMembers = getGroupMemberId(req.getGroupId(), req.getAppId());
        if (group.getMaxMemberCount() != null && currentMembers.size() >= group.getMaxMemberCount()) {
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_MEMBER_IS_BEYOND);
        }

        // 5. 检查用户是否已经是群成员
        ResponseVO<GetRoleInGroupResp> roleResp = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
        if (roleResp.isOk()) {
            return ResponseVO.errorResponse(GroupErrorCode.USER_IS_JOINED_GROUP);
        }

        // 5. 直接添加用户到群组
        GroupMemberDto memberDto = new GroupMemberDto();
        memberDto.setMemberId(req.getOperater());
        memberDto.setRole(GroupMemberRoleEnum.ORDINARY.getCode());
        memberDto.setJoinTime(System.currentTimeMillis());
        memberDto.setJoinType("free");
        memberDto.setAlias(req.getAlias());

        ResponseVO addMemberResp = addGroupMember(req.getGroupId(), req.getAppId(), memberDto);
        if (!addMemberResp.isOk()) {
            log.error("自由加入群组失败，添加群成员失败，用户: {}, 群组: {}, 错误: {}", req.getOperater(), req.getGroupId(), addMemberResp.getMsg());
            return addMemberResp;
        }

        log.info("自由加入群组成功，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
        return ResponseVO.successResponse();
    }

    /**
     * 查询群申请列表
     */
    @Override
    public ResponseVO<GroupApplyListResp> getGroupApplyList(GetGroupApplyListReq req) {
        log.info("查询群申请列表，群组: {}, 状态: {}, 页码: {}, 页大小: {}", req.getGroupId(), req.getApplyStatus(), req.getPageNum(), req.getPageSize());

        // 1. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }

        // 2. 检查查询权限（必须是群主或管理员）
        ResponseVO<GetRoleInGroupResp> roleResp = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleResp.isOk()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }
        GetRoleInGroupResp roleData = roleResp.getData();
        if (roleData.getRole() != GroupMemberRoleEnum.OWNER.getCode() &&
            roleData.getRole() != GroupMemberRoleEnum.MAMAGER.getCode()) {
            return ResponseVO.errorResponse(GroupErrorCode.THIS_OPERATE_NEED_MANAGER_ROLE);
        }

        // 3. 分页查询申请列表
        Page<ImGroupApplyEntity> page = new Page<>(req.getPageNum(), req.getPageSize());
        IPage<ImGroupApplyEntity> pageResult = imGroupApplyMapper.getGroupApplyListPage(
            page, req.getAppId(), req.getGroupId(), req.getApplyStatus(),
            req.getStartTime(), req.getEndTime());

        List<ImGroupApplyEntity> applyList = pageResult.getRecords();
        Long totalCount = pageResult.getTotal();

        // 5. 转换为DTO并补充用户信息
        List<GroupApplyDto> applyDtoList = new ArrayList<>();
        if (!applyList.isEmpty()) {
            // 5.1 收集所有需要查询的用户ID
            Set<String> userIds = new HashSet<>();
            for (ImGroupApplyEntity entity : applyList) {
                userIds.add(entity.getApplicantId());
                if (entity.getApproverId() != null) {
                    userIds.add(entity.getApproverId());
                }
            }

            // 5.2 批量查询用户信息
            Map<String, ImUserDataEntity> userInfoMap = imUserService.batchGetUserInfo(userIds, req.getAppId());

            // 5.3 转换为DTO并设置用户信息
            for (ImGroupApplyEntity entity : applyList) {
                GroupApplyDto dto = new GroupApplyDto();
                BeanUtils.copyProperties(entity, dto);
                // 设置申请人信息
                ImUserDataEntity applicantInfo = userInfoMap.get(entity.getApplicantId());
                if (applicantInfo != null) {
                    dto.setApplicantNickname(applicantInfo.getNickName());
                    dto.setApplicantAvatar(applicantInfo.getPhoto());
                } else {
                    log.warn("未找到申请人用户信息，申请人ID: {}, 申请ID: {}", entity.getApplicantId(), entity.getApplyId());
                    dto.setApplicantNickname("未知用户");
                    dto.setApplicantAvatar("");
                }
                // 设置审批人信息（如果已审批）
                if (entity.getApproverId() != null) {
                    ImUserDataEntity approverInfo = userInfoMap.get(entity.getApproverId());
                    if (approverInfo != null) {
                        dto.setApproverNickname(approverInfo.getNickName());
                    } else {
                        log.warn("未找到审批人用户信息，审批人ID: {}, 申请ID: {}", entity.getApproverId(), entity.getApplyId());
                        dto.setApproverNickname("未知用户");
                    }
                }

                applyDtoList.add(dto);
            }

            log.debug("查询群申请列表用户信息完成，查询用户数: {}, 成功获取: {}", userIds.size(), userInfoMap.size());
        }

        // 6. 构建响应（使用MyBatis-Plus分页对象的便利方法）
        GroupApplyListResp resp = new GroupApplyListResp();
        resp.setApplyList(applyDtoList);
        resp.setTotalCount(pageResult.getTotal());
        resp.setPageNum(pageResult.getCurrent());
        resp.setPageSize(pageResult.getSize());
        resp.setTotalPages(pageResult.getPages());
        resp.setHasNext(pageResult.getCurrent() < pageResult.getPages());
        resp.setHasPrevious(pageResult.getCurrent() > 1);

        log.info("查询群申请列表成功，群组: {}, 总数: {}, 当前页: {}", req.getGroupId(), totalCount, req.getPageNum());
        return ResponseVO.successResponse(resp);
    }

    /**
     * 查询用户在指定群组的最近申请记录
     */
    @Override
    public ResponseVO<UserApplyListResp> getUserApplyList(GetUserApplyListReq req) {
        log.info("查询用户最近申请记录，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());

        // 1. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            log.warn("群组不存在，群组ID: {}", req.getGroupId());
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 查询用户在该群组的最近申请记录
        ImGroupApplyEntity applyEntity = imGroupApplyMapper.getUserLatestApply(
                req.getAppId(), req.getOperater(), req.getGroupId());

        // 3. 构建响应
        UserApplyListResp resp = new UserApplyListResp();

        if (applyEntity != null) {
            // 3.1 转换为DTO
            UserApplyDto dto = new UserApplyDto();
            dto.setApplyId(applyEntity.getApplyId());
            dto.setGroupId(applyEntity.getGroupId());
            dto.setApplyStatus(applyEntity.getApplyStatus());
            dto.setApplyStatusDesc(getApplyStatusDesc(applyEntity.getApplyStatus()));
            dto.setApplyReason(applyEntity.getApplyReason());
            dto.setApplyTime(applyEntity.getApplyTime());
            dto.setApproverId(applyEntity.getApproverId());
            dto.setApproveTime(applyEntity.getApproveTime());
            dto.setRejectReason(applyEntity.getRejectReason());

            // 3.2 设置群组信息
            dto.setGroupName(group.getGroupName());
            dto.setGroupAvatar(group.getPhoto());

            // 3.3 设置审批人信息
            if (applyEntity.getApproverId() != null) {
                ResponseVO<ImUserDataEntity> approverResp = imUserService.getSingleUserInfo(
                        applyEntity.getApproverId(), req.getAppId());
                if (approverResp.isOk()) {
                    dto.setApproverNickname(approverResp.getData().getNickName());
                }
            }

            resp.setLatestApply(dto);
            log.info("查询用户最近申请记录成功，用户: {}, 群组: {}, 申请ID: {}, 状态: {}",
                    req.getOperater(), req.getGroupId(), dto.getApplyId(), dto.getApplyStatusDesc());
        } else {
            resp.setLatestApply(null);
            log.info("用户在该群组没有申请记录，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
        }

        return ResponseVO.successResponse(resp);
    }

    /**
     * 获取申请状态描述
     */
    private String getApplyStatusDesc(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0: return "待审批";
            case 1: return "已同意";
            case 2: return "已拒绝";
            case 3: return "已撤销";
            default: return "未知";
        }
    }

    /**
     * 退出群组
     * @param req 退出群组请求
     * @return 操作结果
     */
    @Override
    @Transactional
    public ResponseVO<Object> exitGroup(ExitGroupReq req) {
        log.info("用户退出群组，用户: {}, 群组: {}, 退出原因: {}", req.getOperater(), req.getGroupId(), req.getExitReason());

        // 1. 验证群组是否存在
        ResponseVO<ImGroupEntity> groupResp = groupService.getGroup(req.getGroupId(), req.getAppId());
        if (!groupResp.isOk()) {
            log.warn("群组不存在，群组ID: {}", req.getGroupId());
            return ResponseVO.errorResponse(groupResp.getCode(), groupResp.getMsg());
        }
        ImGroupEntity group = groupResp.getData();

        // 2. 检查群组状态
        if (group.getStatus() != GroupStatusEnum.NORMAL.getCode()) {
            log.warn("群组已解散，无法退出，群组ID: {}", req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_IS_DESTROY);
        }

        // 3. 权限验证 - 只能退出自己，不能退出其他人
        // 注意：退出群组操作中，操作人(operater)就是要退出的人，无需额外的memberId参数

        // 4. 验证用户是否在群内
        ResponseVO<GetRoleInGroupResp> roleResp = getRoleInGroupOne(req.getGroupId(), req.getOperater(), req.getAppId());
        if (!roleResp.isOk()) {
            log.warn("用户不在群内，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.MEMBER_IS_NOT_JOINED_GROUP);
        }

        GetRoleInGroupResp roleData = roleResp.getData();

        // 4. 检查用户角色 - 群主需要先转让群主身份才能退出
        if (roleData.getRole() == GroupMemberRoleEnum.OWNER.getCode()) {
            log.warn("群主不能直接退出群组，需要先转让群主身份，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_OWNER_IS_NOT_REMOVE);
        }

        // 5. 检查用户是否已经离开
        if (roleData.getRole() == GroupMemberRoleEnum.LEAVE.getCode()) {
            log.warn("用户已经退出群组，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.MEMBER_IS_NOT_JOINED_GROUP);
        }

        // 6. 生成序列号，用于增量同步
        Long seq = redisSeq.doGetSeq(req.getAppId() + ":" + Constants.SeqConstants.Group);

        // 7. 执行退出操作 - 更新成员状态为离开
        ImGroupMemberEntity updateEntity = new ImGroupMemberEntity();
        updateEntity.setGroupMemberId(roleData.getGroupMemberId());
        updateEntity.setRole(GroupMemberRoleEnum.LEAVE.getCode());
        updateEntity.setLeaveTime(System.currentTimeMillis());

        int updateResult = imGroupMemberMapper.updateById(updateEntity);
        if (updateResult != 1) {
            log.error("退出群组失败，数据库更新失败，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
            return ResponseVO.errorResponse(GroupErrorCode.UPDATE_GROUP_BASE_INFO_ERROR);
        }

        // 7.5. 发送群成员退出系统消息给群主和管理员
        sendMemberExitSystemMessage(req.getGroupId(), req.getOperater(), req.getAppId());

        // 8. 更新群组序列号，确保群组信息变更能被增量同步
        ImGroupEntity groupUpdate = new ImGroupEntity();
        groupUpdate.setGroupId(req.getGroupId());
        groupUpdate.setSequence(seq);
        groupUpdate.setUpdateTime(System.currentTimeMillis());

        QueryWrapper<ImGroupEntity> groupQuery = new QueryWrapper<>();
        groupQuery.eq("app_id", req.getAppId());
        groupQuery.eq("group_id", req.getGroupId());
        imGroupDataMapper.update(groupUpdate, groupQuery);

        // 9. 更新用户的群组序列号，确保用户的群组列表变更能被增量同步
        writeUserSeq.writeUserSeq(req.getAppId(), req.getOperater(), Constants.SeqConstants.Group, seq);

        // 10. 发送WebSocket通知 - 使用EXIT_GROUP事件
        ExitGroupPack exitGroupPack = new ExitGroupPack();
        exitGroupPack.setGroupId(req.getGroupId());
        exitGroupPack.setMemberId(req.getOperater());
        exitGroupPack.setExitReason(req.getExitReason());
        exitGroupPack.setSequence(seq);

        try {
            groupMessageProducer.producer(req.getOperater(), GroupEventCommand.EXIT_GROUP, exitGroupPack,
                    new ClientInfo(req.getAppId(), req.getClientType(), req.getImei()));
        } catch (Exception e) {
            log.error("发送退出群组WebSocket通知失败，用户: {}, 群组: {}, 错误: {}",
                    req.getOperater(), req.getGroupId(), e.getMessage(), e);
            // WebSocket通知失败不影响退出操作的成功，只记录日志
        }

        // 8. 回调通知（如果配置了回调）
        if (appConfig.isDeleteGroupMemberAfterCallback()) {
            JSONObject callbackData = new JSONObject();
            callbackData.put("groupId", req.getGroupId());
            callbackData.put("memberId", req.getOperater());
            callbackData.put("exitReason", req.getExitReason());
            callbackData.put("operateType", "exit"); // 区分主动退出和被移除
            callbackData.put("operateTime", System.currentTimeMillis());

            callbackService.callback(req.getAppId(),
                    Constants.CallbackCommand.GroupMemberDeleteAfter,
                    callbackData.toJSONString());
        }

        log.info("用户退出群组成功，用户: {}, 群组: {}", req.getOperater(), req.getGroupId());
        return ResponseVO.successResponse();
    }

    /**
     * 验证入群门槛
     * @param group 群组信息
     * @param applicantId 申请人ID
     * @return 验证结果
     */
    private ResponseVO validateJoinThreshold(ImGroupEntity group, String applicantId) {
        log.info("开始验证入群门槛，群组: {}, 申请人: {}", group.getGroupId(), applicantId);

        try {
            // 1. 解析群组的入群门槛配置
            GroupJoinThresholdConfig thresholdConfig = groupJoinThresholdChecker.parseThresholdConfig(group.getExtra());
            GroupJoinThresholdEnum thresholdType = thresholdConfig.getType();

            log.debug("群组入群门槛类型: {}, 群组: {}, 申请人: {}", thresholdType, group.getGroupId(), applicantId);

            // 2. 根据门槛类型进行验证
            switch (thresholdType) {
                case NO_REQUIREMENT:
                    // 无要求，直接通过
                    log.info("群组无入群门槛要求，直接通过，群组: {}, 申请人: {}", group.getGroupId(), applicantId);
                    return ResponseVO.successResponse();

                case FOLLOW_ME:
                    // 需要关注群主
                    return validateFollowRequirement(group.getOwnerId(), applicantId, group.getGroupId());

                case FOLLOW_ME_7_DAYS:
                    // 需要关注群主超过7天
                    return validateFollowDaysRequirement(group.getOwnerId(), applicantId, group.getGroupId(), 7);

                case FOLLOW_ME_30_DAYS:
                    // 需要关注群主超过30天
                    return validateFollowDaysRequirement(group.getOwnerId(), applicantId, group.getGroupId(), 30);

                default:
                    log.warn("未知的入群门槛类型: {}, 群组: {}, 申请人: {}", thresholdType, group.getGroupId(), applicantId);
                    return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_CONFIG_ERROR);
            }

        } catch (Exception e) {
            log.error("验证入群门槛时发生异常，群组: {}, 申请人: {}, 异常: {}",
                group.getGroupId(), applicantId, e.getMessage(), e);
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_CHECK_FAILED);
        }
    }

    /**
     * 验证关注要求
     * @param ownerId 群主ID
     * @param applicantId 申请人ID
     * @param groupId 群组ID
     * @return 验证结果
     */
    private ResponseVO validateFollowRequirement(String ownerId, String applicantId, String groupId) {
        log.info("验证关注要求，群主: {}, 申请人: {}, 群组: {}", ownerId, applicantId, groupId);

        try {
            boolean isFollowing = focusDayCheckService.isFollowing(applicantId, ownerId);
            if (!isFollowing) {
                log.info("用户未关注群主，验证失败，群主: {}, 申请人: {}, 群组: {}", ownerId, applicantId, groupId);
                return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_NOT_FOLLOW);
            }

            log.info("用户已关注群主，验证通过，群主: {}, 申请人: {}, 群组: {}", ownerId, applicantId, groupId);
            return ResponseVO.successResponse();

        } catch (Exception e) {
            log.error("验证关注要求时发生异常，群主: {}, 申请人: {}, 群组: {}, 异常: {}",
                ownerId, applicantId, groupId, e.getMessage(), e);
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_CHECK_FAILED);
        }
    }

    /**
     * 验证关注天数要求
     * @param ownerId 群主ID
     * @param applicantId 申请人ID
     * @param groupId 群组ID
     * @param requiredDays 要求的关注天数
     * @return 验证结果
     */
    private ResponseVO validateFollowDaysRequirement(String ownerId, String applicantId, String groupId, int requiredDays) {
        log.info("验证关注天数要求，群主: {}, 申请人: {}, 群组: {}, 要求天数: {}",
            ownerId, applicantId, groupId, requiredDays);

        try {
            boolean meetsRequirement = focusDayCheckService.checkFocusDaysRequirement(applicantId, ownerId, requiredDays);
            if (!meetsRequirement) {
                log.info("用户关注天数不足，验证失败，群主: {}, 申请人: {}, 群组: {}, 要求天数: {}",
                    ownerId, applicantId, groupId, requiredDays);
                return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_FOLLOW_DAYS_NOT_ENOUGH);
            }

            log.info("用户关注天数满足要求，验证通过，群主: {}, 申请人: {}, 群组: {}, 要求天数: {}",
                ownerId, applicantId, groupId, requiredDays);
            return ResponseVO.successResponse();

        } catch (Exception e) {
            log.error("验证关注天数要求时发生异常，群主: {}, 申请人: {}, 群组: {}, 要求天数: {}, 异常: {}",
                ownerId, applicantId, groupId, requiredDays, e.getMessage(), e);
            return ResponseVO.errorResponse(GroupErrorCode.GROUP_JOIN_THRESHOLD_CHECK_FAILED);
        }
    }

    /**
     * 获取用户昵称
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 用户昵称，如果获取失败则返回默认值
     */
    private String getUserNickname(String userId, Integer appId) {
        if (StringUtils.isEmpty(userId)) {
            return "新成员";
        }

        try {
            ResponseVO<Map<String, Object>> userInfoResp = imUserService.getUserInfo(userId, appId);
            if (userInfoResp.isOk() && userInfoResp.getData() != null) {
                String nickname = (String) userInfoResp.getData().get("nickName");
                return StringUtils.isNotEmpty(nickname) ? nickname : "新成员";
            }
        } catch (Exception e) {
            log.warn("获取用户昵称失败: userId={}, appId={}, error={}", userId, appId, e.getMessage());
        }

        // 如果获取失败，返回默认值
        return "新成员";
    }

    /**
     * 发送群成员加入系统消息
     * @param groupId 群组ID
     * @param userId 加入的用户ID
     * @param appId 应用ID
     */
    private void sendMemberJoinSystemMessage(String groupId, String userId, Integer appId) {
        try {
            // 获取加入用户的昵称
            String userNickname = getUserNickname(userId, appId);

            // 构建系统消息内容,前端处理国际化
            String messageContent = userNickname;

            // 创建群聊系统消息
            GroupChatMessageContent systemMessage = new GroupChatMessageContent();
            systemMessage.setMessageId(UUID.randomUUID().toString());
            systemMessage.setFromId(userId);
            systemMessage.setGroupId(groupId);
            systemMessage.setMessageBody(messageContent);
            systemMessage.setMessageTime(System.currentTimeMillis());
            systemMessage.setAppId(appId);
            systemMessage.setClientType(0);
            systemMessage.setImei("system");

            // 设置消息类型为系统消息
            systemMessage.setExtra("82");

            // 使用系统消息专用方法发送（绕过权限验证）
            groupMessageService.processSystemMessage(systemMessage);

            log.info("群成员加入系统消息发送成功，群组: {}, 用户: {}, 昵称: {}",
                    groupId, userId, userNickname);

        } catch (Exception e) {
            log.error("发送群成员加入系统消息失败，群组: {}, 用户: {}, 错误: {}",
                    groupId, userId, e.getMessage(), e);
            // 系统消息发送失败不影响加群的主流程，只记录错误日志
        }
    }

    /**
     * 发送群成员移除系统消息给当事人、群主和管理员
     * @param groupId 群组ID
     * @param userId 被移除的用户ID
     * @param appId 应用ID
     */
    private void sendMemberRemoveSystemMessage(String groupId, String userId, Integer appId) {
        try {
            // 获取被移除用户的昵称
            String userNickname = getUserNickname(userId, appId);

            // 构建系统消息内容,前端处理国际化
            String messageContent = userNickname;

            // 获取群主和管理员ID列表
            List<String> adminIds = getGroupOwnerAndManagerIds(groupId, appId);

            // 创建目标接收者列表：被移除的用户 + 群主和管理员
            List<String> targetMembers = new ArrayList<>();
            targetMembers.add(userId); // 被移除的当事人

            // 添加群主和管理员（避免重复添加）
            for (String adminId : adminIds) {
                if (!targetMembers.contains(adminId)) {
                    targetMembers.add(adminId);
                }
            }

            if (targetMembers.isEmpty()) {
                log.warn("未找到接收者，跳过发送移除通知，群组: {}, 被移除用户: {}", groupId, userId);
                return;
            }

            // 创建群聊系统消息
            GroupChatMessageContent systemMessage = new GroupChatMessageContent();
            systemMessage.setMessageId(UUID.randomUUID().toString());
            systemMessage.setFromId(userId);
            systemMessage.setGroupId(groupId);
            systemMessage.setMessageBody(messageContent);
            systemMessage.setMessageTime(System.currentTimeMillis());
            systemMessage.setAppId(appId);
            systemMessage.setClientType(0);
            systemMessage.setImei("system");

            // 设置消息类型为移除系统消息
            systemMessage.setExtra("83");

            // 设置发送给当事人、群主和管理员
            systemMessage.setMemberId(targetMembers);

            // 使用系统消息专用方法发送（绕过权限验证）
            groupMessageService.processSystemMessage(systemMessage);

            log.info("群成员移除系统消息发送成功，群组: {}, 被移除用户: {}, 昵称: {}, 接收者数量: {}",
                    groupId, userId, userNickname, targetMembers.size());

        } catch (Exception e) {
            log.error("发送群成员移除系统消息失败，群组: {}, 被移除用户: {}, 错误: {}",
                    groupId, userId, e.getMessage(), e);
            // 系统消息发送失败不影响移除的主流程，只记录错误日志
        }
    }

    /**
     * 获取群主和管理员ID列表
     * @param groupId 群组ID
     * @param appId 应用ID
     * @return 群主和管理员ID列表
     */
    private List<String> getGroupOwnerAndManagerIds(String groupId, Integer appId) {
        List<String> adminIds = new ArrayList<>();
        try {
            List<GroupMemberDto> managers = getGroupManager(groupId, appId);
            if (managers != null && !managers.isEmpty()) {
                for (GroupMemberDto manager : managers) {
                    adminIds.add(manager.getMemberId());
                }
            }
            log.debug("获取群主和管理员列表成功，群组: {}, 管理员数量: {}", groupId, adminIds.size());
        } catch (Exception e) {
            log.error("获取群主和管理员列表失败，群组: {}, 错误: {}", groupId, e.getMessage(), e);
        }
        return adminIds;
    }

    /**
     * 发送群成员退出系统消息给群主和管理员
     * @param groupId 群组ID
     * @param userId 退出的用户ID
     * @param appId 应用ID
     */
    private void sendMemberExitSystemMessage(String groupId, String userId, Integer appId) {
        try {
            // 获取退出用户的昵称
            String userNickname = getUserNickname(userId, appId);

            // 构建系统消息内容,前端处理国际化
            String messageContent = userNickname;

            // 获取群主和管理员ID列表
            List<String> adminIds = getGroupOwnerAndManagerIds(groupId, appId);
            if (adminIds.isEmpty()) {
                log.warn("未找到群主和管理员，跳过发送退出通知，群组: {}, 退出用户: {}", groupId, userId);
                return;
            }

            // 创建群聊系统消息
            GroupChatMessageContent systemMessage = new GroupChatMessageContent();
            systemMessage.setMessageId(UUID.randomUUID().toString());
            systemMessage.setFromId(userId);
            systemMessage.setGroupId(groupId);
            systemMessage.setMessageBody(messageContent);
            systemMessage.setMessageTime(System.currentTimeMillis());
            systemMessage.setAppId(appId);
            systemMessage.setClientType(0);
            systemMessage.setImei("system");

            // 设置消息类型为退出系统消息
            systemMessage.setExtra("84");

            // 设置只发送给群主和管理员
            systemMessage.setMemberId(adminIds);

            // 使用系统消息专用方法发送（绕过权限验证）
            groupMessageService.processSystemMessage(systemMessage);

            log.info("群成员退出系统消息发送成功，群组: {}, 退出用户: {}, 昵称: {}, 接收者数量: {}",
                    groupId, userId, userNickname, adminIds.size());

        } catch (Exception e) {
            log.error("发送群成员退出系统消息失败，群组: {}, 退出用户: {}, 错误: {}",
                    groupId, userId, e.getMessage(), e);
            // 系统消息发送失败不影响退出的主流程，只记录错误日志
        }
    }

}
