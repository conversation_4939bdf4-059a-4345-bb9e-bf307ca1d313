package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 踢出用户请求模型
 * 
 * @description: 直播间踢人操作的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "踢出用户请求模型")
@Data
public class KickUserReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要执行踢人操作的直播间唯一标识")
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;

    @ApiModelProperty(value = "被踢用户ID", required = true, example = "user123", notes = "要被踢出直播间的用户唯一标识")
    @NotBlank(message = "被踢用户ID不能为空")
    private String userId;

    @ApiModelProperty(value = "踢人原因", example = "违反直播间规则", notes = "踢人的具体原因，可选参数")
    private String reason;

}
