package com.lld.im.service.group.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.lld.im.service.group.dao.ImGroupEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface ImGroupMapper extends BaseMapper<ImGroupEntity> {

    /**
     * @description 获取加入的群的最大seq
     * <AUTHOR>
     * @return java.lang.Long
     */
    @Select(" <script> " +
            " select COALESCE(max(sequence), 0) from im_group where app_id = #{appId} and group_id in " +
            "<foreach collection='groupId' index='index' item='id' separator=',' close=')' open='('>" +
            " #{id} " +
            "</foreach>" +
            " </script> ")
    Long getGroupMaxSeq(Collection<String> groupId, Integer appId);

    /**
     * 获取用户所有相关群组的最大序列号（包括已退出的群组）
     * @param appId 应用ID
     * @param memberId 用户ID
     * @return 最大序列号
     */
    @Select("SELECT COALESCE(MAX(g.sequence), 0) FROM im_group g " +
            "INNER JOIN im_group_member gm ON g.group_id = gm.group_id AND g.app_id = gm.app_id " +
            "WHERE g.app_id = #{appId} AND gm.member_id = #{memberId}")
    Long getUserGroupMaxSeq(@Param("appId") Integer appId, @Param("memberId") String memberId);

    /**
     * 获取指定群组的当前成员数量
     * @param groupId 群组ID
     * @param appId 应用ID
     * @return 成员数量
     */
    @Select("SELECT COUNT(*) FROM im_group_member WHERE group_id = #{groupId} AND app_id = #{appId} AND role in(0,1,2)")
    Integer getGroupMemberCount(@Param("groupId") String groupId, @Param("appId") Integer appId);
}
