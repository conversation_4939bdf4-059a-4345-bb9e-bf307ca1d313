package com.lld.im.service.liveroom.sharding;

import org.apache.commons.lang3.StringUtils;

/**
 * 直播间分片工具类
 * 提供不同的分片策略
 */
public class LiveRoomShardingUtil {

    /**
     * 基于直播间ID的一致性哈希分片
     * 
     * @param roomId 直播间ID
     * @param shardCount 分片数量
     * @return 分片索引
     */
    public static int getShardIdByRoomId(String roomId, int shardCount) {
        if (StringUtils.isBlank(roomId) || shardCount <= 0) {
            return 0;
        }
        
        // 使用直播间ID的哈希值对分片数取模
        return Math.abs(roomId.hashCode() % shardCount);
    }
    
    /**
     * 基于直播间热度的动态分片策略
     * 热门直播间可以分配到专门的队列
     * 
     * @param roomId 直播间ID
     * @param onlineCount 在线人数
     * @param shardCount 分片数量
     * @return 分片索引
     */
    public static int getShardIdByHeat(String roomId, int onlineCount, int shardCount) {
        if (StringUtils.isBlank(roomId) || shardCount <= 0) {
            return 0;
        }
        
        // 热门直播间（在线人数超过1000）分配到专门的分片
        if (onlineCount > 1000) {
            // 将热门直播间平均分配到前20%的分片
            int hotShardCount = Math.max(1, shardCount / 5);
            return Math.abs(roomId.hashCode() % hotShardCount);
        } else {
            // 普通直播间分配到剩余分片
            int normalShardStart = Math.max(1, shardCount / 5);
            int normalShardCount = shardCount - normalShardStart;
            return normalShardStart + Math.abs(roomId.hashCode() % normalShardCount);
        }
    }
    
    /**
     * 基于直播间ID的范围分片策略
     * 
     * @param roomId 直播间ID（假设是数字字符串）
     * @param shardCount 分片数量
     * @return 分片索引
     */
    public static int getShardIdByRange(String roomId, int shardCount) {
        if (StringUtils.isBlank(roomId) || shardCount <= 0) {
            return 0;
        }
        
        try {
            // 尝试将roomId解析为长整型
            long roomIdLong = Long.parseLong(roomId);
            // 根据ID范围分配到不同分片
            return (int) (roomIdLong % shardCount);
        } catch (NumberFormatException e) {
            // 如果roomId不是数字，则使用哈希取模
            return Math.abs(roomId.hashCode() % shardCount);
        }
    }
} 