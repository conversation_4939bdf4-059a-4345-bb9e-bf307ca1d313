package com.lld.im.service.system.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.lld.im.service.system.model.entity.ImSystemNotificationEntity;
import com.lld.im.service.system.dao.mapper.ImSystemNotificationMapper;
import com.lld.im.service.system.dao.mapper.ImUserSystemNotificationMapper;
import com.lld.im.service.system.model.entity.ImUserSystemNotificationEntity;
import com.lld.im.service.utils.RedisZSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 通知数据清理服务
 * 负责清理过期的通知数据，防止Redis和数据库数据无限增长
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class NotificationCleanupService {

    @Autowired
    private RedisZSetService redisZSetService;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private ImSystemNotificationMapper systemNotificationMapper;
    
    @Autowired
    private ImUserSystemNotificationMapper userNotificationMapper;
    
    /**
     * 定时清理过期通知数据
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredNotifications() {
        log.info("开始执行通知数据清理任务");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 1. 清理过期的系统通知
            int cleanedNotifications = cleanupExpiredSystemNotifications();
            
            // 2. 清理过期的用户通知关系
            int cleanedUserRelations = cleanupExpiredUserNotificationRelations();

            long endTime = System.currentTimeMillis();
            log.info("通知数据清理任务完成，清理通知: {}条, 清理用户关系: {}条, 耗时: {}ms", 
                cleanedNotifications, cleanedUserRelations, (endTime - startTime));
                
        } catch (Exception e) {
            log.error("通知数据清理任务执行失败", e);
        }
    }
    
    /**
     * 清理过期的系统通知
     * @return 清理的通知数量
     */
    private int cleanupExpiredSystemNotifications() {
        try {
            // 删除30天前的通知
            long expireTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(30);
            
            QueryWrapper<ImSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lt("create_time", expireTime);
            
            List<ImSystemNotificationEntity> expiredNotifications = systemNotificationMapper.selectList(queryWrapper);
            
            if (!expiredNotifications.isEmpty()) {
                // 删除过期通知
                systemNotificationMapper.delete(queryWrapper);
                
                // 从全局通知池中移除
                for (ImSystemNotificationEntity notification : expiredNotifications) {
                    removeNotificationFromGlobalPool(notification);
                }
                
                log.info("清理过期系统通知: {}条", expiredNotifications.size());
                return expiredNotifications.size();
            }
            
        } catch (Exception e) {
            log.error("清理过期系统通知失败", e);
        }
        
        return 0;
    }
    
    /**
     * 清理过期的用户通知关系
     * @return 清理的关系数量
     */
    private int cleanupExpiredUserNotificationRelations() {
        try {
            // 删除30天前的用户通知关系
            long expireTime = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(30);
            
            QueryWrapper<ImUserSystemNotificationEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.lt("create_time", expireTime);
            
            Integer count = userNotificationMapper.selectCount(queryWrapper);
            if (count != null && count > 0) {
                userNotificationMapper.delete(queryWrapper);
                log.info("清理过期用户通知关系: {}条", count);
                return count;
            }
            
        } catch (Exception e) {
            log.error("清理过期用户通知关系失败", e);
        }
        
        return 0;
    }
    

    
    /**
     * 从全局通知池中移除通知
     */
    private void removeNotificationFromGlobalPool(ImSystemNotificationEntity notification) {
        try {
            String globalPoolKey = notification.getAppId() + ":globalNotificationPool";
            String notificationJson = JSONObject.toJSONString(notification);
            
            redisZSetService.remove(globalPoolKey, notificationJson);
            
            // 删除通知元数据
            String metaKey = notification.getAppId() + ":notificationMeta:" + notification.getNotificationId();
            redisTemplate.delete(metaKey);
            
        } catch (Exception e) {
            log.error("从全局通知池移除通知失败，ID: {}", notification.getNotificationId(), e);
        }
    }
    

    

    

}
