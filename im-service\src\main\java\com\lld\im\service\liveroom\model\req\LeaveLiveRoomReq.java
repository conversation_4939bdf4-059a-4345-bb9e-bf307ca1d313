package com.lld.im.service.liveroom.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 离开直播间请求模型
 * 
 * @description: 用户离开直播间操作的请求参数模型
 * @author: IM System
 * @version: 1.0
 */
@ApiModel(description = "离开直播间请求模型")
@Data
public class LeaveLiveRoomReq extends RequestBase {

    @ApiModelProperty(value = "直播间ID", required = true, example = "room123", notes = "要离开的直播间唯一标识")
    @NotBlank(message = "直播间ID不能为空")
    private String roomId;

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "离开直播间的用户唯一标识")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

}
