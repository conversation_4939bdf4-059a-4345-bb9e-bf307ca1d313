package com.lld.im.common.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@ApiModel(description = "公共请求模型")
@Data
public class RequestBase {
    @ApiModelProperty(value = "应用ID", required = true, example = "10000")
    private Integer appId;
    @ApiModelProperty(value = "操作人", required = true, example = "dll")
    private String operater;

    @ApiModelProperty(value = "客户端类型(0-webApi 1-web 2-ios 3-android 4-windows 5-mac)", required = true, example = "1" , notes = "")
    private Integer clientType;
    @ApiModelProperty(value = "设备标识", required = true, example = "web")
    private String imei;
}
