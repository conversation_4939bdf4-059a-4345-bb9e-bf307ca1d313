package com.lld.im.service.config;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Locale;

/**
 * 语言环境拦截器
 * 
 * @description: 根据HTTP请求头Accept-Language设置当前线程的语言环境
 * @author: IM System
 * @version: 1.0
 */
public class LocaleInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(LocaleInterceptor.class);

    /**
     * 在请求处理之前执行，设置语言环境
     * 
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @return 是否继续处理请求
     */
    @Override
    public boolean preHandle(HttpServletRequest request, 
                           HttpServletResponse response, 
                           Object handler) throws Exception {
        
        // 从请求头获取Accept-Language
        String acceptLanguage = request.getHeader("Accept-Language");
        
        if (StringUtils.isNotBlank(acceptLanguage)) {
            try {
                // 解析语言环境
                Locale locale = parseLocale(acceptLanguage);
                
                // 设置当前线程的语言环境
                LocaleContextHolder.setLocale(locale);
                
                logger.debug("设置语言环境: {} (原始: {})", locale, acceptLanguage);
                
            } catch (Exception e) {
                logger.warn("解析Accept-Language失败，使用默认语言环境: {}", acceptLanguage, e);
                // 设置默认语言环境
                LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
            }
        } else {
            // 如果没有Accept-Language头，使用默认语言环境
            LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
            logger.debug("未找到Accept-Language头，使用默认语言环境: zh-CN");
        }
        
        return true;
    }

    /**
     * 请求处理完成后清理语言环境
     * 
     * @param request  HTTP请求
     * @param response HTTP响应
     * @param handler  处理器
     * @param ex       异常信息
     */
    @Override
    public void afterCompletion(HttpServletRequest request, 
                              HttpServletResponse response, 
                              Object handler, 
                              Exception ex) throws Exception {
        // 清理当前线程的语言环境，避免内存泄漏
        LocaleContextHolder.resetLocaleContext();
    }

    /**
     * 解析Accept-Language头，返回最匹配的语言环境
     *
     * @param acceptLanguage Accept-Language头的值
     * @return 解析后的语言环境
     */
    private Locale parseLocale(String acceptLanguage) {
        // 处理常见的Accept-Language格式
        // 例如: "zh-CN,zh;q=0.9,en;q=0.8,vi-VI;q=0.7"

        String[] languages = acceptLanguage.split(",");

        for (String language : languages) {
            // 移除权重信息 (;q=0.9)
            String lang = language.split(";")[0].trim();

            // 匹配支持的语言（不区分大小写）
            switch (lang.toLowerCase()) {
                case "zh-cn":
                case "zh":
                    return Locale.SIMPLIFIED_CHINESE;

                case "en":
                case "en-us":
                case "en-gb":
                    return Locale.ENGLISH;

                case "vi":
                case "vi-vi":  // 支持 vi-VI 格式（越南语正确格式）
                case "vi-vn":  // 也兼容 vi-VN 格式
                    return new Locale("vi", "VI");

                default:
                    // 尝试解析更复杂的语言标签
                    if (lang.startsWith("zh")) {
                        return Locale.SIMPLIFIED_CHINESE;
                    } else if (lang.startsWith("en")) {
                        return Locale.ENGLISH;
                    } else if (lang.startsWith("vi")) {
                        return new Locale("vi", "VI");
                    }
                    break;
            }
        }

        // 如果没有匹配的语言，返回默认语言环境
        return Locale.SIMPLIFIED_CHINESE;
    }
}
