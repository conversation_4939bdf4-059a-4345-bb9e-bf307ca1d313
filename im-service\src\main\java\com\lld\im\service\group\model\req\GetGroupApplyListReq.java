package com.lld.im.service.group.model.req;

import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Min;

/**
 * 查询群申请列表请求
 */
@ApiModel(description = "查询群申请列表请求模型")
@Data
public class GetGroupApplyListReq extends RequestBase {

    @ApiModelProperty(value = "群组ID", required = true, example = "group123", notes = "要查询申请的群组唯一标识")
    @NotBlank(message = "群组ID不能为空")
    private String groupId;

    @ApiModelProperty(value = "申请状态", example = "0", notes = "申请状态过滤：0-待审批 1-已同意 2-已拒绝 3-已撤销，不传则查询所有状态")
    private Integer applyStatus;

    @ApiModelProperty(value = "页码", example = "1", notes = "分页查询的页码，从1开始")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "每页大小", example = "20", notes = "分页查询的每页记录数，默认20，最大100")
    @Min(value = 1, message = "每页大小必须大于0")
    private Integer pageSize = 20;

    @ApiModelProperty(value = "开始时间", example = "1672531200000", notes = "查询申请时间的开始时间戳")
    private Long startTime;

    @ApiModelProperty(value = "结束时间", example = "1672617600000", notes = "查询申请时间的结束时间戳")
    private Long endTime;
}
