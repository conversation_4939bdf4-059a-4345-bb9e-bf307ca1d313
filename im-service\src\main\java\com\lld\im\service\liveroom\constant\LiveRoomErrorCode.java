package com.lld.im.service.liveroom.constant;

import com.lld.im.common.exception.ApplicationExceptionEnum;

/**
 * 直播间错误码枚举类
 */
public enum LiveRoomErrorCode implements ApplicationExceptionEnum {

    /**
     * 参数错误
     */
    PARAM_ERROR(30001, "liveroom.param.error"),

    /**
     * 直播间不存在
     */
    ROOM_NOT_EXIST(30002, "liveroom.not.exist"),

    /**
     * 用户不在直播间中
     */
    USER_NOT_IN_ROOM(30003, "liveroom.user.not.in.room"),

    /**
     * 直播间全员禁言
     */
    ROOM_MUTED(30004, "liveroom.room.muted"),

    /**
     * 用户被禁言
     */
    USER_MUTED(30005, "liveroom.user.muted"),

    /**
     * 没有权限
     */
    NO_PERMISSION(30006, "liveroom.no.permission"),

    /**
     * 操作失败
     */
    OPERATION_FAILED(30007, "liveroom.operation.failed"),

    /**
     * 游客不能执行此操作
     */
    GUEST_OPERATION_NOT_ALLOWED(30008, "liveroom.guest.operation.not.allowed"),

    /**
     * 直播间已满
     */
    ROOM_FULL(30009, "liveroom.room.full"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST(30010, "liveroom.user.not.exist"),

    /**
     * 直播间ID列表不能为空
     */
    ROOM_IDS_EMPTY(30011, "liveroom.room.ids.empty"),

    /**
     * 单次查询的直播间数量不能超过100个
     */
    ROOM_IDS_LIMIT_EXCEEDED(30012, "liveroom.room.ids.limit.exceeded"),

    /**
     * 有效的直播间ID列表不能为空
     */
    VALID_ROOM_IDS_EMPTY(30013, "liveroom.valid.room.ids.empty"),

    /**
     * 未找到指定的直播间
     */
    ROOMS_NOT_FOUND(30014, "liveroom.rooms.not.found"),

    /**
     * 获取最近消息失败
     */
    GET_RECENT_MESSAGES_FAILED(30015, "liveroom.get.recent.messages.failed"),

    /**
     * 获取在线用户列表失败
     */
    GET_ONLINE_USERS_FAILED(30016, "liveroom.get.online.users.failed"),

    /**
     * 批量检查直播间状态失败
     */
    BATCH_CHECK_FAILED(30017, "liveroom.batch.check.failed"),

    /**
     * 无法对该用户执行禁言操作
     */
    CANNOT_MUTE_HIGHER_ROLE(30018, "liveroom.cannot.mute.higher.role"),

    /**
     * 无法踢出同级或更高级别的用户
     */
    CANNOT_KICK_HIGHER_ROLE(30019, "liveroom.cannot.kick.higher.role");

    private int code;
    private String messageKey;

    LiveRoomErrorCode(int code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @Override
    public String getError() {
        // 使用国际化消息工具获取错误信息
        try {
            // 尝试获取MessageUtils实例
            Class<?> messageUtilsClass = Class.forName("com.lld.im.service.utils.MessageUtils");
            Object instance = messageUtilsClass.getMethod("getInstance").invoke(null);
            if (instance != null) {
                return (String) messageUtilsClass.getMethod("getMessage", String.class, Object[].class)
                    .invoke(instance, "error." + this.messageKey, new Object[0]);
            }
        } catch (Exception e) {
            // 如果获取失败，返回消息键（向后兼容）
        }

        // 向后兼容：如果无法获取国际化消息，返回原始错误信息
        switch (this) {
            case PARAM_ERROR:
                return "参数错误";
            case ROOM_NOT_EXIST:
                return "直播间不存在";
            case USER_NOT_IN_ROOM:
                return "用户不在直播间中";
            case ROOM_MUTED:
                return "直播间已开启全员禁言";
            case USER_MUTED:
                return "您已被禁言";
            case NO_PERMISSION:
                return "没有权限执行此操作";
            case OPERATION_FAILED:
                return "操作失败";
            case GUEST_OPERATION_NOT_ALLOWED:
                return "游客不能执行此操作";
            case ROOM_FULL:
                return "直播间已满";
            case USER_NOT_EXIST:
                return "用户不存在";
            default:
                return this.messageKey;
        }
    }
} 