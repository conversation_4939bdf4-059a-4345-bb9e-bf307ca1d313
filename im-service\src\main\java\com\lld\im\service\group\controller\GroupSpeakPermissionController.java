package com.lld.im.service.group.controller;

import com.lld.im.common.ResponseVO;
import com.lld.im.service.controller.BaseController;
import com.lld.im.service.group.model.req.SetGroupSpeakPermissionReq;
import com.lld.im.service.group.model.resp.GetGroupSpeakPermissionResp;
import com.lld.im.service.group.service.ImGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 群组发言权限管理控制器
 * @author: Eric
 * @description: 提供群组发言权限的设置和查询接口
 */
@Api(tags = "群组发言权限管理")
@RestController
@RequestMapping("/v1/group/speak-permission")
@Validated
public class GroupSpeakPermissionController extends BaseController {

    @Autowired
    private ImGroupService imGroupService;

    /**
     * 设置群组发言权限
     */
    @ApiOperation(value = "设置群组发言权限", notes = "设置群组的发言权限配置，支持所有人发言、仅管理员发言、指定成员发言、禁止发言四种模式")
    @PostMapping("/set")
    public ResponseVO setGroupSpeakPermission(
            @ApiParam(value = "设置权限请求参数", required = true)
            @Valid @RequestBody SetGroupSpeakPermissionReq req) {
        fillCommonParams(req);
        return imGroupService.setGroupSpeakPermission(req);
    }

    /**
     * 获取群组发言权限配置
     */
    @ApiOperation(value = "获取群组发言权限配置", notes = "查询指定群组的发言权限配置信息")
    @GetMapping("/get")
    public ResponseVO<GetGroupSpeakPermissionResp> getGroupSpeakPermission(
            @ApiParam(value = "群组ID", required = true, example = "group123")
            @NotBlank(message = "群组ID不能为空") @RequestParam String groupId) {
        Integer appId = getCurrentAppId();
        return imGroupService.getGroupSpeakPermission(groupId, appId);
    }
}
