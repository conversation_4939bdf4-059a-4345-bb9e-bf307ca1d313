package com.lld.im.service.system.service;

import com.lld.im.common.model.SyncReq;
import com.lld.im.common.model.SyncResp;
import com.lld.im.service.system.model.req.SystemNotification;
import com.lld.im.service.system.model.entity.ImSystemNotificationEntity;
import com.lld.im.service.system.model.resp.NotificationDetailResp;
import com.lld.im.service.system.model.resp.NotificationTypeLatestResp;
import com.lld.im.common.ResponseVO;
import com.lld.im.service.message.model.resp.PageResult;

import java.util.List;

/**
 * 系统通知服务接口
 */
public interface SystemNotificationService {

    /**
     * 发送系统通知给所有用户（包括游客）
     * @param appId 应用ID
     * @param notification 通知内容
     */
    void sendSystemNotification(Integer appId, SystemNotification notification);
    
    /**
     * 发送系统通知给普通用户（不包括游客）
     * @param appId 应用ID
     * @param notification 通知内容
     */
    void sendSystemNotificationToNormalUsers(Integer appId, SystemNotification notification);

    
    
    /**
     * 发送系统通知给游客
     * @param appId 应用ID
     * @param notification 通知内容
     */
    void sendSystemNotificationToGuests(Integer appId, SystemNotification notification);
    
    /**
     * 获取用户未读通知列表
     */
    ResponseVO<List<ImSystemNotificationEntity>> getUnreadNotifications(String userId, Integer appId, Long lastSequence, Integer limit);

    /**
     * 标记通知已读
     */
    ResponseVO<Void> markNotificationRead(String userId, Integer appId, List<Long> notificationIds);

    /**
     * 获取未读通知数量
     */
    ResponseVO<Integer> getUnreadCount(String userId, Integer appId);

    /**
     * 获取用户所有通知列表（包含已读和未读）
     * @param userId 用户ID
     * @param appId 应用ID
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页通知列表
     */
    ResponseVO<PageResult<NotificationDetailResp>> getAllNotifications(String userId, Integer appId, Integer pageNum, Integer pageSize);

    /**
     * 获取用户按通知类型分组的最新通知
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 按通知类型分组的最新通知列表
     */
    ResponseVO<List<NotificationTypeLatestResp>> getLatestNotificationsByType(String userId, Integer appId);

    /**
     * 按通知类型分页查询用户通知列表
     * @param userId 用户ID
     * @param appId 应用ID
     * @param notificationType 通知类型
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页通知列表
     */
    ResponseVO<PageResult<NotificationDetailResp>> getNotificationsByType(String userId, Integer appId, Integer notificationType, Integer pageNum, Integer pageSize);

    /**
     * 一键全部已读 - 将用户的所有未读通知标记为已读
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 操作结果，包含影响的通知数量
     */
    ResponseVO<Integer> markAllNotificationsRead(String userId, Integer appId);

    /**
     * 分类已读 - 按通知类型批量标记已读
     * @param userId 用户ID
     * @param appId 应用ID
     * @param notificationType 通知类型
     * @return 操作结果，包含影响的通知数量
     */
    ResponseVO<Integer> markNotificationsByTypeRead(String userId, Integer appId, Integer notificationType);

    /**
     * 增量同步系统通知列表 - 与会话同步保持一致的接口设计
     * @param req 同步请求参数
     * @return 同步响应结果，包含read_status和read_time字段
     */
    ResponseVO<SyncResp<NotificationDetailResp>> syncSystemNotifications(SyncReq req);
}