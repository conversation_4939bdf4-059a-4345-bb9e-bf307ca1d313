package com.lld.im.service.system.model.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("im_system_notification")
public class ImSystemNotificationEntity {
    
    @TableId
    private Long notificationId;         // 通知ID
    
    private Integer appId;               // 应用ID
    
    private Integer notificationType;     // 通知类型
    
    private String title;                // 通知标题
    
    private String content;              // 通知内容
    
    private String extra;                // 扩展字段(JSON)
    
    private Long createTime;             // 创建时间
    
    private Integer delFlag;             // 删除标识
} 