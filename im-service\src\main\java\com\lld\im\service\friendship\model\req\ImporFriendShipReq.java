package com.lld.im.service.friendship.model.req;

import com.lld.im.common.enums.FriendShipStatusEnum;
import com.lld.im.common.model.RequestBase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 导入好友关系请求
 */
@ApiModel(description = "导入好友关系请求模型")
@Data
public class ImporFriendShipReq extends RequestBase {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123", notes = "要导入好友关系的用户ID")
    @NotBlank(message = "fromId不能为空")
    private String fromId;

    @ApiModelProperty(value = "好友列表", notes = "要导入的好友关系列表")
    private List<ImportFriendDto> friendItem;

    @ApiModel(description = "导入好友信息")
    @Data
    public static class ImportFriendDto{

        @ApiModelProperty(value = "好友用户ID", example = "user456", notes = "好友的用户ID")
        private String toId;

        @ApiModelProperty(value = "好友备注", example = "小明", notes = "对好友的备注名称")
        private String remark;

        @ApiModelProperty(value = "添加来源", example = "search", notes = "添加好友的来源")
        private String addSource;

        @ApiModelProperty(value = "好友状态", example = "1", notes = "好友关系状态，默认为正常")
        private Integer status = FriendShipStatusEnum.FRIEND_STATUS_NO_FRIEND.getCode();

        @ApiModelProperty(value = "黑名单状态", example = "0", notes = "是否在黑名单中，默认为正常")
        private Integer black = FriendShipStatusEnum.BLACK_STATUS_NORMAL.getCode();
    }

}
