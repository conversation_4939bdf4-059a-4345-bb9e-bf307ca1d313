package com.lld.im.codec.pack.message;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: lld
 * @version: 1.0
 */
@Data
public class ChatMessageAck {

    public ChatMessageAck() {
        this.messageId = messageId;
    }

    public ChatMessageAck(String messageId) {
        this.messageId = messageId;
    }
    public ChatMessageAck(String messageId,Long messageSequence) {
        this.messageId = messageId;
        this.messageSequence = messageSequence;
    }
    private String messageId;
    private Long messageSequence;
    private Long messageKey;

    private String fromId;

    private String toId;

    private String messageBody;

    private Long messageTime;

    private String extra;

    private Integer conversationType;

    private String conversationId;



}
