#!/bin/bash
# FTP上传脚本 - 最大化进度显示
# 作者: IM系统部署工具
# 版本: v1.0

# FTP服务器配置
FTP_HOST="*************"
FTP_PORT="2121"
FTP_USER="sp-dev"
FTP_PASS="UoPRndY4wU@Z3^H2wqY"
REMOTE_DIR="apps-zip"

# 获取要上传的文件（支持命令行参数）
# 获取要上传的文件（支持命令行参数和自动选择最新文件）
if [ -z "$1" ]; then
    # 查找最新的ZIP文件
    LATEST_ZIP=$(ls -t *.zip 2>/dev/null | head -n1)
    if [ -n "$LATEST_ZIP" ]; then
        LOCAL_FILE="$LATEST_ZIP"
        echo "📋 自动选择最新文件: $LOCAL_FILE"
    else
        LOCAL_FILE="202507081759.zip"
        echo "📋 使用默认文件: $LOCAL_FILE (未找到ZIP文件)"
    fi
else
    LOCAL_FILE="$1"
    echo "📋 使用指定文件: $LOCAL_FILE"
fi

# 颜色定义（如果终端支持）
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 打印分隔线
print_separator() {
    echo "========================================="
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$LOCAL_FILE" ]; then
        print_message $RED "❌ 错误: 文件 $LOCAL_FILE 不存在"
        echo ""
        print_message $YELLOW "用法: $0 [文件名]"
        print_message $YELLOW "示例: $0 202507081759.zip"
        echo ""
        print_message $CYAN "当前目录ZIP文件列表:"

        # 显示所有ZIP文件及其时间信息
        ZIP_FILES=$(ls -lt *.zip 2>/dev/null)
        if [ -n "$ZIP_FILES" ]; then
            echo "$ZIP_FILES" | while read -r line; do
                if [[ $line == total* ]]; then
                    continue
                fi
                filename=$(echo "$line" | awk '{print $9}')
                filesize=$(echo "$line" | awk '{print $5}')
                datetime=$(echo "$line" | awk '{print $6, $7, $8}')
                size_mb=$((filesize / 1024 / 1024))
                echo "  📦 $filename (${size_mb}MB, $datetime)"
            done
        else
            echo "  (没有找到ZIP文件)"
        fi
        exit 1
    fi

    # 显示选中文件的详细信息
    if [ -f "$LOCAL_FILE" ]; then
        FILE_MTIME=$(stat -c %Y "$LOCAL_FILE" 2>/dev/null || date -r "$LOCAL_FILE" +%s 2>/dev/null)
        if [ -n "$FILE_MTIME" ]; then
            FILE_DATE=$(date -d "@$FILE_MTIME" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || date -r "$LOCAL_FILE" "+%Y-%m-%d %H:%M:%S" 2>/dev/null)
            print_message $CYAN "📅 文件创建时间: $FILE_DATE"
        fi
    fi
}

# 获取文件大小信息
get_file_info() {
    # 尝试不同的方法获取文件大小
    if command -v stat >/dev/null 2>&1; then
        FILE_SIZE=$(stat -c%s "$LOCAL_FILE" 2>/dev/null)
    else
        FILE_SIZE=$(wc -c < "$LOCAL_FILE" 2>/dev/null)
    fi
    
    # 计算MB大小
    if [ -n "$FILE_SIZE" ] && [ "$FILE_SIZE" -gt 0 ]; then
        FILE_SIZE_MB=$((FILE_SIZE / 1024 / 1024))
        FILE_SIZE_KB=$((FILE_SIZE / 1024))
        
        if [ $FILE_SIZE_MB -gt 0 ]; then
            SIZE_DISPLAY="${FILE_SIZE_MB}MB"
        else
            SIZE_DISPLAY="${FILE_SIZE_KB}KB"
        fi
    else
        SIZE_DISPLAY="未知"
    fi
}

# 显示上传信息
show_upload_info() {
    print_separator
    print_message $CYAN "🚀 FTP 文件上传工具"
    print_separator
    print_message $BLUE "📁 文件: $LOCAL_FILE"
    print_message $BLUE "📊 大小: $SIZE_DISPLAY"
    print_message $BLUE "🌐 服务器: $FTP_HOST:$FTP_PORT"
    print_message $BLUE "👤 用户: $FTP_USER"
    print_message $BLUE "📂 目录: $REMOTE_DIR"
    print_separator
}

# 执行FTP上传
perform_upload() {
    print_message $YELLOW "🔗 连接到FTP服务器..."
    echo ""
    
    # 执行FTP命令
    ftp -v -n $FTP_HOST $FTP_PORT << EOF
user $FTP_USER $FTP_PASS
binary
hash
tick
cd $REMOTE_DIR
put $LOCAL_FILE
quit
EOF
    
    # 检查上传结果
    local exit_code=$?
    echo ""
    
    if [ $exit_code -eq 0 ]; then
        print_message $GREEN "✅ 文件上传成功！"
        print_message $GREEN "📡 远程路径: ftp://$FTP_HOST:$FTP_PORT/$REMOTE_DIR/$LOCAL_FILE"
    else
        print_message $RED "❌ 文件上传失败！"
        print_message $RED "错误代码: $exit_code"
        echo ""
        print_message $YELLOW "可能的解决方案:"
        print_message $YELLOW "1. 检查网络连接"
        print_message $YELLOW "2. 验证FTP服务器地址和端口"
        print_message $YELLOW "3. 确认用户名和密码正确"
        print_message $YELLOW "4. 检查远程目录权限"
        return $exit_code
    fi
}

# 显示帮助信息
show_help() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        print_separator
        print_message $CYAN "FTP上传脚本帮助"
        print_separator
        echo "用法: $0 [选项] [文件名]"
        echo ""
        echo "选项:"
        echo "  -h, --help    显示此帮助信息"
        echo ""
        echo "参数:"
        echo "  文件名        要上传的文件名（可选）"
        echo ""
        echo "文件选择规则:"
        echo "  - 无参数时: 自动选择当前目录最新的ZIP文件"
        echo "  - 有参数时: 使用指定的文件名"
        echo "  - 找不到文件时: 使用默认文件名 202507081759.zip"
        echo ""
        echo "示例:"
        echo "  $0                          # 自动选择最新ZIP文件"
        echo "  $0 myfile.zip              # 上传指定文件"
        echo "  $0 /path/to/file.zip       # 上传指定路径的文件"
        echo ""
        echo "配置:"
        echo "  FTP服务器: $FTP_HOST:$FTP_PORT"
        echo "  远程目录: $REMOTE_DIR"
        print_separator
        exit 0
    fi
}

# 主函数
main() {
    # 检查帮助参数
    show_help "$1"
    
    # 显示开始信息
    echo ""
    print_message $CYAN "🎯 开始FTP上传流程..."
    echo ""
    
    # 执行上传流程
    check_file
    get_file_info
    show_upload_info
    
    # 询问用户确认
    print_message $YELLOW "是否继续上传? (y/N): "
    read -r confirm
    
    if [ "$confirm" = "y" ] || [ "$confirm" = "Y" ] || [ "$confirm" = "yes" ] || [ "$confirm" = "YES" ]; then
        perform_upload
        local result=$?
        echo ""
        print_separator
        if [ $result -eq 0 ]; then
            print_message $GREEN "🎉 上传任务完成！"
        else
            print_message $RED "💥 上传任务失败！"
        fi
        print_separator
        exit $result
    else
        print_message $YELLOW "❌ 用户取消上传"
        exit 0
    fi
}

# 脚本入口点
main "$@"
