-- ----------------------------
-- Table structure for im_group_apply_request
-- ----------------------------
DROP TABLE IF EXISTS `im_group_apply_request`;
CREATE TABLE `im_group_apply_request`  (
  `apply_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID，主键',
  `app_id` int(20) NOT NULL COMMENT '应用ID',
  `group_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '群组ID',
  `applicant_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '申请人用户ID',
  `apply_status` int(10) NOT NULL DEFAULT 0 COMMENT '申请状态：0-待审批 1-已同意 2-已拒绝 3-已撤销',
  `apply_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '申请理由',
  `apply_time` bigint(20) NOT NULL COMMENT '申请时间戳',
  `approver_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '审批人用户ID（群主或管理员）',
  `approve_time` bigint(20) NULL DEFAULT NULL COMMENT '审批时间戳',
  `reject_reason` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '拒绝理由',
  `sequence` bigint(20) NULL DEFAULT NULL COMMENT '序列号，用于数据同步',
  `create_time` bigint(20) NOT NULL COMMENT '记录创建时间戳',
  `update_time` bigint(20) NULL DEFAULT NULL COMMENT '记录更新时间戳',
  `extra` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '扩展字段，JSON格式',
  PRIMARY KEY (`apply_id`) USING BTREE,
  UNIQUE INDEX `uk_app_group_applicant_status`(`app_id`, `group_id`, `applicant_id`, `apply_status`) USING BTREE COMMENT '防止重复申请的唯一索引',
  INDEX `idx_app_group_status`(`app_id`, `group_id`, `apply_status`) USING BTREE COMMENT '按群组和状态查询的索引',
  INDEX `idx_applicant_status`(`applicant_id`, `apply_status`) USING BTREE COMMENT '按申请人和状态查询的索引',
  INDEX `idx_apply_time`(`apply_time`) USING BTREE COMMENT '按申请时间查询的索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '群申请记录表' ROW_FORMAT = Dynamic;

