lim:
  tcpPort: 9000
  webSocketPort: 19000
  bossThreadSize: 1
  workThreadSize: 8
  heartBeatTime: 20000 #心跳超时时间 单位毫秒
  brokerId: 1000
  loginModel: 3
  logicUrl: http://127.0.0.1:8000/v1

  # Redis集群模式配置 - 生产环境安全配置
  redis:
    mode: cluster # 使用集群模式
    database: 0
    password: ${REDIS_PASSWORD:your_strong_redis_password_here}  # 使用环境变量
    timeout: 8000         # 生产环境超时时间
    poolMinIdle: 10
    poolConnTimeout: 8000 # 生产环境连接超时
    poolSize: 20          # 生产环境连接池大小
    
    cluster:
      nodes:
        # 生产环境Redis集群节点 - 请替换为实际IP
        - ${REDIS_NODE1_HOST:*********}:${REDIS_NODE1_PORT:7000}
        - ${REDIS_NODE2_HOST:*********}:${REDIS_NODE2_PORT:7000}
        - ${REDIS_NODE3_HOST:*********}:${REDIS_NODE3_PORT:7000}
        # 如果使用负载均衡器，可以配置VIP地址：
        # - ${REDIS_VIP_HOST:*********0}:7000
        # - ${REDIS_VIP_HOST:*********0}:7001
        # - ${REDIS_VIP_HOST:*********0}:7002
      maxRedirects: 6      # 生产环境增加重定向次数
      scanInterval: 3000   # 生产环境扫描间隔
      retryAttempts: 5     # 生产环境重试次数
      retryInterval: 2000  # 生产环境重试间隔

  # RabbitMQ集群模式配置 - 生产环境安全配置
  rabbitmq:
    addresses:
      # 生产环境RabbitMQ集群节点 - 请替换为实际IP
      - host: ${RABBITMQ_NODE1_HOST:*********}
        port: ${RABBITMQ_NODE1_PORT:5672}
      - host: ${RABBITMQ_NODE2_HOST:*********}
        port: ${RABBITMQ_NODE2_PORT:5672}
      - host: ${RABBITMQ_NODE3_HOST:*********}
        port: ${RABBITMQ_NODE3_PORT:5672}
    virtualHost: ${RABBITMQ_VHOST:/im-production}
    userName: ${RABBITMQ_USERNAME:im_user}
    password: ${RABBITMQ_PASSWORD:your_strong_rabbitmq_password_here}
    connectionTimeout: 15000      # 生产环境连接超时
    requestedHeartbeat: 120       # 生产环境心跳间隔
    networkRecoveryInterval: 15000 # 生产环境网络恢复间隔
    automaticRecoveryEnabled: true
    # 生产环境优化参数
    channelCacheSize: 100         # 生产环境通道缓存
    publisherConfirms: true       # 启用发布确认
    publisherReturns: true        # 启用发布返回
    # SSL/TLS配置（如果启用）
    # ssl:
    #   enabled: true
    #   keyStore: ${RABBITMQ_KEYSTORE_PATH:/path/to/keystore.p12}
    #   keyStorePassword: ${RABBITMQ_KEYSTORE_PASSWORD:keystore_password}
    #   trustStore: ${RABBITMQ_TRUSTSTORE_PATH:/path/to/truststore.p12}
    #   trustStorePassword: ${RABBITMQ_TRUSTSTORE_PASSWORD:truststore_password}

  # Nacos集群配置 - 生产环境
  nacosConfig:
    serverAddr: ${NACOS_SERVERS:*********:8848,*********:8848,*********:8848}
    namespace: ${NACOS_NAMESPACE:im-production}
    group: ${NACOS_GROUP:PRODUCTION_GROUP}
    username: ${NACOS_USERNAME:nacos_admin}
    password: ${NACOS_PASSWORD:your_strong_nacos_password_here}
    connectTimeout: 8000
    readTimeout: 15000

# 直播间分片配置
liveroom:
  sharding:
    enabled: true
    shardCount: 8        # 生产环境增加分片数
    strategy: roomId

# 生产环境部署说明
#
# 环境变量配置示例：
# export REDIS_PASSWORD="your_strong_redis_password_here"
# export REDIS_NODE1_HOST="*********"
# export REDIS_NODE2_HOST="*********"
# export REDIS_NODE3_HOST="*********"
# export RABBITMQ_USERNAME="im_user"
# export RABBITMQ_PASSWORD="your_strong_rabbitmq_password_here"
# export NACOS_SERVERS="*********:8848,*********:8848,*********:8848"
#
# 安全建议：
# 1. 密码策略：
#    - 使用强密码（至少16位，包含大小写字母、数字、特殊字符）
#    - 定期轮换密码
#    - 不同服务使用不同密码
#
# 2. 网络安全：
#    - 使用VPN或专线连接
#    - 配置防火墙规则，只允许必要的端口访问
#    - 启用SSL/TLS加密传输
#
# 3. 访问控制：
#    - 创建专用用户，避免使用默认用户
#    - 配置最小权限原则
#    - 启用访问日志和审计
#
# 4. 监控告警：
#    - 监控连接数、延迟、错误率
#    - 配置异常告警
#    - 定期检查安全日志
#
# 5. 备份策略：
#    - 定期备份Redis数据
#    - 备份RabbitMQ配置和消息
#    - 测试恢复流程
