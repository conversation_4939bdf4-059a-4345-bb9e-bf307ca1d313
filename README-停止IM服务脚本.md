# IM服务终止脚本使用说明

本目录包含两个PowerShell脚本，用于终止IM系统的三个核心服务进程：
- `im-tcp` - TCP连接服务
- `im-service` - 核心业务服务  
- `im-message-store` - 消息存储服务

## 脚本文件

### 1. stop-im-services.ps1 (完整版)
功能丰富的终止脚本，支持多种选项和详细日志记录。

**参数说明：**
- `-Force`: 强制终止，不询问用户确认
- `-Verbose`: 详细输出模式，会生成日志文件
- `-WaitForExit`: 等待进程正常退出
- `-TimeoutSeconds`: 等待超时时间（默认30秒）

**使用示例：**
```powershell
# 基本使用（会询问确认）
.\stop-im-services.ps1

# 强制终止，不询问确认
.\stop-im-services.ps1 -Force

# 详细模式，生成日志文件
.\stop-im-services.ps1 -Verbose

# 等待进程优雅退出，超时时间60秒
.\stop-im-services.ps1 -WaitForExit -TimeoutSeconds 60

# 组合使用
.\stop-im-services.ps1 -Force -Verbose -WaitForExit
```

### 2. stop-im-en.ps1 (简化版 - 推荐)
轻量级的快速终止脚本，适合日常使用。使用英文界面避免编码问题。

**参数说明：**
- `-Force`: 强制终止，不询问用户确认

**使用示例：**
```powershell
# 基本使用（会询问确认）
.\stop-im-en.ps1

# 强制终止，不询问确认
.\stop-im-en.ps1 -Force
```

### 3. stop-im-services.bat (批处理文件)
提供图形化菜单界面，方便不熟悉PowerShell的用户使用。

**使用方法：**
- 双击运行批处理文件
- 根据菜单选择相应的操作

## 工作原理

脚本通过以下步骤工作：

1. **进程发现**: 扫描所有Java进程，通过命令行参数识别IM服务
2. **服务识别**: 根据jar包名称或命令行参数匹配以下模式：
   - `im-tcp` 或 `tcp-*.jar` 或 `im-tcp*.jar`
   - `im-service` 或 `im-service*.jar`
   - `im-message-store` 或 `im-message-store*.jar`
3. **进程终止**: 
   - 完整版：支持优雅停止（发送关闭信号）和强制终止
   - 简化版：直接强制终止进程
4. **结果反馈**: 显示终止结果和统计信息

## 注意事项

### 权限要求
- 需要管理员权限来终止某些系统进程
- 建议以管理员身份运行PowerShell

### 执行策略
如果遇到执行策略限制，可以使用以下命令：
```powershell
# 临时允许执行脚本
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 或者直接运行
PowerShell -ExecutionPolicy Bypass -File .\stop-im-services.ps1
```

### 安全提醒
- 脚本会终止正在运行的服务，可能导致数据丢失
- 建议在终止服务前确保没有重要操作正在进行
- 生产环境使用前请先在测试环境验证

## 故障排除

### 常见问题

**1. 未发现IM服务进程**
- 检查服务是否真的在运行
- 确认服务启动时使用的jar包名称是否匹配脚本中的模式

**2. 权限不足**
- 以管理员身份运行PowerShell
- 检查当前用户是否有终止目标进程的权限

**3. 进程终止失败**
- 某些进程可能被系统保护，无法终止
- 尝试使用 `-Force` 参数
- 检查进程是否被其他程序锁定

**4. 脚本执行被阻止**
- 修改PowerShell执行策略
- 使用 `Unblock-File` 命令解除文件阻止

### 手动终止方法
如果脚本无法正常工作，可以手动终止：

```powershell
# 查看Java进程
Get-Process java

# 根据PID终止特定进程
Stop-Process -Id <PID> -Force

# 或者使用taskkill命令
taskkill /PID <PID> /F
```

## 日志文件

使用 `-Verbose` 参数时，完整版脚本会生成 `stop-im-services.log` 日志文件，包含：
- 执行时间戳
- 发现的进程信息
- 终止操作详情
- 错误信息（如有）

## 相关文件

- `stop-im-services.ps1` - 完整功能版本
- `stop-im-en.ps1` - 简化版本（推荐使用）
- `stop-im-services.bat` - 批处理菜单文件
- `stop-im-services.log` - 日志文件（使用-Verbose时生成）
- `README-停止IM服务脚本.md` - 本说明文件

## 版本信息

- 版本: 1.0
- 作者: IM System
- 创建日期: 2025-08-04
- 兼容系统: Windows 10/11 + PowerShell 5.0+
