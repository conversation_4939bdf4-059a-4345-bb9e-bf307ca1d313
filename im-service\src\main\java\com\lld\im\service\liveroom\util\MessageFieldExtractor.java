package com.lld.im.service.liveroom.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * 消息字段提取工具类
 * 用于从JSON消息中提取和验证基础字段
 */
@Slf4j
public class MessageFieldExtractor {


    /**
     * 构建标准格式的消息JSON对象
     * 
     * @param command 命令
     * @param appId 应用ID
     * @param clientType 客户端类型
     * @param imei 设备标识
     * @param data 业务数据
     * @return 标准格式的JSON对象
     */
    public static JSONObject buildStandardMessage(Integer command, Integer appId, Integer clientType,
                                                 String imei, JSONObject data) {
        JSONObject message = new JSONObject();
        message.put("command", command);
        message.put("appId", appId);
        message.put("clientType", clientType != null ? clientType : 0);  // 0表示系统/服务器

        // 对于系统消息，imei可以为空或null
        if (StringUtils.isNotEmpty(imei)) {
            message.put("imei", imei);
        } else {
            message.put("imei", "");  // 系统消息使用空字符串
        }

        message.put("timestamp", System.currentTimeMillis());
        message.put("data", data != null ? data : new JSONObject());

        return message;
    }
}
