package com.lld.im.common.enums.command;

public enum LiveRoomCommand implements Command {

    // 直播间消息 5010
    LIVE_ROOM_MSG(5010),

    // 直播间消息ACK 5011
    LIVE_ROOM_MSG_ACK(5011),
    
    // 加入直播间 5012
    LIVE_ROOM_JOIN(5012),
    
    // 离开直播间 5013
    LIVE_ROOM_LEAVE(5013),
    
    // 直播间禁言 5014
    LIVE_ROOM_MUTE(5014),
    
    // 直播间踢人 5015
    LIVE_ROOM_KICK(5015),
    
    // 直播间公告 5016
    LIVE_ROOM_ANNOUNCEMENT(5016),
    
    // 直播间全员禁言 5017
    LIVE_ROOM_MUTE_ALL(5017),

    // 直播间礼物 5018
    LIVE_ROOM_LIKE(5018),

    // 直播间礼物 5019
    LIVE_ROOM_GIFT(5019),

    // 创建直播间 5020
    LIVE_ROOM_CREATE(5020),

    // 关闭直播间 5021
    LIVE_ROOM_CLOSE(5021),

    // 拉取直播间最近消息 5022
    LIVE_ROOM_GET_MESSAGES(5022);

    private int command;

    LiveRoomCommand(int command) {
        this.command = command;
    }

    @Override
    public int getCommand() {
        return command;
    }
} 