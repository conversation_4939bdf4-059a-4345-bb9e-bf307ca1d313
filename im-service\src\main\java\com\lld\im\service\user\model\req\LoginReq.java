package com.lld.im.service.user.model.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @description: 用户登录请求模型
 **/
@ApiModel(description = "用户登录请求模型")
@Data
public class LoginReq {

    @ApiModelProperty(value = "用户ID", required = true, example = "user123")
    @NotNull(message = "{validation.user.id.not.blank}")
    private String userId;

    @ApiModelProperty(value = "应用ID", required = true, example = "10000")
    @NotNull(message = "{validation.app.id.not.null}")
    private Integer appId;

    
    @ApiModelProperty(value = "客户端类型", example = "1", notes = "0-webApi 1-web 2-ios 3-android 4-windows 5-mac")
    private Integer clientType;

}
