package com.lld.im.common.enums;

public enum LiveRoomUserRole {
    
    ANCHOR(1, "主播"),
    ADMIN(2, "管理员"),
    VIP(3, "VIP用户"),
    NORMAL(4, "普通用户"),
    GUEST(5, "游客");

    private int code;
    private String desc;

    LiveRoomUserRole(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
    
    public static LiveRoomUserRole getByCode(int code) {
        for (LiveRoomUserRole role : values()) {
            if (role.getCode() == code) {
                return role;
            }
        }
        return GUEST;
    }
} 